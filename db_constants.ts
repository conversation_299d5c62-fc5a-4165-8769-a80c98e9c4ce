export const insertUserQuery = `INSERT INTO public.users(
	 password_hash, mobile_number, email,client_id,login_method)
	VALUES ($1,$2,$3,$4,$5)
    RETURNING *;`;

export const checkUserNameQuery = `select 1 from users where user_name = $1`;

export const checkUserEmailQuery = `select 1 from users where email = $1`;

export const checkUserMobQuery = `select 1 from users where mobile_number = $1`;

export const getOtpQuery = `select * from otp_history where destination = $1 and type = $2 and is_verified = false and created::date = now()::date;`;

export const add_otp = `INSERT INTO OTP_HISTORY(DESTINATION,OTP,type)
VALUES($1,$2,$3) ON CONFLICT ON CONSTRAINT UNQ_OTP DO
UPDATE
SET OTP = $2,
	ATTEMPT = 0,
	resend_attempt = 0,
	created = now(),
	IS_VERIFIED = FALSE,
	UPDATED = NOW();`;

export const otp_attempt = `update otp_history
	set attempt = attempt + $3,
		resend_attempt = resend_attempt + $4,
		updated = now()
	where (destination = $1) and (type = $2) and (is_verified = false)
	returning *`;

export const otp_verified = `update otp_history
	set is_verified = true,
	updated = now()
	where (destination = $1) and (type = $2) and (is_verified = false)
	returning *`;

export const verify_email_query = `update users
	set email_verified = true,
	updated = now()
	where (email = $1)
	returning *`;

export const verify_mobile_query = `update users
	set mobile_verified = true,
	updated = now()
	where (mobile_number = $1)
	returning *`;

export const checkUserEmailMobileQuery = `select * from users where (email = $1 or $1 is null) and (mobile_number = $2 or $2 is null);`;

export const userByCredQuery = `select * from users where (email = $1 or mobile_number = $2 or user_name = $3) and (user_type = $4);`;

export const getUserByIDQuery = `select * from users where id = $1;`;

export const updatePasswordQuery = `update users
set password_hash = $1,
updated = now()
where email = $2 or mobile_number = $2;`;

export const upsertUserQuery = `INSERT INTO public.users(
	password_hash, mobile_number, email,
	client_id,login_method, email_verified,
	display_name,display_pic,uid)
   VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ON CONFLICT ON CONSTRAINT unq_usr_email
   DO UPDATE
   SET login_method = $5,
   email_verified = $6,
   display_name = $7,
   display_pic = $8
   RETURNING *;`;

export const getUserbyUID = `select * from users where uid = $1;`;
export const getUserbyemail = `select * from users where email = $1;`;
// export const getUserbymobile = `select * from users where mobile_number = $1;`;

export const transactionBegin = "begin";
export const transactionCommit = "commit";
export const transactionRollback = "rollback";

export const orderInvoiceQuery = `WITH orders_list AS (
	SELECT
		od.id, od.user_id, TO_CHAR(od.created, 'Mon DD, YYYY') date,
		pm.name "paymentMethod", pcc.symbol symbol,
		COALESCE( oi.price_per_quantity, pi.price ) price,
		oi.quantity, pi.image_url, 
		CASE 
			WHEN pi.product_type IN (2, 3) THEN p.name ||
				COALESCE(' - ' || STRING_AGG( vo.value,', ' ORDER BY vo.variation_id),'') ||
				COALESCE(' - ' || p.gst_code,'')
			ELSE p.name ||
				COALESCE(' - ' || STRING_AGG( vo.value,', ' ORDER BY vo.variation_id),'') ||
				COALESCE(' - ' || p.gst_code,'')
		END "name",
		COALESCE(
			(oi.base_price)::numeric(12,2),
			(pi.price)::numeric(12,2)
		) orginal_base_price,
		COALESCE(
			(oi.base_price/ (1 + (pp.gst::numeric / 100)))::numeric(12,2),
			(pi.price/ (1 + (pp.gst::numeric / 100)))::numeric(12,2)
		) original_price_notax,
		(
			CASE
			WHEN (pcs.id IS NOT NULL) and (cc.id IS NOT NULL) and (cc.all_products = false)
			THEN (
					 (
						COALESCE(
							(oi.base_price/ (1 + (pp.gst::numeric / 100)))::numeric(12,2),
							(pi.price/ (1 + (pp.gst::numeric / 100)))::numeric(12,2)
						)
						* ( cc.discount  / 100 )
					)::numeric(12,2)
				)
			WHEN (cc.id IS NOT NULL) and (cc.all_products = true)
			THEN (
					 (
						COALESCE(
							(oi.base_price/ (1 + (pp.gst::numeric / 100)))::numeric(12,2),
							(pi.price/ (1 + (pp.gst::numeric / 100)))::numeric(12,2)
						)
						* ( cc.discount  / 100 )
					)::numeric(12,2)
				)
			ELSE 0
			END
		) discounted,
		cc.code coupon_code, pp.gst,
		cc.discount,
		od.purchased_price,
		od.address_id
	FROM order_details od
	INNER JOIN order_items oi on oi.order_id = od.id
	INNER JOIN product_item pi on pi.id = oi.product_item_id
	INNER JOIN products p 
  ON (
    (pi.product_type != 2 AND p.id = pi.product_id)
    OR
    (pi.product_type = 2 AND p.id = pi.course_product_id)
  )
	INNER JOIN payment_method pm on pm.id = od.payment_method
	INNER JOIN payment_currency pcc on pcc.id = od.payment_currency
	INNER JOIN product_prices pp on pp.product_id = p.id and pp.currency = od.payment_currency
	LEFT JOIN coupon_codes cc on cc.id = od.offer_id
	left join product_coupons pcs on pcs.product_id = p.id and pcs.coupon_id = cc.id
	LEFT JOIN product_configuration pc on pc.product_item_id = pi.id
	LEFT JOIN variation_option vo on vo.id = pc.variation_option_id
	WHERE od.payment_ref = $1
	GROUP BY od.id, od.user_id, pi.id, oi.id,
	p.id, pm.name, pcc.symbol, pp.gst, cc.id, pcs.id, od.address_id, pi.product_type, p.name
)
SELECT
	id, user_id, date, "paymentMethod", symbol,
	purchased_price as total,
	address_id,
	ARRAY_AGG(
		JSONB_BUILD_OBJECT(
			'name', name,
			'price', original_price_notax,
			'gst', ((original_price_notax -  discounted) * (gst / 100))::numeric(12,2),
			'qty', quantity,
			'total', CASE
					WHEN "paymentMethod" = 'Paypal'
					THEN ((original_price_notax - discounted) * 1.1)::numeric(12,2) * quantity
					ELSE (original_price_notax - discounted) + ((original_price_notax - discounted) * (gst / 100))::numeric(12,2) * quantity
					END,
	'symbol', symbol,
	'image',image_url,
	'discounted',discounted
	)
	) products,
	SUM(discounted) discounted,
	coupon_code
FROM orders_list
GROUP BY id, user_id, date, "paymentMethod",
	symbol, coupon_code, purchased_price, address_id;`;

export const userAddressinvoiceQuery = `select ua.first_name "firstName",ua.last_name "lastName",ua.email,ua.phone "mobile_number",JSONB_BUILD_OBJECT(
    'address_line_one',ua.address_line_one,
    'address_line_two',ua.address_line_two,
    'address_line_three',ua.address_line_three,
    'city',ua.city,
    'state',ua.state,
    'country',ua.country,
    'pincode',ua.pincode) address from order_details od
    inner join user_address ua on ua.id = od.address_id
    where od.payment_ref = $1`;

// need to change this, to make the deletion proper from the cart
export const deleteCartItemsByArtistIdQuery = `
DELETE FROM shopping_cart_items 
WHERE cart_id IN (
  SELECT id FROM shopping_cart 
  WHERE (user_id, artist_id) IN (
    SELECT od.user_id,
           COALESCE(p.artist_id, alt_artist.artist_id) AS artist_id
    FROM order_details od
    INNER JOIN order_items oi ON oi.order_id = od.id
    INNER JOIN product_item pi ON pi.id = oi.product_item_id

    LEFT JOIN products p 
  	ON (
  	  (pi.product_type != 2 AND p.id = pi.product_id)
  	  OR
  	  (pi.product_type = 2 AND p.id = pi.course_product_id)
  	)

    LEFT JOIN LATERAL (
      SELECT prod.artist_id
      FROM products prod
      INNER JOIN product_item pi2 ON pi2.product_id = prod.id
      WHERE pi.product_type = 2
        AND pi2.image_url = pi.image_url
        AND pi2.max_price = pi.max_price
      LIMIT 1
    ) alt_artist ON true

    WHERE od.payment_ref = $1
    GROUP BY od.user_id, COALESCE(p.artist_id, alt_artist.artist_id)
  )
);
`;

export const insertPgLogsQuery = `insert into pg_logs (success,code,data)
	values ($1,$2,$3);`;

export const updatePayStatusQuery = `update artist_meeting_logs
set payment_status = $1,
updated = now()
where merchant_transaction_id = $2 and payment_status in (1,2)
returning *;`;

export const getArtistMeetByArtIdQuery = `
	select 
		am.iswhitelisted,
		am.source_id,
		(
			select array_agg(sq.id) 
			from artist_meet_detail sq
			where artist_meet_id = am.id
		) book_ids,
		pp.price,
		am.gst as tax,
		amd.image,
		amd.name,
		amd.duration,
		ast.name artist_name,
		(
			SELECT json_agg(
				JSONB_BUILD_OBJECT(
				'currency', pcr.name,
				'currency_id', pcr.id,
				'price', pp.price,
				'max_price', pp.max_price,
				'gst', pp.gst)
			)
			FROM product_prices pp
			INNER JOIN payment_currency pcr ON pcr.id = pp.currency
			WHERE pp.meet_id = am.id
			GROUP BY pp.meet_id
		) currencies
	from artist_meets am
	inner join product_prices pp on pp.meet_id = am.id and pp.currency = $2
	inner join artist_meet_detail amd on amd.artist_meet_id = am.id
	inner join artist ast on am.artist_id = ast.id
	where  am.id = $1;`;

export const getArtistMeetCurrencyOptionsQuery = `
SELECT json_agg(
    JSONB_BUILD_OBJECT(
        'currency', pcr.name,
        'currency_id', pcr.id,
        'price', pp.price,
        'max_price', pp.max_price,
        'gst', pp.gst
    )
) AS currencies
FROM artist_meets am
INNER JOIN product_prices pp ON pp.meet_id = am.id
INNER JOIN payment_currency pcr ON pcr.id = pp.currency
WHERE am.id = $1
GROUP BY am.id;`;

export const getArtistMeetByArtSourceIdQuery = `
	select 
		am.iswhitelisted,
		am.source_id,
		am.id,
		(
			select array_agg(sq.id) 
			from artist_meet_detail sq
			where artist_meet_id = am.id
		) book_ids,
		pp.price,
		am.gst as tax,
		amd.image,
		amd.name,
		amd.duration,
		ast.name artist_name,
		(
			SELECT json_agg(
				JSONB_BUILD_OBJECT(
				'currency', pcr.name,
				'currency_id', pcr.id,
				'price', pp.price,
				'max_price', pp.max_price,
				'gst', pp.gst)
			)
			FROM product_prices pp
			INNER JOIN payment_currency pcr ON pcr.id = pp.currency
			WHERE pp.meet_id = am.id
			GROUP BY pp.meet_id
		) currencies
	from artist_meets am
	inner join product_prices pp on pp.meet_id = am.id and pp.currency = $2
	inner join artist_meet_detail amd on amd.artist_meet_id = am.id
	inner join artist ast on am.artist_id = ast.id
	where am.is_active = true and am.source_id = $1;`;

export const getUserByFormIdQuery = `select * from artist_meet_users where form_id = $1`;

export const updateMeetScheduledQuery = `update artist_meeting_logs
set is_meeting_scheduled = true,
invoice = $2,
updated = now()
where id = $1`;

export const getArtistBookQuery = `
	select 
	amd.*, true eligibile, null request_status, a.name artist_name,
	am.iswhitelisted,
	amd.metadata ->> 'type' meet_type
from artist_meet_detail amd
inner join artist_meets am on amd.artist_meet_id = am.id
inner join artist a on a.id = am.artist_id
where amd.artist_meet_id = $1 and amd.is_active = true;`;

export const saveRequestQuery = `insert into artist_meet_validation (user_id,artist_meet_detail_id,request_meta_data,status)
values ($1, $2, $3, $4)
returning *;`;

export const getRequestQuery = `
select
	amv.id,amv.status,request_meta_data->'name'->>'value' as "userName",
	request_meta_data->'phn_no'->>'value' as "phoneNumber",
	request_meta_data->'email'->>'value' as "email",
	a.name as "artistName",amv.artist_meet_detail_id meet_id,
	amd.name as "meetName"
from artist_meet_validation amv
inner join artist_meet_detail amd on amd.id = amv.artist_meet_detail_id
inner join artist_meets am on am.id = amd.artist_meet_id
inner join artist a on a.id = am.artist_id
where amv.id = $1;`;

export const getRequestByArtistIdQuery = `
	select 
		amv.*  from artist_meet_validation amv
	where 
		amv.artist_meet_detail_id = ANY($1) 
	and 
		amv.user_id = $2 
	and amv.status = ANY($3)`;

export const updateRequestQuery = `update artist_meet_validation
Set status = $1,
artist_feedback = $3,
updated = now()
where id = $2 and status != $1
returning *;`;

export const userDetailAddressAltQuery = `select ua.display_name "firstName",'' "lastName",ua.email,ua.mobile_number,JSONB_BUILD_OBJECT(
    'address_line_one','',
    'address_line_two','',
    'address_line_three','',
    'city','',
    'state','',
    'country','',
    'pincode','') address from users ua
    where ua.id = $1`;

export const userAddressByIdQuery = `select ua.first_name "firstName",ua.last_name "lastName",ua.email,ua.phone mobile_number,
JSONB_BUILD_OBJECT(
		'address_line_one',ua.address_line_one,
		'address_line_two',ua.address_line_two,
		'address_line_three',ua.address_line_three,
		'city',ua.city,
		'state',ua.state,
		'country',ua.country,
		'pincode',ua.pincode) address from user_address ua
		where ua.id = $1`;

export const meetOrderDetailPdfQuery = `
SELECT 
		aml.id,
    TO_CHAR(aml.created, 'Mon DD, YYYY') AS date,
    pm.name AS "paymentMethod",
    array_agg(
        JSONB_BUILD_OBJECT(
            'name', amd.name || ' - ' || am.gst_code,
            'gst', (
                (
                    COALESCE(aml.base_price, pp.price) / (1 + (am.gst::numeric/100))
                    - 
                    (
                        CASE 
                            WHEN cc.id IS NOT NULL THEN 
                                (
                                    COALESCE(aml.base_price, pp.price) / (1 + (pp.gst::numeric/100))
                                ) * (cc.discount / 100)
                            ELSE 0 
                        END
                    )
                ) * (pp.gst/100)
            )::numeric(12,2),
            'price', (
                COALESCE(aml.base_price, pp.price) / (1 + (pp.gst::numeric/100))
            )::numeric(12,2),
            'qty', 1,
            'total', COALESCE(aml.purchased_price, pp.price),
            'symbol', pc.symbol,
            'image', '',
            'discounted', (
                CASE 
                    WHEN cc.id IS NOT NULL THEN 
                        (
                            COALESCE(aml.base_price, pp.price) / (1 + (pp.gst::numeric/100))
                        ) * (cc.discount/100)
                    ELSE 0 
                END
            )
        )
    ) AS products,
    (
        CASE 
            WHEN cc.id IS NOT NULL THEN 
                (
                    COALESCE(aml.base_price, pp.price) / (1 + (pp.gst::numeric/100))
                ) * (cc.discount/100)
            ELSE 0 
        END
    ) AS discounted,
    pc.symbol,
    COALESCE(aml.purchased_price, pp.price) AS total,
    aml.address_id,
    aml.slot_date,
    aml.slot_time,
    cc.code AS coupon_code,
	am.id "meetId",
	amv.request_meta_data -> 'batch' ->> 'value' AS batch
FROM artist_meeting_logs aml 
INNER JOIN artist_meet_detail amd 
    ON aml.artist_meet_id = amd.artist_meet_id
INNER JOIN artist_meets am 
    ON am.id = amd.artist_meet_id
INNER JOIN payment_method pm 
    ON pm.id = aml.payment_method
INNER JOIN payment_currency pc 
    ON pc.id = aml.payment_currency
INNER JOIN product_prices pp 
    ON pp.meet_id = am.id 
    AND pp.currency = aml.payment_currency
LEFT JOIN coupon_codes cc 
    ON cc.id = aml.offer_id
LEFT JOIN artist_meet_validation amv 
    ON amv.user_id = aml.user_id
WHERE aml.id = $1
GROUP BY aml.id, am.id, pc.symbol, pm.name, cc.id, pp.id,amv.request_meta_data
`;

export const paidVidOrderQuery = `select upv.id,TO_CHAR(upv.created, 'Mon DD, YYYY') date,pm.name "paymentMethod",
array_agg(
	JSONB_BUILD_OBJECT('name','Video ' || pv.title || ' - ' || pv.gst_code,
					  'gst',(
							  (coalesce(pt.base_price,pp.price)/(1 + (pp.gst::numeric/100))
							  - (case when cc.id is not null then 
									((coalesce(pt.base_price,pp.price)/(1 + (pp.gst::numeric/100)))
										*  (cc.discount/100))
								else 0 end)) * (pp.gst/100)
						  	 )::numeric(12,2),
					  'price',(
								  coalesce(pt.base_price,pp.price)/(1 + (pp.gst::numeric/100))
						  	 )::numeric(12,2),
					  'qty',1,
					  'total',coalesce(pt.amount, pp.price),
					  'symbol',pc.symbol,
					  'image', '',
					  'discounted',(case when cc.id is not null then 
									((coalesce(pt.base_price,pp.price)/(1 + (pp.gst::numeric/100)))
										*  (cc.discount/100))::numeric(12,2)
								else 0 end))) products,
(case when cc.id is not null then 
	((coalesce(pt.base_price,pp.price)/(1 + (pp.gst::numeric/100)))
		*  (cc.discount/100))::numeric(12,2)
else 0 end) discounted,
pc.symbol,coalesce(pt.amount,pv.price) total, upv.address_id,cc.code coupon_code
from user_paid_video_entries upv
inner join payment_transactions pt on pt.id = upv.txn_id
inner join paid_videos pv on pv.id = upv.video_id
inner join payment_currency pc on pc.id = pt.payment_currency
inner join payment_method pm on pm.id = pt.payment_method
inner join product_prices pp on pp.video_id = pv.id and pp.currency = pt.payment_currency
left join coupon_codes cc on cc.id = upv.offer_id
where upv.id = $1
group by upv.id,pv.id,pt.id,pc.symbol,pm.name,cc.id,pp.id`;

export const courseOrderQuery = `
WITH combined_courses AS (
    SELECT 
        cpl.id,
        cpl.created,
        cpl.is_combo_purchase,
        cpl.base_price,
        cpl.purchased_price,
        cpl.payment_method,
        cpl.payment_currency,
        cpl.offer_id,
        cpl.address_id,
        CASE 
            WHEN cpl.is_combo_purchase = false THEN 
                string_agg(DISTINCT cd.title || ' - ' || cd.course_level, ', ')
            ELSE 
                MAX(cd.title || ' - ' || cd.course_level)
        END as combined_title,
        MAX(cd.gst_code) as gst_code,
        MAX(pp.price) as price,
        MAX(pp.gst) as gst
    FROM course_purchase_logs cpl 
    INNER JOIN course_details cd ON (
        (cpl.is_combo_purchase = false AND cd.course_level_id = cpl.course_id)
        OR
        (cpl.is_combo_purchase = true AND cd.id = cpl.course_id)
    )
    INNER JOIN product_prices pp ON pp.course_id = cd.id AND pp.currency = cpl.payment_currency
    WHERE cpl.id = $1
    GROUP BY cpl.id, cpl.created, cpl.is_combo_purchase, cpl.base_price, cpl.purchased_price, 
             cpl.payment_method, cpl.payment_currency, cpl.offer_id, cpl.address_id
)
SELECT 
    cc.id,
    TO_CHAR(cc.created, 'Mon DD, YYYY') as date,
    pm.name as "paymentMethod",
    array_agg(
        JSONB_BUILD_OBJECT(
            'name', 'Course ' || cc.combined_title || ' - ' || cc.gst_code,
            'gst', (
                (coalesce(cc.base_price, cc.price) / (1 + (cc.gst::numeric/100))
                - (CASE WHEN coupon.id IS NOT NULL THEN 
                    ((coalesce(cc.base_price, cc.price) / (1 + (cc.gst::numeric/100)))
                     * (coupon.discount/100))
                   ELSE 0 END)) * (cc.gst/100)
            )::numeric(12,2),
            'price', (
                coalesce(cc.base_price, cc.price) / (1 + (cc.gst::numeric/100))
            )::numeric(12,2),
            'qty', 1,
            'total', coalesce(cc.purchased_price, cc.price),
            'symbol', pc.symbol,
            'image', '',
            'discounted', (CASE WHEN coupon.id IS NOT NULL THEN 
                ((coalesce(cc.base_price, cc.price) / (1 + (cc.gst::numeric/100)))
                 * (coupon.discount/100))::numeric(12,2)
               ELSE 0 END)
        )
    ) as products,
    pc.symbol,
    coalesce(cc.purchased_price, cc.price) as total,
    cc.address_id,
    CASE WHEN (coupon.id IS NOT NULL) THEN 
        ((coalesce(cc.purchased_price, cc.price) / ((100 - coupon.discount)/100))::numeric(12,2) 
         - coalesce(cc.purchased_price, cc.price)) 
    END as discounted,
    coupon.code as coupon_code
FROM combined_courses cc
INNER JOIN payment_method pm ON pm.id = cc.payment_method
INNER JOIN payment_currency pc ON pc.id = cc.payment_currency
LEFT JOIN coupon_codes coupon ON coupon.id = cc.offer_id
GROUP BY cc.id, cc.created, pm.name, pc.symbol, coupon.id, coupon.code, cc.purchased_price, cc.price, cc.address_id, cc.combined_title, cc.gst_code, cc.base_price, cc.gst;
`;

// export const courseOrderQuery = `select cpl.id,TO_CHAR(cpl.created, 'Mon DD, YYYY') date,pm.name "paymentMethod",
// array_agg(
// 	JSONB_BUILD_OBJECT('name','Course ' || cd.title || ' - ' || cd.gst_code,
// 					  'gst',(
// 							  (coalesce(cpl.base_price,pp.price)/(1 + (pp.gst::numeric/100))
// 							  - (case when cc.id is not null then 
// 									((coalesce(cpl.base_price,pp.price)/(1 + (pp.gst::numeric/100)))
// 										*  (cc.discount/100))
// 								else 0 end)) * (pp.gst/100)
// 						  	 )::numeric(12,2),
// 					  'price',(
// 							  coalesce(cpl.base_price,pp.price)/(1 + (pp.gst::numeric/100))
// 						  	 )::numeric(12,2),
// 					  'qty',1,
// 					  'total',coalesce(cpl.purchased_price,pp.price),
// 					  'symbol',pc.symbol,
// 					  'image', '',
// 					  'discounted',(case when cc.id is not null then 
// 									((coalesce(cpl.base_price,pp.price)/(1 + (pp.gst::numeric/100)))
// 										*  (cc.discount/100))::numeric(12,2)
// 								else 0 end))) products,pc.symbol symbol,coalesce(cpl.purchased_price,pp.price) total,cpl.address_id,
// case when (cc.id is not null) then ((coalesce(cpl.purchased_price,pp.price)/((100 - cc.discount)/100))::numeric(12,2) - coalesce(cpl.purchased_price,pp.price)) end discounted,
// cc.code coupon_code
// from course_purchase_logs cpl 
// INNER JOIN course_details cd 
//   ON (
//     (cpl.is_combo_purchase = false AND cd.course_level_id = cpl.course_id)
//     OR
//     (cpl.is_combo_purchase = true AND cd.id = cpl.course_id)
//   )
// inner join payment_method pm on pm.id = cpl.payment_method
// inner join payment_currency pc on pc.id = cpl.payment_currency
// inner join product_prices pp on pp.course_id = cd.id and pp.currency = cpl.payment_currency
// left join coupon_codes cc on cc.id = cpl.offer_id
// where cpl.id = $1
// group by cpl.id,cd.id,pc.symbol,pm.name,cc.id,pp.id;`;

export const orderInvoiceDetailByIdQuery = `WITH orders_list AS (
		SELECT
			od.id, TO_CHAR(od.created, 'Mon DD, YYYY') date,
			pm.name "paymentMethod", pcc.symbol symbol,
			COALESCE( oi.price_per_quantity, pi.price ) price,
			oi.quantity, pi.image_url, p.name ||
			COALESCE(' - ' || STRING_AGG( vo.value,', ' ORDER BY vo.variation_id),'') ||
			COALESCE(' - ' || p.gst_code,'') "name",
			COALESCE(
				(oi.base_price)::numeric(12,2),
				(pi.price)::numeric(12,2)
			) orginal_base_price,
			COALESCE(
				(oi.base_price/ (1 + (pp.gst::numeric / 100)))::numeric(12,2),
				(pi.price/ (1 + (pp.gst::numeric / 100)))::numeric(12,2)
			) original_price_notax,
			(
				CASE
				WHEN (pcs.id IS NOT NULL) and (cc.id IS NOT NULL) and (cc.all_products = false)
				THEN (
						(
							COALESCE(
								(oi.base_price/ (1 + (pp.gst::numeric / 100)))::numeric(12,2),
								(pi.price/ (1 + (pp.gst::numeric / 100)))::numeric(12,2)
							)
							* ( cc.discount  / 100 )
						)::numeric(12,2)
					)
				WHEN (cc.id IS NOT NULL) and (cc.all_products = true)
				THEN (
						(
							COALESCE(
								(oi.base_price/ (1 + (pp.gst::numeric / 100)))::numeric(12,2),
								(pi.price/ (1 + (pp.gst::numeric / 100)))::numeric(12,2)
							)
							* ( cc.discount  / 100 )
						)::numeric(12,2)
					)
				ELSE 0
				END
			) discounted,
			cc.code coupon_code, pp.gst,
			cc.discount,
			od.purchased_price,
			od.address_id
		FROM order_details od
		INNER JOIN order_items oi on oi.order_id = od.id
		INNER JOIN product_item pi on pi.id = oi.product_item_id
		INNER JOIN products p 
		  ON (
		    (pi.product_type != 2 AND p.id = pi.product_id)
		    OR
		    (pi.product_type = 2 AND p.id = pi.course_product_id)
		  )
		INNER JOIN payment_method pm on pm.id = od.payment_method
		INNER JOIN payment_currency pcc on pcc.id = od.payment_currency
		INNER JOIN product_prices pp on pp.product_id = p.id and pp.currency = od.payment_currency
		LEFT JOIN coupon_codes cc on cc.id = od.offer_id
		left join product_coupons pcs on pcs.product_id = p.id and pcs.coupon_id = cc.id
		LEFT JOIN product_configuration pc on pc.product_item_id = pi.id
		LEFT JOIN variation_option vo on vo.id = pc.variation_option_id
		WHERE od.id = $1
		GROUP BY od.id, pi.id, oi.id,
		p.id, pm.name, pcc.symbol, pp.gst, cc.id, pcs.id, od.address_id
	)
	SELECT 
		id, date, "paymentMethod", symbol, 
		purchased_price as total,
		address_id,
		ARRAY_AGG(
			JSONB_BUILD_OBJECT(
				'name', name,
				'price', original_price_notax,
				'gst', ((original_price_notax -  discounted) * (gst / 100))::numeric(12,2),
				'qty', quantity,
				'total', CASE 
					WHEN "paymentMethod" = 'Paypal' 
					THEN ((original_price_notax - discounted) * 1.1)::numeric(12,2) * quantity
					ELSE (original_price_notax - discounted) + ((original_price_notax - discounted) * (gst / 100))::numeric(12,2) * quantity
				END,
				'symbol', symbol,
				'image',image_url,
				'discounted',discounted
			)
		) products,
		SUM(discounted) discounted,
		coupon_code
	FROM orders_list
	GROUP BY id, date, "paymentMethod",
		symbol, coupon_code, purchased_price, address_id;`;

export const updatePurchaseStatusQuery = `update course_purchase_logs
set payment_status = $1,
updated = now(),
invoice = $3
where merchant_transaction_id = $2
returning *;`;

// export const getCourseDetailQuery = `
// SELECT
//     cd.id, cd.title,
//     cd.sub_title "subTitle", cd.description,
//     cd.course_level "level", cd.duration,
//     cd.course_language "language", cd.price, cd.gst,
//     cd.author_detials author, cd.course_includes "courseIncludes",
//     image, cd.course_path,
//     array_agg(
//         JSONB_BUILD_OBJECT(
//             'currency', pcr.name,
//             'currency_id', pcr.id,
//             'price', pp.price,
//             'max_price', pp.max_price,
//             'gst', pp.gst
//         )
//     ) currencies,
//     (
//         SELECT json_agg(
//             json_build_object(
//                 'id', pc.id,
//                 'country_name', cl.name,
//                 'country_id', cl.id,
//                 'item_id', pc.course_id
//             )
//         )
//         FROM product_country pc
//         INNER JOIN country_list cl ON cl.id = pc.country_id
//         WHERE pc.course_id = cd.id
//     ) AS countries,
//     (
//         SELECT json_agg(
//             json_build_object(
//                 'id', ce.id,
//                 'name', ce.title,
//                 'order', ce.order_number,
//                 'playList', (
//                     SELECT json_agg(
//                         JSONB_BUILD_OBJECT(
//                             'id', cv.id,
//                             'name', cv.title,
//                             'type', cv.type,
//                             'access', cv.access,
//                             'duration', cv.duration,
//                             'filePath', cv.file_path
//                         )
//                         ORDER BY cv.order_number
//                     )
//                     FROM course_videos cv
//                     WHERE cv.episode_id = ce.id AND cv.is_active = true
//                 )
//             )
//         )
//         FROM course_episodes ce
//         WHERE ce.course_id = cd.id AND ce.is_active = true
//     ) "chapters",
//     EXISTS(
//         SELECT 1 FROM user_courses
//         WHERE course_id = cd.id
//         AND user_id = $2 AND valid_till >= now()
//     ) "purchased",
//     cd.created,
//     cd.updated,
//     cd.filter_keyword
// FROM course_details cd
// INNER JOIN product_prices pp ON pp.course_id = cd.id
// INNER JOIN payment_currency pcr ON pcr.id = pp.currency
// WHERE cd.id = $1 AND cd.is_active = true
// GROUP BY cd.id;
// `;

export const getCourseDetailQuery = `SELECT json_agg(course_data) AS courses
FROM (
    SELECT 
        JSONB_BUILD_OBJECT(
            'id', cd.id,
            'title', cd.title,
            'subTitle', cd.sub_title,
            'description', cd.description,
            'level', cd.course_level,
            'courseLevelId', cd.course_level_id,
            'duration', cd.duration,
            'language', cd.course_language,
            'price', cd.price,
            'gst', cd.gst,
            'author', cd.author_detials,
            'courseIncludes', cd.course_includes,
            'image', cd.image,
            'course_path', cd.course_path,
            'filter_keyword', cd.filter_keyword,
            'created', cd.created,
            'updated', cd.updated,
            'currencies', (
                SELECT json_agg(
                    JSONB_BUILD_OBJECT(
                        'currency', pcr.name,
                        'currency_id', pcr.id,
                        'price', pp.price,
                        'max_price', pp.max_price,
                        'gst', pp.gst
                    )
                )
                FROM product_prices pp
                INNER JOIN payment_currency pcr ON pcr.id = pp.currency
                WHERE pp.course_id = cd.id
            ),
            'countries', (
                SELECT json_agg(
                    json_build_object(
                        'id', pc.id,
                        'country_name', cl.name,
                        'country_id', cl.id,
                        'item_id', pc.course_id
                    )
                )
                FROM product_country pc
                INNER JOIN country_list cl ON cl.id = pc.country_id
                WHERE pc.course_id = cd.id
            ),
            'chapters', (
                SELECT json_agg(
                    json_build_object(
                        'id', ce.id,
                        'name', ce.title,
                        'order', ce.order_number,
                        'playList', (
                            SELECT json_agg(
                                JSONB_BUILD_OBJECT(
                                    'id', cv.id,
                                    'name', cv.title,
                                    'type', cv.type,
                                    'access', cv.access,
                                    'duration', cv.duration,
                                    'filePath', cv.file_path
                                )
                                ORDER BY cv.order_number
                            )
                            FROM course_videos cv
                            WHERE cv.episode_id = ce.id AND cv.is_active = true
                        )
                    )
                )
                FROM course_episodes ce
                WHERE ce.course_id = cd.id AND ce.is_active = true
            ),
            'purchased', EXISTS (
                SELECT 1 FROM user_courses 
                WHERE course_id = cd.id 
                AND user_id = $2 AND valid_till >= now()
            )
        ) AS course_data
    FROM course_details cd
    WHERE (
        ($3::boolean = true AND cd.id = $1) OR 
        ($3::boolean = false AND cd.course_level_id = $1)
      )    
	  AND cd.is_active = true

) AS sub;`;

export const insertUserCourseQuery = `insert into user_courses(course_id,user_id,valid_till)
values($1,$2,(CURRENT_TIMESTAMP + ($3 || ' days')::interval));`;

export const updatePaymentTxnQuery = `update payment_transactions
set payment_status = $1,
updated = now()
where merchant_transaction_id = $2;`;

export const updateVideoPurchasedQuery = `update user_paid_video_entries
set is_completed = true,
valid_till = case when validity is not null then (CURRENT_TIMESTAMP + (validity || ' days')::interval) end,
invoice = $2,
updated = now()
where txn_id = $1`;

export const addAddressQuery = `INSERT INTO public.user_address(
	user_id, first_name, last_name, address_line_one, address_line_two, city, state, pincode, phone, email, country,address_line_three)
	VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12)
    returning *;`;

export const getTreasuresQuery = `select id as "hunt_id",coins_to_collect total_coins_to_collect,metadata from treasure_hunt where is_active = true and artist_id = $1 or $1 is null`;

export const getCoinQuery = `select tc.id coin_id,tc.quantity_per_claim,tc.treasure_id hunt_id,
th.artist_id, th.coins_to_collect total_coins_to_collect, th.available, th.is_limited_quantity
from treasure_coins tc 
inner join treasure_hunt th on th.id = tc.treasure_id
where (th.id = $1)
order by tc.id;`;

export const userTreasureHoldingQuery = `select * from treasure_holdings where user_id = $1 and treasure_id = $2 and status != 'ELIGIBLE';`;

export const addHoldingsQuery = `insert into treasure_holdings(user_id,treasure_id,available)
values($1,$2,$3)
returning *;`;

export const updateHoldingQuery = `update treasure_holdings
set available = available + $1,
updated = now()
where id = $2
returning *;`;

export const updateHoldingStatusQuery = `update treasure_holdings
set status = $1,
updated = now()
where id = $2
returning *;`;

export const updateHuntAvilabilityQuery = `update treasure_hunt
set available = available - 1,
updated = now()
where id = $1
returning *;`;

export const getUserTreasureQuery = `select * from treasure_holdings where id = $1 and user_id = $2 and status = 'ELIGIBLE';`;

export const updateTreasureProductQuery = `update treasure_holdings
set product_item_id = $1,
address_id = $2,
order_status = 2,
updated = now()
where id = $3
returning *;`;

export const addTreasureHistory = `insert into treasure_history(user_id,coin_id,treasure_holdings_id,quantity)
values($1,$2,$3,$4);`;

export const userClaimedCoinsQuery = `select th.id,th.treasure_id,ths.coin_id,th.product_item_id from treasure_holdings th 
inner join treasure_hunt tht on tht.id = th.treasure_id
inner join treasure_history ths on ths.treasure_holdings_id = th.id
where tht.id = $1 and th.user_id = $2;`;

export const getTreasureProductsQuery = `select p.* from products p
inner join treasure_product_items tp on tp.product_id = p.id
where p.is_active = true and p.artist_id = $1
group by p.id`;

export const getProductItemByIdQuery = `select *,p.artist_id from product_item pi
INNER JOIN products p 
  ON (
    (pi.product_type != 2 AND p.id = pi.product_id)
    OR
    (pi.product_type = 2 AND p.id = pi.course_product_id)
  )
 AND p.product_type = pi.product_type
where pi.id = $1;`;

export const getMeetSourceIdQuery = `select am.source_id from artist_meet_detail amd
inner join artist_meets am on am.id = amd.artist_meet_id
where amd.id = $1;`;

export const productItemQuery = `select 
pi.id "productItemId",
(pp.price * $2) price,
pp.price price_per_qty,
pp.gst tax,
pcr.name currency,
pcr.id currency_id,
$2 quantity,
case when pcr.id = 1 then '₹' else '$' end currency_symbol
from product_item pi 
inner join product_prices pp on pp.product_id = pi.product_id and pp.currency = $3
inner join payment_currency pcr on pcr.id = pp.currency
where pi.id = $1`;

export const getOfferQuery = `select * from coupon_codes where code = UPPER(TRIM($1)) and is_active = true and all_products = $2`;

export const getProductOfferQuery = `select * from product_coupons where coupon_id = $1 and (meet_id = $2 or course_id = $3 or video_id = $4 or product_id = $5);`;

export const getPdtsQuery = `select p.*,uw.id wishlist_id from products p
left join user_wishlist uw on uw.product_id = p.id and uw.user_id = $2 
where p.id = $1`;

export const getCourseQuery = `select cd.*,uw.id wishlist_id from course_details cd
left join user_wishlist uw on uw.course_id = cd.id and uw.user_id = $2 
where cd.id = $1`;

export const getMeetQuery = `select am.*,uw.id wishlist_id from artist_meets am
left join user_wishlist uw on uw.meet_id = am.id and uw.user_id = $2 
where am.id = $1`;

export const getVideoQuery = `select pv.*,uw.id wishlist_id from paid_videos pv
left join user_wishlist uw on uw.video_id = pv.id and uw.user_id = $2 
where uw.id = $1`;

export const addWishListQuery = `insert into user_wishlist(product_id,meet_id,course_id,video_id,user_id)
values($1,$2,$3,$4,$5);`;

export const deleteWishlistQuery = `delete from user_wishlist where id = $1;`;

export const offerUsedCountQuery = `select count(id) total_count,count(id) filter (where user_id = $1) user_count from order_details where offer_id = $2;`;

export const getUserAddressByIdQuery = `select * from user_address where id = $1`;

export const getProductIdByItem = `select product_id from product_item where id = $1`;

export const orderDetailForCancelQuery = `select ua.first_name || ' ' || ua.last_name "customerName",ua.email,
array_agg(
	JSONB_BUILD_OBJECT(
		'name',pi.title || ' - ' || p.gst_code,
		'imageUrl',pi.image_url,
		'size',(
			select vo.value from variation_option vo
			inner join product_configuration pc on pc.variation_option_id = vo.id
			where vo.variation_id = 2 and pc.product_item_id = oi.product_item_id
		),
		'quantity',oi.quantity
	)
) products
from order_details od
inner join order_items oi on oi.order_id = od.id
inner join user_address ua on ua.id = od.address_id
INNER JOIN product_item pi on pi.id = oi.product_item_id
INNER JOIN products p 
  ON (
    (pi.product_type != 2 AND p.id = pi.product_id)
    OR
    (pi.product_type = 2 AND p.id = pi.course_product_id)
  )
where od.id = $1
group by ua.id;`;

export const getPaygOrders = `select * from order_details where (payment_method = 3) and (payment_status in (1,2)) order by id limit 15`;

export const getPaygCourseOrders = `select *,(select validity from course_details cd where cd.id = course_id) validity from course_purchase_logs
where  (payment_method = 3) and (payment_status in (1,2)) order by id limit 15;`;

export const getPaygMeetOrders = `select * from artist_meeting_logs
where  (payment_method = 3) and (payment_status in (1,2)) order by id limit 15;`;

export const getPaygVideoOrders = `select pt.*,upv.id video_entry from payment_transactions pt
inner join user_paid_video_entries upv on upv.txn_id = pt.id 
where  (pt.payment_method = 3) and (pt.payment_status in (1,2)) order by pt.id limit 15;`;

export const insertCalendlyCheckout = `
INSERT INTO calendly_checkout (
    address_id, meet_id, coupon_id,
	user_id, payment_id, is_completed, 
	status, 
    comment, invite_id, 
    price, email
) VALUES (
	$1, $2, $3,
	$4, $5, $6, 
	$7,
	$8, $9,
	$10, $11
)
`;

export const getCalendlyCheckoutByUser = `
	select 
		cc.id, cc.user_id, cc.address_id,
		cc.coupon_id, cc.payment_id,
		cc.meet_id,
		cc.is_completed,
		cc.price, cc.created,
		(
			select code from coupon_codes
			where id = cc.coupon_id
		) coupon,
		am.price base_amount
	from calendly_checkout cc
	inner join artist_meets am on am.id = cc.meet_id
	where cc.user_id = $1 and am.source_id = $2 and cc.status = 'PAYMENT_PENDING'
	ORDER BY cc.id desc
	limit 1;
`;

export const insertMeetLogsQuery = `
	insert into artist_meeting_logs (
		user_id, slot_date, slot_time, form_id,
		artist_meet_id, merchant_transaction_id,
		payment_currency, 
		address_id, 
		offer_id,
		purchased_price,payment_method,base_price)
values ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12)
returning *;`;

export const updateCalendlyWebhook = `
UPDATE calendly_checkout
SET 
    invite_id = $1,
    status = $2
WHERE id IN (
    SELECT 
        calendly_checkout.id
    FROM calendly_checkout
    INNER JOIN artist_meets ON calendly_checkout.email = $3 
                      AND artist_meets.source_id = $4
    WHERE calendly_checkout.is_completed = false 
      AND calendly_checkout.status = 'INIT'
      AND calendly_checkout.invite_id IS NULL
	ORDER BY id DESC
	LIMIT 1
);`;

export const updateCalendlyWebhookOther = `
update calendly_checkout
set 
	status = $1
FROM artist_meets
where calendly_checkout.email = $2 and
artist_meets.source_id = $3 and 
calendly_checkout.is_completed = false and calendly_checkout.status = 'INIT'
and calendly_checkout.invite_id is null;
`;

export const getInviteIDquery = `
select 
	invite_id 
from calendly_checkout cc
inner join artist_meets am on am.id = cc.meet_id
where cc.email = $1 and am.source_id = $2 and cc.invite_id is not null and status = 'PAYMENT_PENDING';
`;

export const updateCalendlyStatus = `
update calendly_checkout
set 
	status = $1,
	is_completed = $5
FROM artist_meets
where calendly_checkout.email = $2 and
artist_meets.source_id = $3 and 
calendly_checkout.is_completed = false and calendly_checkout.status = 'PAYMENT_PENDING'
and calendly_checkout.invite_id = $4;
`;

export const updatePrevCalendlyCheckout = `
UPDATE calendly_checkout
set 
is_completed = false,
status = 'DROPPED'
where
user_id = $1 and payment_id = $2 and meet_id = $3 and status = 'INIT';
`;

export const udpateMeetLogcldlyQuery = `update calendly_checkout
set meet_log_id = $1,
updated = now()
where id = $2;`;

export const getCldlyCheckoutByIDquery = `
select 
	* 
from calendly_checkout cc
inner join artist_meets am on am.id = cc.meet_id
where cc.meet_log_id = $1
`;

export const getTimedoutCalendly = `
SELECT 
    cc.id,
	cc.invite_id
FROM 
    calendly_checkout cc
WHERE 
	(cc.status = 'PAYMENT_PENDING') 
	AND (cc.invite_id IS NOT NULL)
	AND (cc.meet_log_id IS NULL)
	AND (cc.created < NOW() - INTERVAL '10 mins')
ORDER BY 
    cc.id DESC;
`;

export const updatedTimeoutCalendly = `
UPDATE calendly_checkout
	SET 
		status = $2,
		is_completed = $3	
WHERE id = $1;
`;
export const oldPendingMeetsPaymentsQuery = `select aml.merchant_transaction_id,aml.id,aml.artist_meet_id from artist_meeting_logs aml
inner join calendly_checkout cc on cc.meet_log_id = aml.id and cc.status = 'PAYMENT_PENDING'
where (aml.created < now() - interval '10 mins')
and payment_status in (1,2)
order by aml.id asc limit 10;`;

export const getTotalSuccessfulTransactionsForInvoiceNumber = `SELECT 
    (SELECT COUNT(*) FROM course_purchase_logs WHERE payment_status = 3) +
    (SELECT COUNT(*) FROM artist_meeting_logs WHERE payment_status = 3) +
    (SELECT COUNT(*) FROM user_paid_video_entries WHERE is_completed = true) +
    (SELECT COUNT(*) FROM order_details WHERE payment_status = 3) AS id;`;

export const getProductIdByItemIdQuery = `
SELECT id
FROM product_item
WHERE product_id = $1 AND product_type = $2;
`;

export const paidVideosInCartOrderQuery = `
SELECT pv.*
FROM public.order_items oi
JOIN public.product_item pi ON oi.product_item_id = pi.id
JOIN public.paid_videos pv ON pi.product_id = pv.id
WHERE oi.order_id = $1
  AND pi.product_type = 3;
`;

export const coursesInCartOrderQuery = `
SELECT cd.*
FROM public.order_items oi
JOIN public.product_item pi ON oi.product_item_id = pi.id
JOIN public.course_details cd ON pi.product_id = cd.id
WHERE oi.order_id = $1
  AND pi.product_type = 2;
`;

export const insertTxnQuery = `insert into payment_transactions (user_id,amount,merchant_transaction_id,payment_status, payment_currency, payment_method, base_price)
values($1,$2,$3,$4,$5,$6,$7)
returning *`;

export const inserPaidVideoEntryQuery = `insert into user_paid_video_entries (user_id,video_id,txn_id,is_completed,validity,address_id,offer_id)
values($1,$2,$3,$4,$5,$6,$7)
returning *`;

export const getDiscountPercentageQuery = `select * from coupon_codes where id = $1;`;