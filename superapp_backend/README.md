Requierments for Karaoke

1. Install ffmpeg

command sudo apt install ffmpeg

2. Modify <PERSON>inx to accept the payload
Steps For Modifying
    1. sudo nano /etc/nginx/nginx.conf
    2. Update below code 
    
    http {
        # ... Other configurations ...  

        client_max_body_size 20M;

        # ... Other configurations ...
    }

    3. sudo nginx -t
    4. sudo systemctl reload nginx

3. Transfer Google JSON for uploading image to cloud storage
    In src/assets/superapp-401117-ef0c865b1388.json

4. Payglocal Transfer key file in Src and update env
