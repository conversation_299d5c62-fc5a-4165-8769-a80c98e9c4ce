openapi: 3.0.0
info:
  title: Metastar Artistverse Backend API
  version: v1.0
  description: API documentation for Metastar Artistverse backend services.

servers:
  - url: http://localhost:3000 # Replace with your actual base URL or use / if relative

components:
  securitySchemes:
    userAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT for User API (/api paths)
    adminAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT for Admin Panel API (/admin paths)
    dashboardAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT for New Dashboard API (/dashboard paths)

  schemas:
    # --- Generic Schemas ---
    StatusMessageResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Operation successful."

    UserIdentifierInput:
      type: object
      properties:
        email:
          type: string
          format: email
          description: User email.
          nullable: true
        mobile:
          type: string
          description: User mobile number.
          nullable: true
        user_name:
          type: string
          description: Username.
          nullable: true

    UserDetails: # Placeholder for user details structure
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        email:
          type: string
          format: email
        # ... other user fields

    AddressDetails:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        first_name:
          type: string
        last_name:
          type: string
        address_line_one:
          type: string
        address_line_two:
          type: string
          nullable: true
        address_line_three:
          type: string
          nullable: true
        city:
          type: string
        state:
          type: string
        pincode:
          type: string
        phone:
          type: string
        email:
          type: string
          format: email
        country:
          type: string
      required:
        - first_name
        - last_name
        - address_line_one
        - city
        - state
        - pincode
        - phone
        - email
        - country

    UserProfileDetails:
      type: object
      properties:
        name:
          type: string
          nullable: true
        email:
          type: string
          format: email
          nullable: true
        phone:
          type: string
          nullable: true
        image:
          type: string
          format: url
          nullable: true
        defaultaddress: # Assuming this refers to default_address_id
          type: integer
          nullable: true

    ProductItem: # Placeholder for product item structure
      type: object
      properties:
        productItemId:
          type: integer
        quantity:
          type: integer
        currency:
          type: integer

    PaymentDetails: # Placeholder for payment details structure
      type: object
      properties:
        # ... common payment fields (e.g., redirectUrl, transactionId)
        id: # often order_id or log_id
          type: integer
          description: ID related to the payment (e.g., order_id, meet_log_id)

    OrderDetails: # Placeholder for generic order details
      type: object
      properties:
        # ... common order fields
        order_id:
          type: integer
        status:
          type: string

    PaginationResult:
      type: object
      properties:
        items:
          type: array
          items:
            type: object # Generic item, specific endpoints will override
        total:
          type: integer
        page:
          type: integer
        pageSize:
          type: integer
        totalPages:
          type: integer

    # --- Specific Schemas for data objects ---
    CheckUserResponse:
      allOf:
        - $ref: "#/components/schemas/StatusMessageResponse"
        - type: object
          properties:
            exists:
              type: boolean

    LoginResponseData:
      type: object
      properties:
        token:
          type: string

    UserRegistrationResponseData:
      type: object
      properties:
        user_details: # Assuming this is the structure returned
          $ref: "#/components/schemas/UserDetails"

    SocialLoginResponseData:
      type: object
      properties:
        user:
          $ref: "#/components/schemas/UserDetails"
        token:
          type: string

    GetAddressResponseData:
      type: object
      properties:
        address:
          $ref: "#/components/schemas/AddressDetails"

    GetAddressAllResponseData:
      type: object
      properties:
        address:
          type: array
          items:
            $ref: "#/components/schemas/AddressDetails"

    GetUserProfileResponseData:
      type: object
      properties:
        user: # As per doc, it's an array, though likely a single object
          type: array
          items:
            $ref: "#/components/schemas/UserProfileDetails"

    AllProductsItem: # Placeholder for item in /allProducts
      type: object
      properties:
        # ... fields for product, meet, course, paid_video
        id:
          type: integer
        name:
          type: string
        type:
          type: string
          enum: [MERCH, COURSE, MEET, PAID_VIDEO] # Example
        wishlist_status:
          type: boolean
          nullable: true

    AllProductsResponseData:
      type: object
      properties:
        products:
          type: array
          items:
            $ref: "#/components/schemas/AllProductsItem"

    ProductDetailsResponseData:
      type: object
      properties:
        imageList:
          type: array
          items:
            type: object # Assuming structure for image list
        details:
          type: object # Assuming structure for product details (variants, pricing)

    CartItemsResponseData:
      type: object
      properties:
        cartItems:
          type: array
          items:
            type: object # Assuming structure for cart items

    OrderResponseData:
      type: object
      properties:
        selectedPayment:
          $ref: "#/components/schemas/PaymentDetails"

    OrdersListItem: # Placeholder for item in /orders list
      type: object
      properties:
        # ... common fields for an order summary
        order_id:
          type: integer
        order_date:
          type: string
          format: date-time
        total_amount:
          type: number
        status:
          type: string

    OrdersListResponseData:
      type: object
      properties:
        orders:
          type: array
          items:
            $ref: "#/components/schemas/OrdersListItem"
        totalPages:
          type: integer

    OrderDetailResponseData:
      type: object
      properties:
        detail:
          $ref: "#/components/schemas/OrderDetails" # Generic, should be more specific
        products:
          type: array
          items:
            type: object # Product details specific to this order item

    KaraokeTrackItem:
      type: object
      properties:
        id:
          type: integer
        title:
          type: string
        # ... other track details

    KaraokeTracksResponseData:
      type: object
      properties:
        track:
          type: array
          items:
            $ref: "#/components/schemas/KaraokeTrackItem"

    ArtistMeetRegistrationResponseData:
      type: object
      properties:
        price:
          type: number
        currencies:
          type: array
          items:
            type: object # Currency details
        tax:
          type: number
        name:
          type: string
        image:
          type: string
          format: url
        duration:
          type: string
        artsit_name: # Typo from original, keeping for consistency
          type: string
        user:
          type: object # User details
        slots:
          type: object # Slot details

    ArtistMeetDetailItem: # Placeholder for item in /getArtistBookDetail
      type: object
      properties:
        # ... meet details
        id:
          type: integer
        title:
          type: string
        request_status: # If applicable
          type: string
          nullable: true

    ArtistMeetDetailResponseData: # As per doc, it's an array, though likely a single object
      type: array
      items:
        $ref: "#/components/schemas/ArtistMeetDetailItem"

    CourseItem:
      type: object
      properties:
        id:
          type: integer
        title:
          type: string
        # ... other course summary fields

    CoursesResponseData:
      type: object
      properties:
        courses:
          type: array
          items:
            $ref: "#/components/schemas/CourseItem"

    CourseDetailResponseData:
      type: object
      properties:
        # ... detailed course structure including chapters, videos, purchase status
        id:
          type: integer
        title:
          type: string
        is_purchased:
          type: boolean

    CoursePurchaseResponseData:
      type: object
      properties:
        id:
          type: integer # purchase_log_id
        payment:
          $ref: "#/components/schemas/PaymentDetails"

    V2CoursePurchaseResponseData:
      type: object
      properties:
        selectedPayment:
          $ref: "#/components/schemas/PaymentDetails" # id here is purchase_log_id

    PaidVideoItem:
      type: object
      properties:
        id:
          type: integer
        title:
          type: string
        # ... other video summary fields

    PaidVideosResponseData:
      type: object
      properties:
        videos:
          type: array
          items:
            $ref: "#/components/schemas/PaidVideoItem"

    PaidVideoDetailResponseData:
      type: object
      properties:
        # ... detailed video structure
        id:
          type: integer
        title:
          type: string
        trailer_stream_url:
          type: string
          format: url
          nullable: true
        video_stream_url:
          type: string
          format: url
          nullable: true

    SubscribeVideoResponseData:
      type: object
      properties:
        id:
          type: integer # entry_id
        payment:
          $ref: "#/components/schemas/PaymentDetails"

    V2SubscribeVideoResponseData:
      type: object
      properties:
        selectedPayment:
          $ref: "#/components/schemas/PaymentDetails" # id here is entry_id

    TreasureCoinDetails:
      type: object
      properties:
        # ... coin details
        claimed:
          type: boolean
        coins_collected:
          type: integer
        productClaimed:
          type: boolean

    TreasureProductItem:
      type: object
      properties:
        # ... product details for treasure rewards
        id:
          type: integer
        name:
          type: string

    TreasureProductsResponseData:
      type: object
      properties:
        products:
          type: array
          items:
            $ref: "#/components/schemas/TreasureProductItem"

    PopupContentResponseData:
      type: object
      properties:
        title:
          type: string
        images:
          type: array
          items:
            type: string # Assuming image URLs
        text:
          type: string
        audioLink:
          type: string
          format: url
          nullable: true
        video:
          type: string # Video identifier or URL
          nullable: true
        video_stream_url:
          type: string
          format: url
          nullable: true

    VideoDetailsResponseData:
      type: object
      properties:
        video_id:
          type: integer
        video:
          type: string # Video identifier or URL
        video_stream_url:
          type: string
          format: url

    CalendlySlotDetails: # Placeholder
      type: object
      properties:
        # ... slot details from Calendly
        available_times:
          type: array
          items:
            type: string # e.g., "2023-10-27T10:00:00"

    DashboardLoginResponse:
      type: object
      properties:
        message:
          type: string
        token:
          type: string
        expiresAt:
          type: string
          format: date-time
        user:
          type: object
          properties:
            id:
              type: integer
            name:
              type: string
            email:
              type: string
              format: email
            userType:
              type: integer
            artistMap:
              type: object # Or specific schema if known

    DashboardPaginatedUsers:
      allOf:
        - $ref: "#/components/schemas/PaginationResult"
        - type: object
          properties:
            items:
              type: array
              items:
                $ref: "#/components/schemas/UserDetails" # Assuming user details structure

    DashboardPaginatedKaraoke:
      allOf:
        - $ref: "#/components/schemas/PaginationResult"
        - type: object
          properties:
            items:
              type: array
              items:
                $ref: "#/components/schemas/KaraokeTrackItem" # Assuming karaoke recording structure

    DashboardPaginatedOrders:
      allOf:
        - $ref: "#/components/schemas/PaginationResult"
        - type: object
          properties:
            items:
              type: array
              items:
                $ref: "#/components/schemas/OrderDetails" # Assuming sales item structure

    TopFanItem:
      type: object
      properties:
        user_id:
          type: integer
        user_name:
          type: string
        purchase_amount:
          type: number

    TopFansResponseData:
      type: object
      properties:
        top_fans:
          type: array
          items:
            $ref: "#/components/schemas/TopFanItem"

    TopSongItem:
      type: object
      properties:
        song_id:
          type: integer
        song_title:
          type: string
        play_count:
          type: integer

    TopSongsResponseData:
      type: object
      properties:
        top_songs:
          type: array
          items:
            $ref: "#/components/schemas/TopSongItem"

    AnalyticsTrendData:
      type: object
      properties:
        average: # Or total, depending on metric
          type: number
          nullable: true # Can be null if no data
        total: # For visit data
          type: number
          nullable: true
        percentChange:
          type: number # Or string if it includes '%'
          nullable: true
        trend:
          type: array
          items:
            type: object # e.g., { date: 'YYYY-MM-DD', value: number }
          nullable: true
        isPositive:
          type: boolean
          nullable: true

    CountriesDataResult:
      allOf:
        - $ref: "#/components/schemas/PaginationResult"
        - type: object
          properties:
            items:
              type: array
              items:
                type: object
                properties:
                  country:
                    type: string
                  sessions:
                    type: integer
                  revenue:
                    type: number

    GenderDataResult:
      type: object
      properties:
        male:
          type: object
          properties:
            value:
              type: integer
            percentage:
              type: number
        female:
          type: object
          properties:
            value:
              type: integer
            percentage:
              type: number
        other:
          type: object
          properties:
            value:
              type: integer
            percentage:
              type: number
        total:
          type: integer

    DeviceDataPoint:
      type: object
      properties:
        name:
          type: string
        value:
          type: integer

    DeviceDataResult:
      type: object
      properties:
        devices:
          type: array
          items:
            $ref: "#/components/schemas/DeviceDataPoint"
        browsers:
          type: array
          items:
            $ref: "#/components/schemas/DeviceDataPoint"
        total:
          type: integer

    TotalRevenueResult:
      type: object
      properties:
        total_revenue:
          type: number
        percent_change: # string as per doc
          type: string
        chart_data:
          type: array
          items:
            type: object # e.g., { period: 'week1', revenue: number }
        total_orders:
          type: integer

    TrendingProductItem:
      type: object
      properties:
        product_id:
          type: integer
        product_name:
          type: string
        product_type:
          type: string
        items_sold:
          type: integer

    TrendingProductsResult:
      type: object
      properties:
        trending_products:
          type: array
          items:
            $ref: "#/components/schemas/TrendingProductItem"

    HtmlPopupResponseData:
      type: object
      properties:
        htmlContent:
          type: string

    AdminPanelListResult: # Specific for admin panel list views
      type: object
      properties:
        items:
          type: array
          items:
            type: object # Generic item, specific endpoints will override
        total:
          type: integer

    AdminFilterOptionsData:
      type: object
      properties:
        loginTypes:
          type: array
          items:
            type: object # e.g., {id: 1, name: 'Google'}
        clients:
          type: array
          items:
            type: object # e.g., {id: 1, name: 'Client A'}

tags:
  - name: 1. User Authentication & Management
    description: Endpoints for user registration, login, OTP, and password management.
  - name: 2. User Profile & Address Management
    description: Endpoints for managing user profiles and addresses.
  - name: 3. Marketplace (General)
    description: General marketplace endpoints like product listings and wishlists.
  - name: 4. Merchandise (Cart & Orders)
    description: Endpoints related to merchandise shopping cart and order processing.
  - name: 5. Karaoke
    description: Endpoints for karaoke track listing and processing.
  - name: 6. Meet the Artist (Booking)
    description: Endpoints for booking artist meets and payment.
  - name: 7. Meet the Artist (Request/Approval)
    description: Endpoints for meet request submissions and artist approval workflow.
  - name: 8. Courses
    description: Endpoints for course listing, details, purchase, and video streaming.
  - name: 9. Paid Videos
    description: Endpoints for paid video (film) listing, details, subscription, and streaming.
  - name: 10. Treasure Hunt
    description: Endpoints for treasure hunt participation and reward claims.
  - name: 11. Immersive Spaces (Popups/Videos)
    description: Endpoints for retrieving content for immersive spaces.
  - name: 12. Calendly Integration
    description: Endpoints for Calendly integration for artist meets.
  - name: 13. Payment Gateway Webhooks
    description: Webhook endpoints for various payment gateways.
  - name: 14. Admin Panel
    description: Endpoints for the admin panel.
  - name: 15. New Dashboard
    description: Endpoints for the new artiste/admin dashboard.
  - name: 16. HTML Popup
    description: Endpoint for retrieving raw HTML popups.
  - name: 17. Payment Gateway Callbacks (Internal Processing)
    description: Internal payment gateway callback processing.

paths:
  # --- 1. User Authentication & Management (/api) ---
  /api/check-user:
    post:
      tags:
        - 1. User Authentication & Management
      summary: Checks if a user exists
      description: Checks if a user exists based on email, mobile, or username.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UserIdentifierInput"
      responses:
        "200":
          description: User existence check result.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CheckUserResponse"
  /api/user:
    post:
      tags:
        - 1. User Authentication & Management
      summary: Registers a new user
      description: Registers a new user. Sends OTP for verification.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userName:
                  type: string
                  description: User's email or mobile number.
                password:
                  type: string
                  format: password
                client_id:
                  type: integer
                  default: 2
                  nullable: true
              required:
                - userName
                - password
      responses:
        "200":
          description: Registration status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/UserRegistrationResponseData"
                        nullable: true
  /api/login:
    post:
      tags:
        - 1. User Authentication & Management
      summary: Logs in a user
      description: Logs in a user with email/mobile and password. Returns JWT.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userName:
                  type: string
                  description: User's email or mobile number.
                password:
                  type: string
                  format: password
              required:
                - userName
                - password
      responses:
        "200":
          description: Login status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/LoginResponseData"
                        nullable: true
  /api/google-user:
    post:
      tags:
        - 1. User Authentication & Management
      summary: Google OAuth login/registration
      description: Handles Google OAuth login/registration. Returns JWT.
      Middleware: verifyGoogleToken.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                  description: Google ID token.
                client_id:
                  type: integer
                  default: 2
                  nullable: true
              required:
                - token
      responses:
        "200":
          description: Google login status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/SocialLoginResponseData"
                        nullable: true
  /api/facebook-user:
    post:
      tags:
        - 1. User Authentication & Management
      summary: Facebook login/registration via Firebase
      description: Handles Facebook login/registration via Firebase. Returns JWT.
      Middleware: verifyFirebaseToken.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                  description: Firebase ID token.
                client_id:
                  type: integer
                  default: 2
                  nullable: true
              required:
                - token
      responses:
        "200":
          description: Facebook login status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/SocialLoginResponseData"
                        nullable: true
  /api/twitter-user:
    post:
      tags:
        - 1. User Authentication & Management
      summary: Twitter login/registration via Firebase
      description: Handles Twitter login/registration via Firebase. Returns JWT.
      Middleware: verifyFirebaseToken.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                  description: Firebase ID token.
                client_id:
                  type: integer
                  default: 2
                  nullable: true
              required:
                - token
      responses:
        "200":
          description: Twitter login status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/SocialLoginResponseData"
                        nullable: true
  /api/firebase-user:
    post:
      tags:
        - 1. User Authentication & Management
      summary: Generic Firebase login/registration
      description: Handles generic Firebase login/registration. Returns JWT.
      Middleware: verifyFirebaseToken.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                  description: Firebase ID token.
                client_id:
                  type: integer
                  default: 2
                  nullable: true
              required:
                - token
      responses:
        "200":
          description: Firebase login status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/SocialLoginResponseData"
                        nullable: true
  /api/forgot-password:
    post:
      tags:
        - 1. User Authentication & Management
      summary: Initiates password reset
      description: Initiates password reset by sending an OTP to the user's email or mobile.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userName:
                  type: string
                  description: User's email or mobile number.
              required:
                - userName
      responses:
        "200":
          description: OTP sending status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
  /api/update-password:
    post:
      tags:
        - 1. User Authentication & Management
      summary: Updates user's password
      description: Updates the user's password after verifying the OTP.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userName:
                  type: string
                  description: User's email or mobile number.
                otp:
                  type: string
                  description: OTP received by the user.
                new_password:
                  type: string
                  format: password
                  description: The new password.
              required:
                - userName
                - otp
                - new_password
      responses:
        "200":
          description: Password update status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/LoginResponseData"
                        nullable: true
  /api/send-otp:
    post:
      tags:
        - 1. User Authentication & Management
      summary: Sends OTP for verification
      description: Sends an OTP to a registered user's email or mobile for verification.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userName:
                  type: string
                  description: User's email or mobile number.
              required:
                - userName
      responses:
        "200":
          description: OTP sending status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
  /api/verify-otp:
    post:
      tags:
        - 1. User Authentication & Management
      summary: Verifies OTP
      description: Verifies the OTP sent to the user's email or mobile. Returns JWT upon successful verification.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userName:
                  type: string
                  description: User's email or mobile number.
                otp:
                  type: string
                  description: OTP entered by the user.
              required:
                - userName
                - otp
      responses:
        "200":
          description: OTP verification status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/LoginResponseData"
                        nullable: true

  # --- 2. User Profile & Address Management (/api) ---
  /api/address: # GET default address & POST new address
    get:
      tags:
        - 2. User Profile & Address Management
      summary: Get user's default or most recent address
      security:
        - userAuth: []
      responses:
        "200":
          description: User's address.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/GetAddressResponseData"
                        nullable: true
    post: # Add new address
      tags:
        - 2. User Profile & Address Management
      summary: Adds a new address for the user
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddressDetails" # Reusing AddressDetails for input
      responses:
        "200":
          description: Address addition status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"

  /api/getAddressAll:
    get:
      tags:
        - 2. User Profile & Address Management
      summary: Retrieves all addresses saved by the user
      security:
        - userAuth: []
      responses:
        "200":
          description: List of user addresses.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/GetAddressAllResponseData"
                        nullable: true
  /api/updateAddress:
    post:
      tags:
        - 2. User Profile & Address Management
      summary: Updates an existing user address
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              allOf: # id is required here, AddressDetails does not have it as required by default
                - $ref: "#/components/schemas/AddressDetails"
                - type: object
                  properties:
                    id:
                      type: integer
                      description: ID of the address to update.
                  required:
                    - id
      responses:
        "200":
          description: Address update status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"

  /api/getUserProfile:
    get:
      tags:
        - 2. User Profile & Address Management
      summary: Retrieves the user's profile information
      description: "Retrieves the user's profile information. Note: API doc says returns an array, likely should be a single object."
      security:
        - userAuth: []
      responses:
        "200":
          description: User profile information.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/GetUserProfileResponseData" # This schema defines user as an array
                        nullable: true
  /api/insertUserProfile:
    post:
      tags:
        - 2. User Profile & Address Management
      summary: Inserts a new user profile entry
      security:
        - userAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UserProfileDetails"
      responses:
        "200":
          description: Profile insertion status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
  /api/updateUserProfile:
    post:
      tags:
        - 2. User Profile & Address Management
      summary: Updates an existing user profile
      security:
        - userAuth: []
      requestBody:
        content:
          application/json:
            schema:
              type: object # Slightly different from UserProfileDetails (default_address vs defaultaddress)
              properties:
                name:
                  type: string
                  nullable: true
                email:
                  type: string
                  format: email
                  nullable: true
                phone:
                  type: string
                  nullable: true
                default_address: # ID of the default address
                  type: integer
                  nullable: true
      responses:
        "200":
          description: Profile update status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
  /api/updateUserProfilePic:
    post:
      tags:
        - 2. User Profile & Address Management
      summary: Uploads and updates user's profile picture
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: The profile picture file.
              required:
                - file
      responses:
        "200":
          description: Profile picture update status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"

  # --- 3. Marketplace (General) (/api) ---
  /api/allProducts:
    get:
      tags:
        - 3. Marketplace (General)
      summary: Retrieves a list of all active products
      description: Retrieves Merch, Courses, Meets, Paid Videos. Includes wishlist status if authenticated. Optional auth.
      security:
        - userAuth: [] # Optional, so we declare it. If no token, it proceeds.
      parameters:
        - name: artistId
          in: query
          description: Comma-separated list of artist IDs.
          schema:
            type: string
        - name: categoryId
          in: query
          description: Comma-separated list of category IDs.
          schema:
            type: string
        - name: countryId
          in: query
          description: Comma-separated list of country IDs.
          schema:
            type: string
      responses:
        "200":
          description: List of products.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/AllProductsResponseData"
                        nullable: true
  /api/allCategories:
    get:
      tags:
        - 3. Marketplace (General)
      summary: Retrieves a list of all active product categories
      responses:
        "200":
          description: List of categories.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data: # Array of category objects
                        type: array
                        items:
                          type: object
                        nullable: true
  /api/addWishlist:
    post:
      tags:
        - 3. Marketplace (General)
      summary: Adds an item to user's wishlist
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: ID of the item.
                type:
                  type: string
                  description: "Type of item ('PRODUCT', 'MEET', 'COURSE', 'PAID_VIDEO')."
                  enum: [PRODUCT, MEET, COURSE, PAID_VIDEO]
              required:
                - id
                - type
      responses:
        "200":
          description: Wishlist addition status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
  /api/removeWishlist:
    post:
      tags:
        - 3. Marketplace (General)
      summary: Removes an item from user's wishlist
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: ID of the item.
                type:
                  type: string
                  description: "Type of item ('PRODUCT', 'MEET', 'COURSE', 'PAID_VIDEO')."
                  enum: [PRODUCT, MEET, COURSE, PAID_VIDEO]
              required:
                - id
                - type
      responses:
        "200":
          description: Wishlist removal status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
  /api/payment-gateways:
    get:
      tags:
        - 3. Marketplace (General)
      summary: Retrieves available payment gateways
      security:
        - userAuth: []
      parameters:
        - name: currency
          in: query
          description: Currency ID (e.g., 1 for INR, 2 for USD).
          schema:
            type: integer
      responses:
        "200":
          description: List of payment gateways.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data: # Array of payment gateway objects
                        type: array
                        items:
                          type: object
                        nullable: true

  # --- 4. Merchandise (Cart & Orders) (/api) ---
  /api/products/{artistId}:
    get:
      tags:
        - 4. Merchandise (Cart & Orders)
      summary: Retrieves merchandise products for a specific artist
      parameters:
        - name: artistId
          in: path
          required: true
          description: ID of the artist.
          schema:
            type: integer
      responses:
        "200":
          description: List of artist's merchandise products.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          products:
                            type: array
                            items:
                              type: object # Product summary
                        nullable: true
  /api/product-details/{productId}:
    get:
      tags:
        - 4. Merchandise (Cart & Orders)
      summary: Retrieves detailed information for a specific merchandise product
      parameters:
        - name: productId
          in: path
          required: true
          description: ID of the product.
          schema:
            type: integer
      responses:
        "200":
          description: Detailed product information.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/ProductDetailsResponseData"
                        nullable: true
  /api/cart: # Add/update item in cart
    post:
      tags:
        - 4. Merchandise (Cart & Orders)
      summary: Adds or updates an item in the user's shopping cart
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                productItemId:
                  type: integer
                  description: ID of the specific product item (variant).
                quantity:
                  type: integer
                  description: Quantity to add/set (0 to remove).
                currency:
                  type: integer
                  default: 1
                  description: Currency ID.
                  nullable: true
              required:
                - productItemId
                - quantity
      responses:
        "200":
          description: Cart update status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
  /api/bulk-cart:
    post:
      tags:
        - 4. Merchandise (Cart & Orders)
      summary: Replaces the user's entire cart
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                products:
                  type: array
                  items:
                    $ref: "#/components/schemas/ProductItem"
              required:
                - products
      responses:
        "200":
          description: Bulk cart update status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
  /api/cart/{artistId}: # Note: artistId can be 'undefined' as a string
    get:
      tags:
        - 4. Merchandise (Cart & Orders)
      summary: Retrieves user's shopping cart items
      security:
        - userAuth: []
      parameters:
        - name: artistId
          in: path
          required: true # Path param is always required, even if value can be 'undefined'
          description: ID of the artist or 'undefined'.
          schema:
            type: string # To accommodate 'undefined'
        - name: coupon
          in: query
          description: Coupon code to apply.
          schema:
            type: string
        - name: currency
          in: query
          description: Currency ID.
          schema:
            type: integer
            default: 1
      responses:
        "200":
          description: User's cart items.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/CartItemsResponseData"
                        nullable: true
  /api/cartSingle:
    get:
      tags:
        - 4. Merchandise (Cart & Orders)
      summary: Calculates price for a single item with coupon
      security:
        - userAuth: []
      parameters:
        - name: coupon
          in: query
          description: Coupon code.
          schema:
            type: string
        - name: currency
          in: query
          description: Currency ID.
          schema:
            type: integer
        - name: productItemId
          in: query
          required: true
          description: ID of the item.
          schema:
            type: integer
        - name: type
          in: query
          required: true
          description: "Type of item ('1': Product, '2': Meet, '3': Course, '4': Paid Video)."
          schema:
            type: string
            enum: ["1", "2", "3", "4"]
      responses:
        "200":
          description: Calculated item price.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/CartItemsResponseData" # Assuming similar structure
                        nullable: true
  /api/order: # Original order endpoint
    post:
      tags:
        - 4. Merchandise (Cart & Orders)
      summary: Places an order for cart items (PhonePe INR only)
      security:
        - userAuth: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                artistId:
                  type: integer
                  nullable: true
                address:
                  $ref: "#/components/schemas/AddressDetails"
                  nullable: true
                address_id:
                  type: integer
                  nullable: true
                coupon:
                  type: string
                  nullable: true
      responses:
        "200":
          description: Order placement status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/OrderResponseData"
                        nullable: true
  /api/order-now:
    post:
      tags:
        - 4. Merchandise (Cart & Orders)
      summary: Places an order for a single product item immediately
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                quantity:
                  type: integer
                productItemId:
                  type: integer
                address:
                  $ref: "#/components/schemas/AddressDetails"
                  nullable: true
                address_id:
                  type: integer
                  nullable: true
                currency:
                  type: integer
                  default: 1
                  nullable: true
                payment_method:
                  type: integer
                  default: 2 # PayPal
                  nullable: true
                coupon:
                  type: string
                  nullable: true
              required:
                - quantity
                - productItemId
      responses:
        "200":
          description: Order placement status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/OrderResponseData" # id here is order_id
                        nullable: true
  /api/v2/order:
    post:
      tags:
        - 4. Merchandise (Cart & Orders)
      summary: Places an order for cart items (v2 - multiple payment methods)
      security:
        - userAuth: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                artistId:
                  type: integer
                  nullable: true
                address:
                  $ref: "#/components/schemas/AddressDetails"
                  nullable: true
                address_id:
                  type: integer
                  nullable: true
                payment_method:
                  type: integer
                  default: 2 # PayPal
                  nullable: true
                coupon:
                  type: string
                  nullable: true
                currency:
                  type: integer
                  default: 1
                  nullable: true
      responses:
        "200":
          description: Order placement status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/OrderResponseData" # id here is order_id
                        nullable: true
  /api/cancel-payment:
    post:
      tags:
        - 4. Merchandise (Cart & Orders) # Also used by other modules
      summary: Marks a pending payment as declined/failed
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                mTxnId:
                  type: string
                  description: Merchant Transaction ID of the payment.
              required:
                - mTxnId
      responses:
        "200":
          description: Payment cancellation status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
  /api/orders:
    get:
      tags:
        - 4. Merchandise (Cart & Orders) # Generic for all order types
      summary: Retrieves user's orders (paginated)
      security:
        - userAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 40
      responses:
        "200":
          description: List of user's orders.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/OrdersListResponseData"
                        nullable: true
  /api/v1/orders: # Seems duplicate of /orders
    get:
      tags:
        - 4. Merchandise (Cart & Orders)
      summary: Retrieves user's orders (paginated, v1)
      security:
        - userAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 40 # Default is 40 as per doc, unusual for page
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 40
      responses:
        "200":
          description: List of user's orders.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/OrdersListResponseData"
                        nullable: true
  /api/order-detail:
    get:
      tags:
        - 4. Merchandise (Cart & Orders) # Generic for all order types
      summary: Retrieves detailed information for a specific order item
      security:
        - userAuth: []
      parameters:
        - name: order_item_id
          in: query
          required: true
          description: ID of the order item (or log ID for Course/Meet/Video).
          schema:
            type: integer
        - name: order_type
          in: query
          required: true
          description: "Type of order ('PRODUCT', 'COURSE', 'MEET', 'PAID_VIDEO')."
          schema:
            type: string
            enum: [PRODUCT, COURSE, MEET, PAID_VIDEO]
      responses:
        "200":
          description: Order item details.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/OrderDetailResponseData"
                        nullable: true
  /api/cancelOrder:
    post:
      tags:
        - 4. Merchandise (Cart & Orders)
      summary: Cancels a placed merchandise order
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_id:
                  type: integer
                  description: ID of the order detail.
                order_type:
                  type: string
                  description: "Type of order ('PRODUCT')."
                  enum: [PRODUCT]
              required:
                - order_id
                - order_type
      responses:
        "200":
          description: Order cancellation status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"

  # --- 5. Karaoke (/api) ---
  /api/getKaraokeTracks/{artistId}:
    get:
      tags:
        - 5. Karaoke
      summary: Retrieves karaoke tracks for a specific artist
      security:
        - userAuth: []
      parameters:
        - name: artistId
          in: path
          required: true
          description: ID of the artist.
          schema:
            type: integer
      responses:
        "200":
          description: List of karaoke tracks.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/KaraokeTracksResponseData"
                        nullable: true
  /api/process-karaoke:
    post:
      tags:
        - 5. Karaoke
      summary: Mixes user's vocal with karaoke track
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                track: # ID of the karaoke track
                  type: integer
                file: # User's vocal recording
                  type: string
                  format: binary
              required:
                - track
                - file
      responses:
        "200":
          description: Mixed audio file stream.
          content:
            audio/mpeg:
              schema:
                type: string
                format: binary

  # --- 6. Meet the Artist (Booking) (/api) ---
  /api/register-user: # For artist meet
    post:
      tags:
        - 6. Meet the Artist (Booking)
      summary: Registers a user for an artist meet and returns slots
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                mobile:
                  type: string
                  nullable: true
                artistMeetId:
                  type: integer
                name:
                  type: string
                  nullable: true
              required:
                - artistMeetId
      responses:
        "200":
          description: Meet registration details and available slots.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/ArtistMeetRegistrationResponseData"
                        nullable: true
  /api/init-meet:
    post:
      tags:
        - 6. Meet the Artist (Booking)
      summary: Initiates payment for an artist meet slot (PhonePe INR only)
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                slotDate:
                  type: string
                  format: date # Or specific string format
                slotTime:
                  type: string # Or specific time format
                formId:
                  type: string
                artistMeetId:
                  type: integer
                address:
                  $ref: "#/components/schemas/AddressDetails"
                  nullable: true
                address_id:
                  type: integer
                  nullable: true
                coupon:
                  type: string
                  nullable: true
              required:
                - slotDate
                - slotTime
                - formId
                - artistMeetId
      responses:
        "200":
          description: Meet payment initiation status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/OrderResponseData" # Reusing structure
                        nullable: true
  /api/v2/init-meet:
    post:
      tags:
        - 6. Meet the Artist (Booking)
      summary: Initiates payment for an artist meet (v2 - multiple payment methods)
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                artistMeetId:
                  type: integer
                address:
                  $ref: "#/components/schemas/AddressDetails"
                  nullable: true
                address_id:
                  type: integer
                  nullable: true
                coupon:
                  type: string
                  nullable: true
                currency:
                  type: integer
                  default: 1
                  nullable: true
                payment_method:
                  type: integer
                  default: 2 # PayPal
                  nullable: true
              required:
                - artistMeetId
      responses:
        "200":
          description: Meet payment initiation status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/OrderResponseData" # id here is meet_log_id
                        nullable: true
  /api/meet-callback:
    post:
      tags:
        - 17. Payment Gateway Callbacks (Internal Processing) # More appropriate tag
      summary: Handles PhonePe payment callbacks for artist meets
      description: Updates payment status and schedules meeting. (Webhook)
      requestBody:
        description: PhonePe callback data.
        content:
          application/json: # Or application/x-www-form-urlencoded depending on PhonePe
            schema:
              type: object # Specific to PhonePe
      responses:
        "200":
          description: Callback processed.
          content:
            text/plain:
              schema:
                type: string
                example: OK # Or SUCCESS
  /api/cancel-meet:
    post:
      tags:
        - 6. Meet the Artist (Booking)
      summary: Cancels a pending meet payment
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                mTxnId:
                  type: string
                  description: Merchant Transaction ID of the payment.
              required:
                - mTxnId
      responses:
        "200":
          description: Meet payment cancellation status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
  /api/getArtistBookDetail/{id}:
    get:
      tags:
        - 6. Meet the Artist (Booking)
      summary: Retrieves details for a specific artist meet (with login)
      description: "Includes user's request status. Note: API doc says returns an array, likely should be a single object."
      security:
        - userAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the artist meet detail.
          schema:
            type: integer
      responses:
        "200":
          description: Artist meet details.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/ArtistMeetDetailResponseData" # This schema defines an array
                        nullable: true
  /api/getArtistBookWithoutLogin/{id}:
    get:
      tags:
        - 6. Meet the Artist (Booking)
      summary: Retrieves details for a specific artist meet (no login)
      description: "Note: API doc says returns an array, likely should be a single object."
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the artist meet detail.
          schema:
            type: integer
      responses:
        "200":
          description: Artist meet details.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/ArtistMeetDetailResponseData" # This schema defines an array
                        nullable: true

  # --- 7. Meet the Artist (Request/Approval) (/api) ---
  /api/asset-upload:
    post:
      tags:
        - 7. Meet the Artist (Request/Approval)
      summary: Uploads assets (video/audio) for a meet request
      security:
        - userAuth: []
      parameters:
        - name: id
          in: query
          required: true
          description: ID related to the asset (e.g., artist meet detail ID).
          schema:
            type: integer
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: The asset file.
              required:
                - file
      responses:
        "200":
          description: Asset upload status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      url:
                        type: string
                        format: url
                        nullable: true
  /api/meet-request: # POST submit request & GET retrieve request details
    post:
      tags:
        - 7. Meet the Artist (Request/Approval)
      summary: Submits a meet request application
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  description: Request metadata (e.g., name, phone, email, video URL).
                id:
                  type: integer
                  description: Artist meet detail ID.
              required:
                - data
                - id
      responses:
        "200":
          description: Meet request submission status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
    get: # GET retrieve request details (for artist review)
      tags:
        - 7. Meet the Artist (Request/Approval)
      summary: Retrieves meet request details for artist review
      description: Accessed via email link. `artistValidate` middleware implies role check on userAuth token.
      security:
        - userAuth: [] # Assuming artistValidate uses the standard user token with role check
      parameters:
        - name: token
          in: query
          required: true
          description: JWT token containing request ID (likely the user's auth token or a specific one).
          schema:
            type: string
        - name: id
          in: query
          required: true
          description: Request ID.
          schema:
            type: string # Or integer if it's a numerical ID
      responses:
        "200":
          description: HTML content for the accept/reject form.
          content:
            text/html:
              schema:
                type: string
  /api/reject-request-detail:
    get:
      tags:
        - 7. Meet the Artist (Request/Approval)
      summary: Retrieves details for rejecting a meet request
      description: Accessed via email link. `artistValidate` middleware.
      security:
        - userAuth: []
      parameters:
        - name: token
          in: query
          required: true
          description: JWT token containing request ID.
          schema:
            type: string
        - name: id
          in: query
          required: true
          description: Request ID.
          schema:
            type: string
      responses:
        "200":
          description: HTML content for the rejection feedback form.
          content:
            text/html:
              schema:
                type: string
  /api/reject-request: # POST actual rejection & Duplicate GET
    post:
      tags:
        - 7. Meet the Artist (Request/Approval)
      summary: Rejects a meet request (from feedback form)
      description: artistValidate middleware.
      security:
        - userAuth: []
      parameters:
        - name: token
          in: query
          required: true
          description: JWT token containing request ID.
          schema:
            type: string
        - name: id
          in: query
          required: true
          description: Request ID.
          schema:
            type: string
      requestBody:
        content:
          application/json: # Assuming form data is sent as JSON, or could be x-www-form-urlencoded
            schema:
              type: object
              properties:
                feedback:
                  type: string
                  nullable: true
                  description: Rejection feedback message.
      responses:
        "200":
          description: Request rejection status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
    get: # Duplicate GET route for reject-request
      tags:
        - 7. Meet the Artist (Request/Approval)
      summary: Rejects a meet request (GET - likely intended as POST)
      description: artistValidate middleware.
      security:
        - userAuth: []
      parameters:
        - name: token
          in: query
          required: true
          description: JWT token containing request ID.
          schema:
            type: string
        - name: id
          in: query
          required: true
          description: Request ID.
          schema:
            type: string
      responses:
        "200":
          description: Request rejection status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
  /api/accept-request: # POST actual acceptance & Duplicate GET
    post:
      tags:
        - 7. Meet the Artist (Request/Approval)
      summary: Accepts a meet request (from accept/reject form)
      description: artistValidate middleware.
      security:
        - userAuth: []
      parameters:
        - name: token
          in: query
          required: true
          description: JWT token containing request ID.
          schema:
            type: string
        - name: id
          in: query
          required: true
          description: Request ID.
          schema:
            type: string
      responses:
        "200":
          description: Request acceptance status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
    get: # Duplicate GET route for accept-request
      tags:
        - 7. Meet the Artist (Request/Approval)
      summary: Accepts a meet request (GET - likely intended as POST)
      description: artistValidate middleware.
      security:
        - userAuth: []
      parameters:
        - name: token
          in: query
          required: true
          description: JWT token containing request ID.
          schema:
            type: string
        - name: id
          in: query
          required: true
          description: Request ID.
          schema:
            type: string
      responses:
        "200":
          description: Request acceptance status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"

  # --- 8. Courses (/api) ---
  /api/course: # GET list of courses
    get:
      tags:
        - 8. Courses
      summary: Retrieves a list of all active courses
      security:
        - userAuth: []
      responses:
        "200":
          description: List of active courses.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/CoursesResponseData"
                        nullable: true
  /api/user-course:
    get:
      tags:
        - 8. Courses
      summary: Retrieves courses purchased by the logged-in user
      security:
        - userAuth: []
      responses:
        "200":
          description: List of user's purchased courses.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/CoursesResponseData"
                        nullable: true
  /api/course-detail/{courseId}:
    get:
      tags:
        - 8. Courses
      summary: Retrieves detailed information for a specific course
      security:
        - userAuth: []
      parameters:
        - name: courseId
          in: path
          required: true
          description: ID of the course.
          schema:
            type: integer
        - name: coupon
          in: query
          description: Coupon code.
          schema:
            type: string
      responses:
        "200":
          description: Course details.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/CourseDetailResponseData"
                        nullable: true
  /api/course-callback:
    post:
      tags:
        - 17. Payment Gateway Callbacks (Internal Processing)
      summary: Handles PhonePe payment callbacks for course purchases
      description: Updates payment status and grants access. (Webhook)
      requestBody:
        description: PhonePe callback data.
        content:
          application/json:
            schema:
              type: object # Specific to PhonePe
      responses:
        "200":
          description: Callback processed.
          content:
            text/plain:
              schema:
                type: string
                example: OK
  /api/course-purchase:
    post:
      tags:
        - 8. Courses
      summary: Initiates payment for a course purchase (PhonePe INR only)
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                courseId:
                  type: integer
                coupon:
                  type: string
                  nullable: true
                address:
                  $ref: "#/components/schemas/AddressDetails"
                  nullable: true
                address_id:
                  type: integer
                  nullable: true
              required:
                - courseId
      responses:
        "200":
          description: Course purchase initiation status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/CoursePurchaseResponseData"
                        nullable: true
  /api/v2/course-purchase:
    post:
      tags:
        - 8. Courses
      summary: Initiates payment for a course purchase (v2 - multiple payment methods)
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                courseId:
                  type: integer
                coupon:
                  type: string
                  nullable: true
                address:
                  $ref: "#/components/schemas/AddressDetails"
                  nullable: true
                address_id:
                  type: integer
                  nullable: true
                payment_method:
                  type: integer
                  default: 2 # PayPal
                  nullable: true
                currency:
                  type: integer
                  default: 1
                  nullable: true
              required:
                - courseId
      responses:
        "200":
          description: Course purchase initiation status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/V2CoursePurchaseResponseData"
                        nullable: true
  /api/cancel-course-purchase:
    post:
      tags:
        - 8. Courses
      summary: Cancels a pending course purchase payment
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                mTxnId:
                  type: string
                  description: Merchant Transaction ID of the payment.
              required:
                - mTxnId
      responses:
        "200":
          description: Course purchase cancellation status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
  /api/getCourseVideo:
    get:
      tags:
        - 8. Courses
      summary: Streams a course video
      description: Requires a valid session token (JWT).
      parameters:
        - name: session
          in: query
          required: true
          description: JWT token containing video ID and session ID.
          schema:
            type: string # This is a JWT, not a typical session ID
      responses:
        "200":
          description: Course video stream.
          content:
            video/mp4:
              schema:
                type: string
                format: binary

  # --- 9. Paid Videos (/api) ---
  /api/paid-videos:
    get:
      tags:
        - 9. Paid Videos
      summary: Retrieves a list of all active paid videos (films)
      security:
        - userAuth: []
      responses:
        "200":
          description: List of paid videos.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/PaidVideosResponseData"
                        nullable: true
  /api/paid-video-details/{videoId}:
    get:
      tags:
        - 9. Paid Videos
      summary: Retrieves detailed information for a specific paid video
      security:
        - userAuth: []
      parameters:
        - name: videoId
          in: path
          required: true
          description: ID of the video.
          schema:
            type: integer
        - name: coupon
          in: query
          description: Coupon code.
          schema:
            type: string
      responses:
        "200":
          description: Paid video details.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/PaidVideoDetailResponseData"
                        nullable: true
  /api/user-subscribed-videos:
    get:
      tags:
        - 9. Paid Videos
      summary: Retrieves paid videos subscribed by the user
      security:
        - userAuth: []
      responses:
        "200":
          description: List of user's subscribed videos.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/PaidVideosResponseData" # Reusing list structure
                        nullable: true
  /api/subscribe-video:
    post:
      tags:
        - 9. Paid Videos
      summary: Initiates payment for a paid video subscription (PhonePe INR only)
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                videoId:
                  type: integer
                coupon:
                  type: string
                  nullable: true
                address:
                  $ref: "#/components/schemas/AddressDetails"
                  nullable: true
                address_id:
                  type: integer
                  nullable: true
              required:
                - videoId
      responses:
        "200":
          description: Video subscription initiation status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/SubscribeVideoResponseData"
                        nullable: true
  /api/v2/subscribe-video:
    post:
      tags:
        - 9. Paid Videos
      summary: Initiates payment for a paid video subscription (v2 - multiple payment methods)
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                videoId:
                  type: integer
                coupon:
                  type: string
                  nullable: true
                address:
                  $ref: "#/components/schemas/AddressDetails"
                  nullable: true
                address_id:
                  type: integer
                  nullable: true
                payment_method:
                  type: integer
                  default: 2 # PayPal
                  nullable: true
                currency:
                  type: integer
                  default: 1
                  nullable: true
              required:
                - videoId
      responses:
        "200":
          description: Video subscription initiation status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/V2SubscribeVideoResponseData"
                        nullable: true
  /api/paid-video-callback:
    post:
      tags:
        - 17. Payment Gateway Callbacks (Internal Processing)
      summary: Handles PhonePe payment callbacks for paid video subscriptions
      description: Updates payment status and grants access. (Webhook)
      requestBody:
        description: PhonePe callback data.
        content:
          application/json:
            schema:
              type: object # Specific to PhonePe
      responses:
        "200":
          description: Callback processed.
          content:
            text/plain:
              schema:
                type: string
                example: OK
  /api/cancel-video-purchase:
    post:
      tags:
        - 9. Paid Videos
      summary: Cancels a pending paid video purchase payment
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                mTxnId:
                  type: string
                  description: Merchant Transaction ID of the payment.
              required:
                - mTxnId
      responses:
        "200":
          description: Video purchase cancellation status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"

  # --- 10. Treasure Hunt (/api) ---
  /api/treasure/{huntId}/{coinId}:
    get:
      tags:
        - 10. Treasure Hunt
      summary: Retrieves details for a specific treasure coin
      security:
        - userAuth: []
      parameters:
        - name: huntId
          in: path
          required: true
          schema:
            type: integer
        - name: coinId
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Treasure coin details.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/TreasureCoinDetails"
                        nullable: true
  /api/treasure: # POST claim a coin
    post:
      tags:
        - 10. Treasure Hunt
      summary: Claims a treasure coin for the user
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                huntId:
                  type: integer
                coinId:
                  type: integer
              required:
                - huntId
                - coinId
      responses:
        "200":
          description: Coin claim status and updated details.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/TreasureCoinDetails" # Assuming updated details
                        nullable: true
  /api/treasure-claim:
    post:
      tags:
        - 10. Treasure Hunt
      summary: Claims a physical product reward for a completed treasure hunt
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                productItemId:
                  type: integer
                productClaimId: # ID of the treasure holding entry
                  type: integer
                address:
                  $ref: "#/components/schemas/AddressDetails"
              required:
                - productItemId
                - productClaimId
                - address
      responses:
        "200":
          description: Product claim status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
  /api/treasure-products/{artistId}:
    get:
      tags:
        - 10. Treasure Hunt
      summary: Retrieves treasure hunt reward products for an artist
      security:
        - userAuth: []
      parameters:
        - name: artistId
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: List of treasure reward products.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/TreasureProductsResponseData"
                        nullable: true

  # --- 11. Immersive Spaces (Popups/Videos) (/api) ---
  /api/popup:
    get:
      tags:
        - 11. Immersive Spaces (Popups/Videos)
      summary: Retrieves content for a specific popup
      parameters:
        - name: id
          in: query
          required: true
          description: ID of the popup.
          schema:
            type: integer
      responses:
        "200":
          description: Popup content.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/PopupContentResponseData"
                        nullable: true
  /api/video:
    get:
      tags:
        - 11. Immersive Spaces (Popups/Videos)
      summary: Retrieves details and stream URL for a specific video
      parameters:
        - name: id
          in: query
          required: true
          description: ID of the video.
          schema:
            type: integer
      responses:
        "200":
          description: Video details.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/VideoDetailsResponseData"
                        nullable: true

  # --- 12. Calendly Integration (/api) ---
  /api/getAvailableSlot:
    post:
      tags:
        - 12. Calendly Integration
      summary: Fetches available time slots for a Calendly-integrated artist meet
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                artistMeetId:
                  type: integer
              required:
                - artistMeetId
      responses:
        "200":
          description: Available Calendly slots.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/CalendlySlotDetails"
                        nullable: true
  /api/start-calendly-checkout:
    post:
      tags:
        - 12. Calendly Integration
      summary: Creates a pending checkout entry for a Calendly meet
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                artistMeetId:
                  type: integer
                coupon:
                  type: string
                  nullable: true
                email_id:
                  type: string
                  format: email
                address:
                  $ref: "#/components/schemas/AddressDetails"
                  nullable: true
                address_id:
                  type: integer
                  nullable: true
                currency:
                  type: integer
                  default: 1
                  nullable: true
                payment_method:
                  type: integer
                  default: 2 # PayPal
                  nullable: true
              required:
                - artistMeetId
                - email_id
      responses:
        "200":
          description: Calendly checkout creation status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"
  /api/start-calendly-payment:
    post:
      tags:
        - 12. Calendly Integration
      summary: Initiates payment for a Calendly meet checkout entry
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                event_id: # ID of the Calendly checkout entry
                  type: integer
              required:
                - event_id
      responses:
        "200":
          description: Calendly payment initiation status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/OrderResponseData" # id here is meet_log_id
                        nullable: true
  /api/cancel-calendly-event:
    post:
      tags:
        - 12. Calendly Integration
      summary: Cancels a Calendly event booking
      security:
        - userAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                source_id: # Calendly event type source ID
                  type: string
                email_id:
                  type: string
                  format: email
              required:
                - source_id
                - email_id
      responses:
        "200":
          description: Calendly event cancellation status.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StatusMessageResponse"

  # --- 13. Payment Gateway Webhooks (/api) ---
  /api/paypal-callback:
    post:
      tags:
        - 13. Payment Gateway Webhooks
      summary: Handles PayPal webhook notifications
      description: Processes payment status updates. (Webhook)
      requestBody:
        description: PayPal webhook data.
        content:
          application/json:
            schema:
              type: object # Specific to PayPal
      responses:
        "200":
          description: Callback processed.
          content:
            text/plain:
              schema:
                type: string
                example: SUCCESS
  /api/v2/pay-glocal-wh:
    post:
      tags:
        - 13. Payment Gateway Webhooks
      summary: Handles PayGlocal webhook notifications
      description: Processes payment status updates. (Webhook)
      requestBody:
        description: PayGlocal webhook data.
        content:
          application/json:
            schema:
              type: object # Specific to PayGlocal
      responses:
        "200":
          description: Callback processed.
          content:
            text/plain:
              schema:
                type: string
                example: SUCCESS
  /api/v2/pay-wh: # Razorpay
    post:
      tags:
        - 13. Payment Gateway Webhooks
      summary: Handles Razorpay webhook notifications
      description: Processes payment status updates. (Webhook)
      requestBody:
        description: Razorpay webhook data.
        content:
          application/json:
            schema:
              type: object # Specific to Razorpay
      responses:
        "200":
          description: Callback processed.
          content:
            text/plain:
              schema:
                type: string
                example: SUCCESS
  /api/calendly-webhook:
    post:
      tags:
        - 13. Payment Gateway Webhooks
      summary: Handles Calendly webhook notifications (invitee.created)
      description: Updates database with invite details. (Webhook)
      requestBody:
        description: Calendly webhook data.
        content:
          application/json:
            schema:
              type: object # Specific to Calendly invitee.created event
      responses:
        "200":
          description: Callback processed.
          content:
            text/plain:
              schema:
                type: string
                example: OK

  # --- 16. HTML Popup (/api) ---
  /api/getHTMLPopup:
    get:
      tags:
        - 16. HTML Popup
      summary: Retrieves raw HTML content for a specific popup ID
      parameters:
        - name: popupId
          in: query
          required: true
          description: ID of the HTML popup.
          schema:
            type: integer
      responses:
        "200":
          description: HTML content of the popup.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/HtmlPopupResponseData"
                        nullable: true

  # --- 17. Payment Gateway Callbacks (Internal Processing) (/api) ---
  /api/pay-wh: # PhonePe for merchandise (original /pay-wh)
    post:
      tags:
        - 17. Payment Gateway Callbacks (Internal Processing)
      summary: Handles PhonePe payment callbacks for merchandise orders
      description: Updates payment and order status. (Webhook)
      requestBody:
        description: PhonePe callback data for merchandise.
        content:
          application/json: # Or application/x-www-form-urlencoded
            schema:
              type: object # Specific to PhonePe
      responses:
        "200":
          description: Callback processed.
          content:
            text/plain: # As per doc, "SUCCESS (as expected by webhook)"
              schema:
                type: string
                example: SUCCESS

  # --- Admin Panel Paths ---
  /admin/login:
    post:
      tags:
        - 14. Admin Panel
      summary: Logs in an admin user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userName:
                  type: string
                  description: Admin email or mobile.
                password:
                  type: string
                  format: password
              required:
                - userName
                - password
      responses:
        "200":
          description: Admin login status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/LoginResponseData"
                        nullable: true
  /admin/google-login:
    post:
      tags:
        - 14. Admin Panel
      summary: Handles Google OAuth login for admin users
      description:
      Middleware: verifyGoogleToken.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                  description: Google ID token.
              required:
                - token
      responses:
        "200":
          description: Admin Google login status.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/LoginResponseData"
                        nullable: true
  /admin/user:
    get:
      tags:
        - 14. Admin Panel
      summary: Retrieves a paginated list of users for admin
      security:
        - adminAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 10
        - name: key
          in: query
          schema:
            type: string
            default: "user_id"
        - name: order
          in: query
          schema:
            type: string
            default: "desc"
            enum: [asc, desc]
      responses:
        "200":
          description: Paginated list of users.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      result:
                        $ref: "#/components/schemas/AdminPanelListResult" # Assuming items are UserDetails like
                        nullable: true
  /admin/karaoke:
    get:
      tags:
        - 14. Admin Panel
      summary: Retrieves paginated karaoke recordings for admin
      security:
        - adminAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 10
        - name: key
          in: query
          schema:
            type: string
            default: "user_id"
        - name: order
          in: query
          schema:
            type: string
            default: "desc"
            enum: [asc, desc]
        - name: fromDate
          in: query
          schema:
            type: string
            format: date
            description: YYYY-MM-DD
        - name: toDate
          in: query
          schema:
            type: string
            format: date
            description: YYYY-MM-DD
        - name: artistId
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: Paginated list of karaoke recordings.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      result:
                        $ref: "#/components/schemas/AdminPanelListResult" # Items are karaoke recordings
                        nullable: true
  /admin/orderHistory:
    get:
      tags:
        - 14. Admin Panel
      summary: Retrieves paginated order history for admin
      security:
        - adminAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 10
        - name: key
          in: query
          schema:
            type: string
            default: "created"
        - name: order
          in: query
          schema:
            type: string
            default: "desc"
            enum: [asc, desc]
        - name: fromDate
          in: query
          schema:
            type: string
            format: date
        - name: toDate
          in: query
          schema:
            type: string
            format: date
        - name: artistId
          in: query
          schema:
            type: integer
        - name: filterBy # Status filter
          in: query
          schema:
            type: string
            example: "PLACED"
      responses:
        "200":
          description: Paginated list of orders.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      result:
                        $ref: "#/components/schemas/AdminPanelListResult" # Items are orders
                        nullable: true
  /admin/cartHistory:
    get:
      tags:
        - 14. Admin Panel
      summary: Retrieves paginated user shopping carts for admin
      security:
        - adminAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 10
        - name: key
          in: query
          schema:
            type: string
            default: "cart_id"
        - name: order
          in: query
          schema:
            type: string
            default: "desc"
            enum: [asc, desc]
      responses:
        "200":
          description: Paginated list of carts.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      result:
                        $ref: "#/components/schemas/AdminPanelListResult" # Items are cart details
                        nullable: true
  /admin/orderDetail: # Admin get order detail - MISSING adminValidate as per doc
    get:
      tags:
        - 14. Admin Panel
      summary: Retrieves order detail for admin
      (SECURITY RISK: Missing adminValidate)
      parameters:
        - name: order_item_id
          in: query
          required: true
          schema:
            type: integer
        - name: order_type
          in: query
          required: true
          schema:
            type: string
            enum: [PRODUCT, COURSE, MEET, PAID_VIDEO]
      responses:
        "200":
          description: Order item details.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/OrderDetailResponseData"
                        nullable: true
  /admin/user-report: # Admin user report - MISSING adminValidate as per doc
    get:
      tags:
        - 14. Admin Panel
      summary: Generates and downloads user Excel report
      (SECURITY RISK: Missing adminValidate)
      parameters:
        - name: fromDate
          in: query
          schema:
            type: string
            format: date
        - name: toDate
          in: query
          schema:
            type: string
            format: date
        - name: clientId
          in: query
          schema:
            type: integer
        - name: loginType
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: Excel file download.
          content:
            application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
              schema:
                type: string
                format: binary
  /admin/karaoke-report: # Admin karaoke report - MISSING adminValidate as per doc
    get:
      tags:
        - 14. Admin Panel
      summary: Generates and downloads karaoke Excel report
      (SECURITY RISK: Missing adminValidate)
      parameters:
        - name: fromDate
          in: query
          schema:
            type: string
            format: date
        - name: toDate
          in: query
          schema:
            type: string
            format: date
        - name: artistId
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: Excel file download.
          content:
            application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
              schema:
                type: string
                format: binary
  /admin/order-report: # Admin order report - MISSING adminValidate as per doc
    get:
      tags:
        - 14. Admin Panel
      summary: Generates and downloads order Excel report
      (SECURITY RISK: Missing adminValidate)
      parameters:
        - name: fromDate
          in: query
          schema:
            type: string
            format: date
        - name: toDate
          in: query
          schema:
            type: string
            format: date
        - name: artistId
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: Excel file download.
          content:
            application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
              schema:
                type: string
                format: binary
  /admin/user-filter: # Admin get user filter options
    get:
      tags:
        - 14. Admin Panel
      summary: Retrieves filter options for user reports/listings
      security:
        - adminAuth: []
      responses:
        "200":
          description: Filter options.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/AdminFilterOptionsData"
                        nullable: true

  # --- New Dashboard Paths ---
  /dashboard/login:
    post:
      tags:
        - 15. New Dashboard
      summary: Logs in a dashboard user (Admin or Artiste)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  format: password
              required:
                - email
                - password
      responses:
        "200":
          description: Dashboard login success.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DashboardLoginResponse"
  /dashboard/logout:
    post:
      tags:
        - 15. New Dashboard
      summary: Logs out the dashboard user
      security:
        - dashboardAuth: []
      responses:
        "200":
          description: Logout success.
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
  /dashboard/getUsers:
    get:
      tags:
        - 15. New Dashboard
      summary: Retrieves paginated list of users for dashboard
      security:
        - dashboardAuth: [] # Also adminOnly (role check)
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 10
        - name: key
          in: query
          schema:
            type: string
            default: "user_id"
        - name: order
          in: query
          schema:
            type: string
            default: "desc"
            enum: [asc, desc]
        - name: artist_id # Not used in query per doc, but present in controller logic
          in: query
          schema:
            type: integer
          description: "Artist ID to filter by (intended, not implemented in current query as per docs)."
      responses:
        "200":
          description: Paginated list of users.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      result:
                        $ref: "#/components/schemas/DashboardPaginatedUsers"
                        nullable: true
  /dashboard/getKaraoke:
    get:
      tags:
        - 15. New Dashboard
      summary: Retrieves paginated karaoke recordings for dashboard
      security:
        - dashboardAuth: [] # Also adminOnly
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 10
        - name: key
          in: query
          schema:
            type: string
            default: "user_id"
        - name: order
          in: query
          schema:
            type: string
            default: "desc"
            enum: [asc, desc]
        - name: fromDate
          in: query
          schema:
            type: string
            format: date
        - name: toDate
          in: query
          schema:
            type: string
            format: date
        - name: artist_id
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: Paginated karaoke recordings.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      result:
                        $ref: "#/components/schemas/DashboardPaginatedKaraoke"
                        nullable: true
  /dashboard/getOrders:
    get:
      tags:
        - 15. New Dashboard
      summary: Retrieves paginated sales items for dashboard
      security:
        - dashboardAuth: [] # Also adminOnly
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 10
        - name: key
          in: query
          schema:
            type: string
            default: "created"
        - name: order
          in: query
          schema:
            type: string
            default: "desc"
            enum: [asc, desc]
        - name: artist_id
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: Paginated sales items.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      result:
                        $ref: "#/components/schemas/DashboardPaginatedOrders"
                        nullable: true
  /dashboard/getTopFans:
    get:
      tags:
        - 15. New Dashboard
      summary: Retrieves list of top fans for dashboard
      security:
        - dashboardAuth: [] # Also adminOnly
      parameters:
        - name: timeline
          in: query
          schema:
            type: string
            default: "weekly"
            enum: [weekly, monthly, yearly]
        - name: artist_id
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: List of top fans.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      result:
                        $ref: "#/components/schemas/TopFansResponseData"
                        nullable: true
  /dashboard/getTopSongs:
    get:
      tags:
        - 15. New Dashboard
      summary: Retrieves list of top karaoke songs for dashboard
      security:
        - dashboardAuth: [] # Also adminOnly
      parameters:
        - name: fromDate
          in: query
          schema:
            type: string
            format: date
        - name: toDate
          in: query
          schema:
            type: string
            format: date
        - name: artist_id
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
            default: 5
      responses:
        "200":
          description: List of top songs.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      result:
                        $ref: "#/components/schemas/TopSongsResponseData"
                        nullable: true
  /dashboard/getTimeSpent:
    get:
      tags:
        - 15. New Dashboard
      summary: Retrieves average session duration from Google Analytics
      security:
        - dashboardAuth: [] # Also adminOnly
      parameters:
        - name: startDate
          in: query
          schema:
            type: string
            default: "30daysAgo"
        - name: endDate
          in: query
          schema:
            type: string
            default: "today"
        - name: compareStartDate
          in: query
          schema:
            type: string
            default: "60daysAgo"
        - name: compareEndDate
          in: query
          schema:
            type: string
            default: "31daysAgo"
        - name: artist_id
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: Average session duration data.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      result:
                        $ref: "#/components/schemas/AnalyticsTrendData"
                        nullable: true
  /dashboard/getVisitsData:
    get:
      tags:
        - 15. New Dashboard
      summary: Retrieves session count data from Google Analytics
      security:
        - dashboardAuth: [] # Also adminOnly
      parameters:
        - name: startDate
          in: query
          schema:
            type: string
            default: "30daysAgo"
        - name: endDate
          in: query
          schema:
            type: string
            default: "today"
        - name: compareStartDate
          in: query
          schema:
            type: string
            default: "60daysAgo"
        - name: compareEndDate
          in: query
          schema:
            type: string
            default: "31daysAgo"
        - name: artist_id
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: Session count data.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      result:
                        $ref: "#/components/schemas/AnalyticsTrendData" # Reusing structure, total instead of average
                        nullable: true
  /dashboard/getCountriesData:
    get:
      tags:
        - 15. New Dashboard
      summary: Retrieves session and revenue data by country from Google Analytics
      security:
        - dashboardAuth: [] # Also adminOnly
      parameters:
        - name: startDate
          in: query
          schema:
            type: string
            default: "30daysAgo"
        - name: endDate
          in: query
          schema:
            type: string
            default: "today"
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 10
        - name: artist_id
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: Country-wise session and revenue data.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      result:
                        $ref: "#/components/schemas/CountriesDataResult"
                        nullable: true
  /dashboard/getGenderData:
    get:
      tags:
        - 15. New Dashboard
      summary: Retrieves user gender distribution from Google Analytics
      security:
        - dashboardAuth: [] # Also adminOnly
      parameters:
        - name: startDate
          in: query
          schema:
            type: string
            default: "30daysAgo"
        - name: endDate
          in: query
          schema:
            type: string
            default: "today"
        - name: artist_id
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: Gender distribution data.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      result:
                        $ref: "#/components/schemas/GenderDataResult"
                        nullable: true
  /dashboard/getDeviceData:
    get:
      tags:
        - 15. New Dashboard
      summary: Retrieves user device and browser data from Google Analytics
      security:
        - dashboardAuth: [] # Also adminOnly
      parameters:
        - name: startDate
          in: query
          schema:
            type: string
            default: "30daysAgo"
        - name: endDate
          in: query
          schema:
            type: string
            default: "today"
        - name: artist_id
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: Device and browser data.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      result:
                        $ref: "#/components/schemas/DeviceDataResult"
                        nullable: true
  /dashboard/getTotalRevenue:
    get:
      tags:
        - 15. New Dashboard
      summary: Retrieves total revenue and order count data
      security:
        - dashboardAuth: [] # Also adminOnly
      parameters:
        - name: timeline
          in: query
          schema:
            type: string
            default: "weekly"
            enum: [weekly, monthly, yearly]
        - name: artist_id
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: Total revenue and order data.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      result:
                        $ref: "#/components/schemas/TotalRevenueResult"
                        nullable: true
  /dashboard/user-report: # Dashboard user report - MISSING auth as per doc
    get:
      tags:
        - 15. New Dashboard
      summary: Generates and downloads user Excel report for dashboard
      (SECURITY RISK: Missing auth)
      parameters:
        - name: fromDate
          in: query
          schema:
            type: string
            format: date
        - name: toDate
          in: query
          schema:
            type: string
            format: date
        - name: clientId
          in: query
          schema:
            type: integer
        - name: loginType
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: Excel file download.
          content:
            application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
              schema:
                type: string
                format: binary
  /dashboard/karaoke-report: # Dashboard karaoke report - MISSING auth as per doc
    get:
      tags:
        - 15. New Dashboard
      summary: Generates and downloads karaoke Excel report for dashboard
      (SECURITY RISK: Missing auth)
      parameters:
        - name: fromDate
          in: query
          schema:
            type: string
            format: date
        - name: toDate
          in: query
          schema:
            type: string
            format: date
        - name: artistId
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: Excel file download.
          content:
            application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
              schema:
                type: string
                format: binary
  /dashboard/getTrendingProducts:
    get:
      tags:
        - 15. New Dashboard
      summary: Retrieves list of top-selling products for dashboard
      security:
        - dashboardAuth: [] # Also adminOnly
      parameters:
        - name: timeline
          in: query
          schema:
            type: string
            default: "weekly"
            enum: [weekly, monthly, yearly]
        - name: artist_id
          in: query
          schema:
            type: integer
      responses:
        "200":
          description: List of trending products.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/StatusMessageResponse"
                  - type: object
                    properties:
                      result:
                        $ref: "#/components/schemas/TrendingProductsResult"
                        nullable: true
