import { Pool } from 'pg';
import logger from '../logger';
import config from './db_config';

const pool = new Pool({
    user: config.PG_USER,
    host: config.HOST,
    database: config.DATABASE,
    password: config.PASSWORD,
    port: Number(config.DB_PORT),
});

export async function initDB(): Promise<Pool> {
    try {
        await pool.connect();
        logger.info('Connection to the DB is success');
        return pool;
    } catch (error) {
        logger.error('Couldn\'t establish a connection to the DB');
        return Promise.reject({
            message: 'Connection error',
            rawError: error,
        });
    }
}

export async function query(text: string, params: any[]) {
    const res = await pool.query(text, params);
    return res;
};
