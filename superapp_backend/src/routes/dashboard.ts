import { adminOnly, auth } from "../dashboard/auth";
import { dashboardLogin, getUser, logout } from "../dashboard";
import express from "express";
import { getKaraokeReport, getUserReport } from "../dashboard/report";
import {
  getSaleData,
  getTopFans,
  getTotalRevenue,
  getTrendingProducts,
} from "../dashboard/order";
import {
  getCountriesData,
  getDeviceCategoryData,
  getGenderData,
  getTimeSpentData,
  getUserAcquisitionData,
  getVisitsData,
} from "../dashboard/analytics";
import { getKaraoke, getTopSongs } from "../dashboard/karaoke";

const router = express.Router();

router.post("/login", dashboardLogin);
router.post("/logout", logout);

// List
router.get("/getUsers", auth, adminOnly, getUser);
router.get("/getKaraoke", auth, adminOnly, getKaraoke);
router.get("/getOrders", auth, adminOnly, getSaleData);
router.get("/getTopFans", auth, adminOnly, getTopFans);
router.get("/getTopSongs", auth, adminOnly, getTopSongs);

// Analytics
router.get("/getTimeSpent", auth, adminOnly, getTimeSpentData);
router.get("/getVisitsData", auth, adminOnly, getVisitsData);
router.get("/getCountriesData", auth, adminOnly, getCountriesData);
router.get("/getGenderData", auth, adminOnly, getGenderData);
router.get("/getDeviceData", auth, adminOnly, getDeviceCategoryData);
router.get("/getUserAcquisitionData", auth, adminOnly, getUserAcquisitionData);

// Chart
router.get("/getTotalRevenue", auth, adminOnly, getTotalRevenue);

// Report
router.get("/user-report", getUserReport);
router.get("/karaoke-report", getKaraokeReport);
router.get("/getTrendingProducts", auth, adminOnly, getTrendingProducts);

export default router;
