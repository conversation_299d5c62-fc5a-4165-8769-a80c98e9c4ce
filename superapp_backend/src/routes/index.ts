import {
  artistValidate,
  optionalValidate,
  validate,
  verifyGoogleToken,
} from "../middleware";
import {
  checkUserExist,
  firebaseUser,
  forgotPassword,
  googleUser,
  login,
  register,
  sendOtp,
  updatePassword,
  verifyOtp,
} from "../controller/auth";
import express from "express";
import { upload } from "../middleware/multer";

import { getKaraokeTracks, karaokeMix } from "../controller/karaoke";
import {
  updateCart,
  getProductDetails,
  getProducts,
  getUserCart,
  getAddress,
  getAddressAll,
  getUserProfile,
  addAddress,
  placeOrder,
  paymentCallback,
  paymentCallbackV2,
  orderList,
  orderDetail,
  bulkUpdateCart,
  popup,
  video,
  updateOrderStatusadmin,
  cancelPayment,
  placeOrderV2,
  orderNow,
  newOrderList,
  postOrderStatusCancel,
  getUserCartSingleCheckout,
  insertUserProfile,
  updateUserProfile,
  updateAddress,
  deleteAddress,
  updateUserProfilePic,
  getPaymentGateways,
} from "../controller/merch";
import { getOrderReport } from "../services/admin";
import {
  cancelPaymentMeet,
  getArtistBook,
  getArtistBookWithoutLogin,
  initMeet,
  initMeetV2,
  meetPayment,
  meetPaymentV2,
  registerUserMeet,
} from "../controller/meet";
import {
  cancelPurchase,
  coursePurchaseCallback,
  coursePurchaseCallbackV2,
  getCourses,
  getCoursesDetail,
  getUserCourses,
  processStreaming,
  purchaseCourse,
  purchaseCourseV2,
} from "../controller/course";
import {
  acceptRequest,
  rejectRequest,
  uploadAssets,
  userMeetRequest,
  userMeetRequestDetail,
  userRejectRequestDetail,
} from "../controller/meet_auth";
import {
  cancelVideoPurchase,
  getUserVideos,
  getVideoDetails,
  getVideos,
  subscribeVideo,
  subscribeVideoV2,
  videoTxnCallback,
  videoTxnCallbackV2,
} from "../controller/paid_video";
import * as treasure from "../controller/treasure";
import { getHTMLPopup } from "../controller/popup";
import {
  addWishList,
  getMarketProductCategories,
  getMarketProductList,
  removeWishList,
} from "../marketplace";
import {
  paygLocalCallback,
  paypalCallBack,
  razorPayCallBack,
  phonePeCallback
} from "../controller/webhook";
import { verifyFirebaseToken } from "../services/firebase_auth";
import { calendlyCallback, getAvailableSlots, startCheckout, calendlyMeetPayment, cancelCalendlyEvent } from "../controller/calendly";
import { processAndUploadFolderVideos } from "../services/m3u8UploadToAwsBucket";
import testRoutes from "./test";

const router = express.Router();

const uploadMiddleware = upload.single(
  "file"
) as unknown as express.RequestHandler;

// User
router.post("/check-user", checkUserExist);
router.post("/user", register);
router.post("/login", login);
router.post("/google-user", verifyGoogleToken, googleUser);
router.post("/facebook-user", verifyFirebaseToken, firebaseUser);
router.post("/twitter-user", verifyFirebaseToken, firebaseUser);
router.post("/firebase-user", verifyFirebaseToken, firebaseUser);
// Password
router.post("/forgot-password", forgotPassword);
router.post("/update-password", updatePassword);

// OTP
router.post("/send-otp", sendOtp);
router.post("/verify-otp", verifyOtp);

// Karaoke

router.get("/getKaraokeTracks/:artistId", validate, getKaraokeTracks);
router.post("/process-karaoke", validate, uploadMiddleware, karaokeMix);

// Marketplace

router.get("/allProducts", optionalValidate, getMarketProductList);
router.get("/allCategories", getMarketProductCategories);
router.post("/addWishlist", validate, addWishList);
router.post("/removeWishlist", validate, removeWishList);

//Merch
router.get("/products/:artistId", getProducts);
router.get("/product-details/:productId", getProductDetails);
router.post("/cart", validate, updateCart);
router.post("/bulk-cart", validate, bulkUpdateCart);
router.get("/cart/:artistId", validate, getUserCart);
// This one just handles coupon-code checking
router.get("/cartSingle", validate, getUserCartSingleCheckout);
router.get("/address", validate, getAddress);

router.get("/getAddressAll", validate, getAddressAll);
router.post("/updateAddress", validate, updateAddress);
router.post("/deleteAddress", validate, deleteAddress);
router.get("/getUserProfile", validate, getUserProfile);
router.post("/insertUserProfile", validate, insertUserProfile);
router.post("/updateUserProfile", validate, updateUserProfile);
router.post(
  "/updateUserProfilePic",
  validate,
  uploadMiddleware,
  updateUserProfilePic
);

router.get("/payment-gateways", validate, getPaymentGateways);

router.post("/address", validate, addAddress);
// /order is not being used any where, just check once and reomve it
router.post("/order", validate, placeOrder);
router.post("/order-now", validate, orderNow);
router.post("/v2/order", validate, placeOrderV2);
router.post("/cancel-payment", validate, cancelPayment);
router.post("/pay-wh", paymentCallback);
router.get("/orders", validate, orderList);
router.get("/v1/orders", validate, newOrderList);
router.get("/order-detail", validate, orderDetail);
router.get("/popup", popup);
router.get("/video", video);
router.post("/update-order", uploadMiddleware, updateOrderStatusadmin);
router.post("/cancelOrder", validate, postOrderStatusCancel);
// Upload m3u8 ot AWS
router.post("/upload-videos-m3u8", processAndUploadFolderVideos);

//Meets
router.post("/register-user", validate, registerUserMeet);
router.post("/init-meet", validate, initMeet);
router.post("/v2/init-meet", validate, initMeetV2);
// Need to remove the /meet-callback here, coz we are not using it anymore 
// but need to confirm if its not being used anywhere other than phonePe callback
router.post("/meet-callback", meetPayment);
// router.post('/v2/meet-callback', meetPaymentV2)
router.post("/cancel-meet", validate, cancelPaymentMeet);
router.get("/getArtistBookDetail/:id", validate, getArtistBook);
router.get("/getArtistBookWithoutLogin/:id", getArtistBookWithoutLogin);

//calendly

router.post('/getAvailableSlot', validate, getAvailableSlots);
router.post('/start-calendly-checkout', validate, startCheckout)
router.post('/start-calendly-payment', validate, calendlyMeetPayment)
router.post('/cancel-calendly-event', validate, cancelCalendlyEvent)

// router.get("/checkAvailable", checkAvailable);
// router.get("/checkingUser", checkingUser);
// router.post("/register-user", validate, registerUserMeet1);
// router.post("/init-meet", validate, initMeet1);
// router.post("/v2/init-meet", validate, initMeetV21);
// router.post("/meet-callback", meetPayment1);

// router.post('/v2/meet-callback', meetPaymentV2)
// router.post("/cancel-meet", validate, cancelPaymentMeet1);
// router.get("/getArtistBookDetail/:id", validate, getArtistBook);
// router.get(
//   "/callingArtistDataViaSource/:source_id",
//   callingArtistDataViaSource
// );

//Meet Auth
router.post("/asset-upload", validate, uploadMiddleware, uploadAssets);
// this is being called to send mail rn
router.post("/meet-request", validate, userMeetRequest);
router.get("/meet-request", artistValidate, userMeetRequestDetail);
router.get("/reject-request-detail", artistValidate, userRejectRequestDetail);
router.post("/reject-request", artistValidate, rejectRequest);
router.post("/accept-request", artistValidate, acceptRequest);
router.get("/reject-request", artistValidate, rejectRequest);
router.get("/accept-request", artistValidate, acceptRequest);

//Report
router.get("/report", getOrderReport);

//Course
router.get("/course", validate, getCourses);
router.get("/user-course", validate, getUserCourses);
router.get("/course-detail/:courseId", validate, getCoursesDetail);
router.post("/course-callback", coursePurchaseCallback);
router.post("/course-purchase", validate, purchaseCourse);
router.post("/v2/course-purchase", validate, purchaseCourseV2);
router.post("/cancel-course-purchase", validate, cancelPurchase);

//PaidVideos
router.get("/paid-videos", validate, getVideos);
router.get("/paid-video-details/:videoId", validate, getVideoDetails);
router.get("/user-subscribed-videos", validate, getUserVideos);
router.post("/subscribe-video", validate, subscribeVideo);
router.post("/v2/subscribe-video", validate, subscribeVideoV2);
router.post("/paid-video-callback", videoTxnCallback);
router.post("/cancel-video-purchase", validate, cancelVideoPurchase);

//Treasure
// router.get('/treasure', validate, treasure.getTreasures)
router.get("/treasure/:huntId/:coinId", validate, treasure.getCoin);
router.post("/treasure", validate, treasure.claimCoin);
router.post("/treasure-claim", validate, treasure.claimProduct);
router.get(
  "/treasure-products/:artistId",
  validate,
  treasure.getTreasureProducts
);

router.get("/getCourseVideo", processStreaming);

// HTML Popup
router.get("/getHTMLPopup", getHTMLPopup);

router.post(
  "/paypal-callback",
  paypalCallBack,
  paymentCallbackV2,
  meetPaymentV2,
  coursePurchaseCallbackV2,
  videoTxnCallbackV2
);
// router.get(
//   "/v2/pay-glocal-wh",
//   paygLocalCallback
//   // paymentCallbackV2,
//   // meetPaymentV2,
//   // coursePurchaseCallbackV2,
//   // videoTxnCallbackV2
// );

router.post(
  "/v2/pay-glocal-wh",
  paygLocalCallback,
  paymentCallbackV2,
  meetPaymentV2,
  coursePurchaseCallbackV2,
  videoTxnCallbackV2
);
router.post(
  "/v2/pay-wh",
  razorPayCallBack,
  paymentCallbackV2,
  meetPaymentV2,
  coursePurchaseCallbackV2,
  videoTxnCallbackV2
); //razorpay callback

router.post(
  "/v2/phonepe-callback",
  phonePeCallback,
  paymentCallbackV2,
  meetPaymentV2,
  coursePurchaseCallbackV2,
  videoTxnCallbackV2
); //phonepe callback

router.post(
  "/calendly-webhook",
  calendlyCallback
); //Calendly callback;

// Test routes
router.use("/test", testRoutes);


export default router;
