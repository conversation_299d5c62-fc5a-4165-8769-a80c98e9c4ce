import { getCartHistory, getOrderHistory, orderDetail } from '../admin/merch';
import { getKaraoke } from '../admin/karaoke';
import { getUser } from '../admin/users';
import { adminLogin } from '../admin/auth';
import { adminValidate, verifyGoogleToken } from '../middleware';

import express from 'express';
import { getUserFilter, getUserReport,getOrderReportDwnl, getKaraokeReport } from '../services/admin';

const router = express.Router();

// User
router.get('/user', adminValidate,getUser);

// Karaoke
router.get('/karaoke', adminValidate,getKaraoke);

// Merch
router.get('/orderHistory', adminValidate,getOrderHistory);
router.get('/cartHistory', adminValidate,getCartHistory);
router.get('/orderDetail',orderDetail);


// Login
router.post('/login', adminLogin)
router.post('/google-login', verifyGoogleToken, adminLogin)

router.get('/user-report', getUserReport)
router.get('/karaoke-report', getKaraokeReport)
router.get('/order-report', getOrderReportDwnl)
router.get('/user-filter', getUserFilter)

export default router;
