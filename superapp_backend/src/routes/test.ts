import express from "express";
import { query } from "../db";
import { generatePDF } from "../services/pdf";
import { 
    courseOrderQuery, 
    meetOrderDetailPdfQuery, 
    orderInvoiceDetailByIdQuery, 
    paidVidOrderQuery, 
    userAddressByIdQuery 
} from "../common/db_constants";

const router = express.Router();

type OrderType = 'MEET' | 'COURSE' | 'PAID_VIDEO' | 'PRODUCT';

interface PdfRequest {
    address_id: string;
    id: string;
    type: OrderType;
}

/**
 * Test endpoint for generating PDFs for different order types
 * @route POST /test/generate-invoice
 * @param {string} address_id - Customer address ID
 * @param {string} id - Order ID
 * @param {string} type - Order type (MEET, COURSE, PAID_VIDEO, PRODUCT)
 * @returns {Object} PDF URL and success status
 */
router.post("/generate-invoice", async (req: express.Request<{}, {}, PdfRequest>, res: express.Response) => {
    try {
        const { address_id, id, type } = req.body;

        // Validate required fields
        if (!address_id || !id || !type) {
            return res.status(400).json({
                success: false,
                error: "Missing required fields: address_id, id, and type are required"
            });
        }

        // Map order type to corresponding database query
        const queryMap: Record<OrderType, string> = {
            'MEET': meetOrderDetailPdfQuery,
            'COURSE': courseOrderQuery,
            'PAID_VIDEO': paidVidOrderQuery,
            'PRODUCT': orderInvoiceDetailByIdQuery
        };

        // Type assertion to ensure type is OrderType
        const orderType = type as OrderType;
        const dbQuery = queryMap[orderType];

        if (!dbQuery) {
            return res.status(400).json({
                success: false,
                error: "Invalid order type. Must be one of: MEET, COURSE, PAID_VIDEO, PRODUCT"
            });
        }

        // Get order details from database
        const orderDetail = await query(dbQuery, [id]);
        if (!orderDetail.rows[0]) {
            return res.status(404).json({ 
                success: false, 
                error: "Order not found" 
            });
        }

        // Get customer details from database
        const customerDetail = await query(userAddressByIdQuery, [address_id]);
        if (!customerDetail.rows[0]) {
            return res.status(404).json({ 
                success: false, 
                error: "Customer address not found" 
            });
        }

        // Generate PDF
        const pdfUrl = await generatePDF(
            orderDetail.rows[0], 
            customerDetail.rows[0], 
            "Placed", 
            true
        );

        return res.json({ 
            success: true, 
            pdfUrl,
            orderType: type,
            orderId: id
        });

    } catch (error: unknown) {
        console.error("Error generating PDF:", error);
        return res.status(500).json({ 
            success: false, 
            error: "Failed to generate PDF",
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});

export default router; 