import { sendEmail } from "../services/mailer";
import {
  checkCredentialParams,
  comparePasswprd,
  createJ<PERSON>TToken,
  handleResponse,
  hashPassword,
} from "../common";
import {
  LOGIN_METHODS,
  OTP_CONFIG,
  OTP_TYPES,
  REGEX,
  USER_TYPES,
  cleanUserName,
  getForgotBody,
  getForgotSubject,
  getSignupSubject,
  getSignupVerificationBody,
} from "../common/constants";
import {
  checkUserEmail,
  checkUserMobile,
  checkUsername,
  findUser,
  getOtp,
  insertUser,
  saveOtp,
  updateNewPassword,
  updateOtpAttempt,
  updateOtpVerified,
  upsertUser,
} from "../services/auth";
import { USER } from "../types/db_types";
import { sendMobileOtp } from "../services/webhooks";
import { query } from "../db";
import { checkUserEmailMobileQuery } from "../common/db_constants";
import logger from "../logger";

export const checkUserExist = async (req: any, res: any) => {
  let response: any = {};
  const { email, mobile, user_name } = req.body;
  try {
    if (!email && !mobile && !user_name) {
      response = {
        status: false,
        message: `Invalid params`,
      };
      return handleResponse(res, response, 400);
    }

    if (email) {
      if (!REGEX.EMAIL.test(email)) {
        response = {
          status: false,
          message: `Invalid email id`,
        };
        return handleResponse(res, response, 400);
      }

      const userEmailExists = await checkUserEmail(email);

      response = {
        status: false,
        message: `Success`,
        exists: userEmailExists,
      };
      return handleResponse(res, response, 200);
    }

    if (mobile) {
      if (!REGEX.MOBILE.test(mobile)) {
        response = {
          status: false,
          message: `Invalid Mobile Number`,
        };
        return handleResponse(res, response, 400);
      }

      const userMobileExists = await checkUserMobile(mobile);

      response = {
        status: false,
        message: `Success`,
        exists: userMobileExists,
      };
      return handleResponse(res, response, 200);
    }

    if (user_name) {
      const userNameExists = await checkUsername(user_name);

      response = {
        status: false,
        message: `Success`,
        exists: userNameExists,
      };
      return handleResponse(res, response, 200);
    }
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};

export const register = async (req: any, res: any) => {
  let response: any = {};
  let { userName, password, client_id = 2 } = req.body;
  userName = cleanUserName(userName);
  const email = REGEX.MOBILE.test(userName) ? null : userName;
  const mobile = REGEX.MOBILE.test(userName) ? "91" + userName : null;
  try {
    if (!email && !mobile) {
      response = {
        status: false,
        message: `Both mobile and email fields cannot be empty.`,
      };
      return handleResponse(res, response, 400);
    }

    if (!client_id) {
      response = {
        status: false,
        message: `Invalid Params`,
      };
      return handleResponse(res, response, 400);
    }

    if (email && !REGEX.EMAIL.test(email)) {
      response = {
        status: false,
        message: `Invalid email id`,
      };
      return handleResponse(res, response, 400);
    }

    if (mobile && !REGEX.MOBILE.test(mobile)) {
      response = {
        status: false,
        message: `Invalid mobile number`,
      };
      return handleResponse(res, response, 400);
    }

    // if (!user_name || !password) {
    //     response = {
    //         status: false,
    //         message: `Invalid params`
    //     }
    //     return handleResponse(res, response, 400);
    // }

    // const userNameExists = await checkUsername(user_name);

    // if (userNameExists) {
    //     response = {
    //         status: false,
    //         message: `This username is already taken`
    //     }
    //     return handleResponse(res, response, 400);
    // }

    const userEmailExists = email ? await checkUserEmail(email) : "";

    if (userEmailExists) {
      response = {
        status: false,
        message: `This email is already taken`,
      };
      return handleResponse(res, response, 400);
    }

    const userMobExists = mobile ? await checkUserMobile(mobile) : "";

    if (userMobExists) {
      response = {
        status: false,
        message: `This mobile number is already used`,
      };
      return handleResponse(res, response, 400);
    }

    const hashedPassword = await hashPassword(password);

    let destination = null;
    let type = null;
    let login_method = null;
    if (email) {
      if (!REGEX.EMAIL.test(email)) {
        response = {
          status: false,
          message: `Invalid email id`,
        };
        return handleResponse(res, response, 400);
      }
      type = OTP_TYPES.MAIL;
      destination = email;
      login_method = LOGIN_METHODS.EMAIL;
    } else if (mobile) {
      if (!REGEX.MOBILE.test(mobile)) {
        response = {
          status: false,
          message: `Invalid mobile number`,
        };
        return handleResponse(res, response, 400);
      }
      type = OTP_TYPES.MOBILE;
      destination = mobile;
      login_method = LOGIN_METHODS.MOBILE;
    }

    if (!type) {
      response = {
        status: false,
        message: `Invalid Otp Type`,
      };
      return handleResponse(res, response, 400);
    }

    const userData: USER = {
      password_hash: hashedPassword,
      email: email || null,
      mobile_number: mobile || null,
      client_id: Number(client_id),
      login_method: login_method!,
    };

    const user = await insertUser(userData);

    if (!user.status) {
      response = {
        status: false,
        message: `User not created`,
      };
      return handleResponse(res, response, 400);
    }

    let otp;
    const otpSent = await getOtp(destination, type);

    if (!otpSent) {
      otp = await saveOtp(destination, type);
    } else if (otpSent.resend_attempt >= OTP_CONFIG.MAX_RESEND) {
      response = {
        status: false,
        message: "Otp request limt exceeded Try after sometime",
      };
      return handleResponse(res, response, 400);
    } else {
      await updateOtpAttempt(destination, type, 0, 1);
      otp = otpSent.otp;
    }

    if (type == OTP_TYPES.MAIL) {
      const body = getSignupVerificationBody(otp);
      const subject = getSignupSubject();
      sendEmail([email], subject, body);
    } else if (type == OTP_TYPES.MOBILE) {
      await sendMobileOtp(mobile!, otp);
    }

    response = {
      status: true,
      message: `User registered successfully`,
      data: user.data,
    };

    return handleResponse(res, response, 200);
  } catch (error) {
    logger.error(`Error on register api : `, error);
    response = {
      status: false,
      message: `Request failed`,
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};

export const sendOtp = async (req: any, res: any) => {
  let response: any = {};
  let { userName } = req.body;
  userName = cleanUserName(userName);
  const email = REGEX.MOBILE.test(userName) ? null : userName;
  const mobile = REGEX.MOBILE.test(userName) ? "91" + userName : null;
  if (!email && !mobile) {
    response = {
      status: false,
      message: `Both mobile and email fields cannot be empty.`,
    };
    return handleResponse(res, response, 400);
  }
  try {
    const verifyData = await query(checkUserEmailMobileQuery, [email, mobile]);

    if (!verifyData.rowCount) {
      response = {
        status: true,
        message: "Email/Mobile is not registered with any account",
      };
      return handleResponse(res, response, 400);
    }
    let destination = null;
    let type = null;
    if (email) {
      if (!REGEX.EMAIL.test(email)) {
        response = {
          status: false,
          message: `Invalid email id`,
        };
        return handleResponse(res, response, 400);
      }
      type = OTP_TYPES.MAIL;
      destination = email;
    } else if (mobile) {
      if (!REGEX.MOBILE.test(mobile)) {
        response = {
          status: false,
          message: `Invalid mobile number`,
        };
        return handleResponse(res, response, 400);
      }
      type = OTP_TYPES.MOBILE;
      destination = mobile;
    }

    if (!type) {
      response = {
        status: false,
        message: `Invalid Otp Type`,
      };
      return handleResponse(res, response, 400);
    }

    let otp;
    const otpSent = await getOtp(destination, type);

    if (!otpSent) {
      otp = await saveOtp(destination, type);
    } else if (otpSent.resend_attempt >= OTP_CONFIG.MAX_RESEND) {
      response = {
        status: false,
        message: "Otp request limt exceeded Try after sometime",
      };
      return handleResponse(res, response, 400);
    } else {
      await updateOtpAttempt(destination, type, 0, 1);
      otp = otpSent.otp;
    }

    if (type == OTP_TYPES.MAIL) {
      const body = `<!DOCTYPE html><html><body><h4 style='text-align:justify'>Your Otp is - ${otp} </h4></body></html>`;
      sendEmail([email], "Email Verification", body);
    } else if (type == OTP_TYPES.MOBILE) {
      await sendMobileOtp(mobile!, otp);
    }

    response = {
      status: true,
      message: "Otp sent successfully",
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};

export const verifyOtp = async (req: any, res: any) => {
  let response: any = {};
  let { userName, otp } = req.body;
  userName = cleanUserName(userName);
  const email = REGEX.MOBILE.test(userName) ? null : userName;
  const mobile = REGEX.MOBILE.test(userName) ? "91" + userName : null;

  if ((!email || mobile) && (email || !mobile) && !otp) {
    response = {
      status: false,
      message: `Invalid Params`,
    };
    return handleResponse(res, response, 400);
  }

  try {
    const user = await findUser(USER_TYPES.USER, undefined, email, mobile);
    if (!user) {
      response = {
        status: false,
        message: `User not found.`,
      };
      return handleResponse(res, response, 400);
    }

    let destination = null;
    let type = null;
    if (email) {
      if (!REGEX.EMAIL.test(email)) {
        response = {
          status: false,
          message: `Invalid email id`,
        };
        return handleResponse(res, response, 400);
      }
      type = OTP_TYPES.MAIL;
      destination = email;
    } else if (mobile) {
      if (!REGEX.MOBILE.test(mobile)) {
        response = {
          status: false,
          message: `Invalid mobile number`,
        };
        return handleResponse(res, response, 400);
      }
      type = OTP_TYPES.MOBILE;
      destination = mobile;
    }

    if (!type) {
      response = {
        status: false,
        message: `Invalid Otp Type`,
      };
      return handleResponse(res, response, 400);
    }

    const otpSent = await getOtp(destination, type);
    if (!otpSent) {
      response = {
        status: false,
        message: "OTP Expired!",
      };
      return handleResponse(res, response, 400);
    }

    if (otpSent.attempt >= OTP_CONFIG.MAX_ATTEMPT) {
      response = {
        status: false,
        message: "Maximum attempt reached Try later",
      };
      return handleResponse(res, response, 400);
    }

    if (Number(otpSent.otp) != Number(otp)) {
      await updateOtpAttempt(destination, type, 1, 0);
      response = {
        status: false,
        message: "Incorrect OTP",
      };
      return handleResponse(res, response, 400);
    }

    await updateOtpAttempt(destination, type, 1, 0);

    await updateOtpVerified(destination, type);

    const token = createJWTToken({
      id: user.id,
      email: user.email,
      mobile: user.mobile_number,
      email_verified: user.email_verified,
      mobile_verified: user.mobile_number,
      client_id: user.client_id,
      display_name : user.display_name,
      display_pic: user.display_pic
    });

    response = {
      status: false,
      message: "Verified Successfully",
      data: {
        token,
      },
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};

export const login = async (req: any, res: any) => {
  let response: any = {};
  let { userName, password } = req.body;
  userName = cleanUserName(userName);
  const email = REGEX.MOBILE.test(userName) ? null : userName;
  const mobile = REGEX.MOBILE.test(userName) ? "91" + userName : null;

  if (!password || !checkCredentialParams([email, mobile])) {
    response = {
      status: false,
      message: `Invalid Params`,
    };
    return handleResponse(res, response, 400);
  }

  try {
    const user = await findUser(USER_TYPES.USER, undefined, email, mobile);
    if (!user) {
      response = {
        status: false,
        message: `User not found.`,
      };
      return handleResponse(res, response, 400);
    }

    const verifyPass: any = await comparePasswprd(user.password_hash, password);

    if (!verifyPass) {
      response = {
        status: false,
        message: "Invalid Credential",
      };
      return handleResponse(res, response, 400);
    }

    const token = createJWTToken({
      id: user.id,
      email: user.email,
      mobile: user.mobile_number,
      email_verified: user.email_verified,
      mobile_verified: user.mobile_number,
      client_id: user.client_id,
      display_name : user.display_name,
      display_pic: user.display_pic
    });

    response = {
      status: true,
      message: "Success",
      data: {
        token: token,
      },
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};

export const forgotPassword = async (req: any, res: any) => {
  let response: any = {};
  let { userName } = req.body;
  userName = cleanUserName(userName);
  const email = REGEX.MOBILE.test(userName) ? null : userName;
  const mobile = REGEX.MOBILE.test(userName) ? "91" + userName : null;

  try {
    const verifyData = await query(checkUserEmailMobileQuery, [email, mobile]);

    if (!verifyData.rowCount) {
      response = {
        status: true,
        message: "Email/Mobile is not found",
      };
      return handleResponse(res, response, 400);
    }

    let destination = null;
    let type = null;
    if (email) {
      if (!REGEX.EMAIL.test(email)) {
        response = {
          status: false,
          message: `Invalid email id`,
        };
        return handleResponse(res, response, 400);
      }
      type = OTP_TYPES.MAIL;
      destination = email;
    } else if (mobile) {
      if (!REGEX.MOBILE.test(mobile)) {
        response = {
          status: false,
          message: `Invalid mobile number`,
        };
        return handleResponse(res, response, 400);
      }
      type = OTP_TYPES.MOBILE;
      destination = mobile;
    }

    if (!type) {
      response = {
        status: false,
        message: `Invalid Otp Type`,
      };
      return handleResponse(res, response, 400);
    }

    let otp;
    const otpSent = await getOtp(destination, type);

    if (!otpSent) {
      otp = await saveOtp(destination, type);
    } else if (otpSent.resend_attempt >= OTP_CONFIG.MAX_RESEND) {
      response = {
        status: false,
        message: "Otp request limt exceeded Try after sometime",
      };
      return handleResponse(res, response, 400);
    } else {
      await updateOtpAttempt(destination, type, 0, 1);
      otp = otpSent.otp;
    }

    if (type == OTP_TYPES.MAIL) {
      const body = getForgotBody(otp);
      const subject = getForgotSubject();
      sendEmail([email], subject, body);
    } else if (type == OTP_TYPES.MOBILE) {
      await sendMobileOtp(mobile!, otp);
    }

    response = {
      status: true,
      message: "Otp sent successfully",
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};

export const updatePassword = async (req: any, res: any) => {
  let response: any = {};
  let { userName, otp, new_password } = req.body;
  userName = cleanUserName(userName);
  const email = REGEX.MOBILE.test(userName) ? null : userName;
  const mobile = REGEX.MOBILE.test(userName) ? "91" + userName : null;

  if (!otp && !new_password && (!email || mobile) && (email || !mobile)) {
    response = {
      status: false,
      message: `Invalid Params`,
    };
    return handleResponse(res, response, 400);
  }

  try {
    const verifyData = await query(checkUserEmailMobileQuery, [email, mobile]);

    if (!verifyData.rowCount) {
      response = {
        status: true,
        message: "Email/Mobile is not found",
      };
      return handleResponse(res, response, 400);
    }

    let destination = null;
    let type = null;
    if (email) {
      if (!REGEX.EMAIL.test(email)) {
        response = {
          status: false,
          message: `Invalid email id`,
        };
        return handleResponse(res, response, 400);
      }
      type = OTP_TYPES.MAIL;
      destination = email;
    } else if (mobile) {
      if (!REGEX.MOBILE.test(mobile)) {
        response = {
          status: false,
          message: `Invalid mobile number`,
        };
        return handleResponse(res, response, 400);
      }
      type = OTP_TYPES.MOBILE;
      destination = mobile;
    }

    if (!type) {
      response = {
        status: false,
        message: `Invalid Otp Type`,
      };
      return handleResponse(res, response, 400);
    }

    const otpSent = await getOtp(destination, type);
    if (!otpSent) {
      response = {
        status: false,
        message: "OTP Expired!",
      };
      return handleResponse(res, response, 400);
    }

    if (otpSent.attempt >= OTP_CONFIG.MAX_ATTEMPT) {
      response = {
        status: false,
        message: "Maximum attempt reached Try later",
      };
      return handleResponse(res, response, 400);
    }

    if (otpSent.otp != otp) {
      await updateOtpAttempt(destination, type, 1, 0);
      response = {
        status: false,
        message: "Incorrect OTP",
      };
      return handleResponse(res, response, 400);
    }

    await updateOtpAttempt(destination, type, 1, 0);

    await updateOtpVerified(destination, type);

    const hashedPassword = await hashPassword(new_password);

    await updateNewPassword(hashedPassword, destination);

    const user = verifyData.rows[0];

    const token = createJWTToken({
      id: user.id,
      email: user.email,
      mobile: user.mobile_number,
      email_verified: user.email_verified,
      mobile_verified: user.mobile_number,
      client_id: user.client_id,
      display_name : user.display_name,
      display_pic: user.display_pic
    });

    response = {
      status: true,
      message: "Success",
      data: {
        token,
      },
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};

export const googleUser = async (req: any, res: any) => {
  let response: any = {};
  let { email, email_verified, name, picture } = req.user;
  const { client_id = 2 } = req.body;

  try {
    email = cleanUserName(email);
    const userData: USER = {
      email: email,
      client_id: Number(client_id),
      login_method: LOGIN_METHODS.GOOGLE,
      password_hash: null,
      mobile_number: null,
      email_verified: email_verified,
      display_name: name,
      display_pic: picture,
    };
    const user = await upsertUser(userData);
    if (!user.status) {
      response = {
        status: false,
        message: `User not created`,
      };
      return handleResponse(res, response, 400);
    }
    const token = createJWTToken({
      id: user.data.id,
      email: user.data.email,
      mobile: user.data.mobile_number,
      email_verified: user.data.email_verified,
      mobile_verified: user.data.mobile_number,
      client_id: user.data.client_id,
      display_name : name,
      display_pic: picture
    });
    response = {
      status: true,
      message: `User registered successfully`,
      data: {
        user: user.data,
        token: token,
      },
    };

    return handleResponse(res, response, 200);
  } catch (error) {
    logger.error(`Error on goole-user api : `, error);
    response = {
      status: false,
      message: `Request failed`,
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};

export const firebaseUser = async (req: any, res: any) => {
  let response: any = {};
  let { name, email, profilePic, isFacebook, uid } = req.user;
  const { client_id = 2 } = req.body;
  try {
    if (email) {
      email = cleanUserName(email);
    }
    const userData: USER = {
      uid: uid,
      email: email,
      client_id: Number(client_id),
      login_method: isFacebook ? LOGIN_METHODS.FACEBOOK : LOGIN_METHODS.TWITTER,
      password_hash: null,
      mobile_number: null,
      email_verified: email ? true : false,
      display_name: name,
      display_pic: profilePic,
    };
    const user = await upsertUser(userData);
    if (!user.status) {
      response = {
        status: false,
        message: `User not created`,
      };
      return handleResponse(res, response, 400);
    }
    const token = createJWTToken({
      id: user.data.id,
      email: user.data.email,
      mobile: user.data.mobile_number,
      email_verified: user.data.email_verified,
      mobile_verified: user.data.mobile_number,
      client_id: user.data.client_id,
      avatar: user.data.display_pic,
      display_name : user.data.display_name,
      display_pic: user.data.display_pic
    });
    response = {
      status: true,
      message: `User registered successfully`,
      data: {
        user: user.data,
        token: token,
      },
    };

    return handleResponse(res, response, 200);
  } catch (error) {
    logger.error(`Error on Twitter-user api : `, error);
    response = {
      status: false,
      message: `Request failed`,
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};
