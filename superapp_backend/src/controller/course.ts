import { PayInitBody, PaymentCallback } from "../types/pg_types";
import { handleResponse, reducePercent } from "../common";
import { query } from "../db";
import { v4 as uuidv4 } from 'uuid';
import { CURRENCIES, PAYMENT_METHOD, PAYPAL_PLATFORM_FEES, PHONE_PE_STATUS } from "../common/constants";
import { decodeBase64, runPayAPI } from "../services/pg";
import { addAddressQuery, getCourseDetailQuery, getUserAddressByIdQuery, insertPgLogsQuery, insertUserCourseQuery, offerUsedCountQuery, updatePurchaseStatusQuery } from "../common/db_constants";
import logger from "../logger";
import * as jwt from 'jsonwebtoken';
import { getCourseVideo } from "../services/video";
import { generateInvoice, getOfferData } from "../services/orders";
import { UserAddress } from "../types/db_types";
import { createOrder } from "../services/paypal";
import { PayGLocalTxnData, PaymentCallbackDefault } from "../types";
import { initPayment } from "../services/payglocal";
import { generateSignedUrl, getSignedPlaylistWithSegments } from "../services/aws";

const getCoursesQuery = `select cd.*,array_agg(
	JSONB_BUILD_OBJECT(
		'currency',pcr.name,
		'currency_id',pcr.id,
		'price' , pp.price,
		'max_price' , pp.max_price,
		'gst' , pp.gst)
			) currencies from course_details cd
        inner join product_prices pp on pp.course_id = cd.id 
        inner join payment_currency pcr on pcr.id = pp.currency
    where cd.is_active = true 
    group by cd.id
    order by cd.created desc;`

const insertCoursePurchaseLogQuery = `insert into course_purchase_logs(course_id,user_id,merchant_transaction_id,payment_status, payment_currency,address_id,offer_id,purchased_price,payment_method,base_price)
values($1,$2,$3,$4,$5,$6,$7,$8,$9,$10)
RETURNING *;`

const getPurchaseByTxnIdQuery = `select *,(select validity from course_details cd where cd.id = course_id) validity from course_purchase_logs where merchant_transaction_id = $1 and payment_status = ANY($2);`

const insertVideoStreaingSessionQuery = `insert into course_video_sessions (video,stream_started_time,user_id,is_streaming,access,session_id)
values($1,now(),$2,true,$3,$4);`

const checkVidSessionQuery = `select * from course_video_sessions where video = $1 and session_id = $2 and is_streaming = true;`

const updateVidSessionQuery = `update course_video_sessions set updated = now() where video = $1 and session_id = $2;`

const getUsetCoursesQuery = `select cd.id,cd.id,cd.title,cd.sub_title "subTitle",cd.description,cd.course_level "level",cd.duration,cd.course_language "language",cd.price,cd.author_detials author,cd.course_includes "courseIncludes" from user_courses uc
inner join course_details cd on cd.id = uc.course_id
where uc.user_id = $1 and uc.is_active = true and uc.valid_till >= now()`;

// const userCourseByCidQuery = `select * from user_courses where course_id = $1 and user_id = $2 and valid_till >= now();`

export const getCourses = async (req: any, res: any) => {
  let response: any = {};
  try {
    const result = await query(getCoursesQuery, []);

    if (!result.rowCount) {
      response = {
        status: true,
        message: `Not Available`
      }
      return handleResponse(res, response, 200);
    }
    response = {
      status: true,
      message: `Success`,
      data: {
        courses: result.rows
      }
    }
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error
    }
    return handleResponse(res, response, 500);
  }
}

export const getUserCourses = async (req: any, res: any) => {
  const user = req.user;

  let response: any = {};
  try {
    const result = await query(getUsetCoursesQuery, [user.id]);

    if (!result.rowCount) {
      response = {
        status: true,
        message: `Not Available`
      }
      return handleResponse(res, response, 200);
    }
    response = {
      status: true,
      message: `Success`,
      data: {
        courses: result.rows
      }
    }
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error
    }
    return handleResponse(res, response, 500);
  }
}

export const getCoursesDetail = async (req: any, res: any) => {
  let response: any = {};

  const { courseId } = req.params;
  const { coupon } = req.query;

  if (!courseId) {
    response = {
      status: false,
      message: `Course Id Not Available`
    }
    return handleResponse(res, response, 400);
  }

  const user = req.user;

  try {
    let discountData: any;
    if (coupon) {
      const couponData = await getOfferData(coupon, undefined, courseId, undefined, undefined);
      if (couponData.length) {
        discountData = couponData[0]
      }
    }

    if (discountData) {
      const countData = await query(offerUsedCountQuery, [user.id, discountData.id]);

      if (!discountData.unlimited) {
        if (countData.rowCount) {
          if (countData.rows[0].total_count >= discountData.total_limit) {
            response = {
              status: false,
              message: `Invalid Coupon`
            }
            return handleResponse(res, response, 400);
          }
        }
      }

      if (discountData.user_limited) {
        if (countData.rows[0].user_count >= discountData.user_limit) {
          response = {
            status: false,
            message: `Invalid Coupon`
          }
          return handleResponse(res, response, 400);
        }
      }
    }

    const result = await query(getCourseDetailQuery, [courseId, user.id]);

    if (!result.rowCount) {
      response = {
        status: true,
        message: `Not Available`
      }
      return handleResponse(res, response, 200);
    }
    
    let courseDetail = result.rows[0];
    if (courseDetail.purchased) {
      for (let i = 0; i < courseDetail.chapters.length; i++) {
        courseDetail.chapters[i].playList = await Promise.all(
          courseDetail.chapters[i].playList.map(async (el: any) => {
            let filePath = `${el.filePath}`;
            let videoPath = generateSignedUrl(filePath, 30*60);
    
            if (!videoPath) {
              throw new Error(`Failed to generate signed URL for ${filePath}`);
            }
    
            let signed_playlist = await getSignedPlaylistWithSegments(videoPath);
            let signed_video_stream_url = `data:application/vnd.apple.mpegurl;base64,${Buffer.from(signed_playlist).toString('base64')}`;
            
            return {
              ...el,
              streamingUrl: signed_video_stream_url
            };
          })
        );
      }
    }

    courseDetail.currencies = courseDetail.currencies.filter((obj: any, index: any) => index === courseDetail.currencies.findIndex((o: any) => obj.currency_id === o.currency_id));

    //Getting ip location
    let currency = courseDetail.currencies.find((el: any) => el.currency_id === CURRENCIES.INR)
    courseDetail.currency_symbol = "₹";
    courseDetail.price = currency.price;
    courseDetail.tax = currency.gst;
    courseDetail.currency = currency.currency;
    if (discountData && Number(discountData.discount) > 0) {
      courseDetail.price = reducePercent(courseDetail.price, courseDetail.tax, Number(discountData.discount));
      courseDetail.currencies = courseDetail.currencies.map((el: any) => {
        return {
          ...el,
          price: reducePercent(el.price, el.gst, discountData!.discount)
        }
      })
    }

    response = {
      status: true,
      message: `Success`,
      data: courseDetail

    }
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error
    }
    return handleResponse(res, response, 500);
  }
}

export const purchaseCourse = async (req: any, res: any) => {
  let response: any = {};

  const { courseId, coupon } = req.body;
  const user = req.user;
  let address: UserAddress = req.body.address;
  const address_id: number = req.body.address_id;

  // //update address


  if (address_id) {
    let result = await query(getUserAddressByIdQuery, [address_id]);
    if (result.rowCount) {
      address = result.rows[0];
    }
  }

  if (!address || !address.first_name || !address.last_name || !address.address_line_one || !address.city || !address.state || !address.pincode || !address.phone || !address.email) {
    response = {
      status: false,
      message: `Address Missing Params`
    }
    return handleResponse(res, response, 400);
  }

  if (!courseId) {
    response = {
      status: false,
      message: `Course Id Not Available`
    }
    return handleResponse(res, response, 400);
  }
  try {
    let discountData: any;
    if (coupon) {
      const couponData = await getOfferData(coupon, undefined, courseId, undefined, undefined);
      if (couponData.length) {
        discountData = couponData[0]
      }
    }

    if (discountData) {
      const countData = await query(offerUsedCountQuery, [user.id, discountData.id]);

      if (!discountData.unlimited) {
        if (countData.rowCount) {
          if (countData.rows[0].total_count >= discountData.total_limit) {
            response = {
              status: false,
              message: `Invalid Coupon`
            }
            return handleResponse(res, response, 400);
          }
        }
      }

      if (discountData.user_limited) {
        if (countData.rows[0].user_count >= discountData.user_limit) {
          response = {
            status: false,
            message: `Invalid Coupon`
          }
          return handleResponse(res, response, 400);
        }
      }
    }

    const result = await query(getCourseDetailQuery, [courseId, user.id]);

    if (!result.rowCount) {
      response = {
        status: false,
        message: `Not Available`
      }
      return handleResponse(res, response, 400);
    }

    if (result.rows[0].purchased == true) {
      response = {
        status: false,
        message: `You've already purchased this course`
      }
      return handleResponse(res, response, 400);
    }

    let addressId = address_id;
    if (!addressId && address) {
      const addAddressDb = await query(addAddressQuery, [user.id, address.first_name, address.last_name, address.address_line_one, address.address_line_two, address.city, address.state, address.pincode, address.phone, address.email, address.country, address.address_line_three]);

      if (!addAddressDb.rowCount) {
        response = {
          status: false,
          message: `Error in saving Address`
        }
        return handleResponse(res, response, 400);
      }
      addressId = addAddressDb.rows[0].id
    }
    let amount = Number(result.rows[0].price);
    const baseAmount = Number(result.rows[0].price);

    if (discountData && Number(discountData.discount) > 0) {
      amount = reducePercent(amount, result.rows[0].gst, Number(discountData.discount));
    }

    const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
    let payment: any = null;
    if (amount > 0) {
      const data: PayInitBody = {
        merchantTransactionId: txnId,
        amount: parseInt((Number(amount) * 100).toString()),
        merchantUserId: user.id
      }

      const callBackUrl = process.env.COURSE_CALLBACK_URL

      payment = await runPayAPI(data, callBackUrl!!);
    }

    const purchaseLog = await query(insertCoursePurchaseLogQuery, [
      courseId,
      user.id,
      txnId,
      PHONE_PE_STATUS.PAYMENT_INIT.id,
      CURRENCIES.INR,
      addressId,
      discountData ? discountData.id : null,
      amount,
      PAYMENT_METHOD.PHONEPE,
      baseAmount
    ]);

    if (!purchaseLog.rowCount) {
      response = {
        status: false,
        message: 'Failed creating log'
      }
      return handleResponse(res, response, 400);
    }


    response = {
      status: true,
      message: "Success",
      data: {
        id: purchaseLog.rows[0].id,
        payment: payment
          ? {
            merchantTransactionId: payment.data.data.merchantTransactionId,
            redirectInfo: payment.data.data.instrumentResponse.redirectInfo,
          }
          : {},
      },
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: "Request Failed",
    };
    return handleResponse(res, response, 500);
  }
};

export const purchaseCourseV2 = async (req: any, res: any) => {
  let response: any = {};

  const { courseId, coupon } = req.body;
  const user = req.user;
  const address_id: number = req.body.address_id;
  let txnStarted = false;
  const payment_method: number = req.body.payment_method || PAYMENT_METHOD.PAYPAL;

  if (!courseId) {
    response = {
      status: false,
      message: `Course Id Not Available`
    }
    return handleResponse(res, response, 400);
  }

  let address: UserAddress = req.body.address;
  let currency = req.body.currency || CURRENCIES.INR;

  if (!Object.values(CURRENCIES).includes(Number(currency))) {
    response = {
      status: false,
      message: `Invalid Currency`
    }
    return handleResponse(res, response, 400);
  }

  try {
    let discountData: any;
    if (coupon) {
      const couponData = await getOfferData(coupon, undefined, courseId, undefined, undefined);
      if (couponData.length) {
        discountData = couponData[0]
      }
    }

    if (discountData) {
      const countData = await query(offerUsedCountQuery, [user.id, discountData.id]);

      if (!discountData.unlimited) {
        if (countData.rowCount) {
          if (countData.rows[0].total_count >= discountData.total_limit) {
            response = {
              status: false,
              message: `Invalid Coupon`
            }
            return handleResponse(res, response, 400);
          }
        }
      }

      if (discountData.user_limited) {
        if (countData.rows[0].user_count >= discountData.user_limit) {
          response = {
            status: false,
            message: `Invalid Coupon`
          }
          return handleResponse(res, response, 400);
        }
      }
    }

    const result = await query(getCourseDetailQuery, [courseId, user.id]);

    if (!result.rowCount) {
      response = {
        status: false,
        message: `Not Available`
      }
      return handleResponse(res, response, 400);
    }

    if (result.rows[0].purchased == true) {
      response = {
        status: false,
        message: `You've already purchased this course`
      }
      return handleResponse(res, response, 400);
    }

    const priceData = result.rows[0].currencies.find((obj: any) => { return Number(obj.currency_id) === Number(currency) });

    if (!priceData) {
      response = {
        status: false,
        message: `Currency data not found`
      }
      return handleResponse(res, response, 400);
    }

    let addressData: any = {
      address_id
    };

    if (!address_id && address) {
      const addAddressDb = await query(addAddressQuery, [user.id, address.first_name, address.last_name, address.address_line_one, address.address_line_two, address.city, address.state, address.pincode, address.phone, address.email, address.country, address.address_line_three]);

      if (!addAddressDb.rowCount) {
        response = {
          status: false,
          message: `Error in saving Address`
        }
        return handleResponse(res, response, 400);
      }
      addressData = {
        address_id: addAddressDb.rows[0].id,
        ...addAddressDb.rows[0]
      };
    } else {
      const addressRes = await query(getUserAddressByIdQuery, [address_id]);
      if (addressRes.rowCount) {
        address = result.rows[0];
        addressData = {
          address_id: addressRes.rows[0].id,
          ...addressRes.rows[0]
        };
      }
    }

    let amount = Number(priceData.price);
    const baseAmount = Number(priceData.price);
    if (discountData && Number(discountData.discount) > 0) {
      amount = reducePercent(amount, priceData.gst, Number(discountData.discount));
    }
    let payment: any = null;
    if (amount > 0) {
      if (payment_method == PAYMENT_METHOD.PAYPAL && currency == CURRENCIES.USD) {
        amount += PAYPAL_PLATFORM_FEES * amount;
        payment = await createOrder(Number(amount).toFixed(2).toString(), 'USD');
      } else if (payment_method == PAYMENT_METHOD.PAYGLOCAL && currency == CURRENCIES.USD) {
        const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
        const data: PayGLocalTxnData = {
          totalAmount: Number(amount).toFixed(2).toString(),
          txnCurrency: "USD",
          billingData: {
            firstName: addressData.first_name || addressData.firstName || '',
            lastName: addressData.last_name || addressData.lastName || '',
            addressStreet1: addressData.address_line_one,
            addressCity: addressData.city,
            addressState: addressData.state,
            addressPostalCode: addressData.pincode,
            addressCountry: "US",
            emailId: addressData.email
          }
        }
        payment = await initPayment(txnId, data);
      } else {
        const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
        const data: PayInitBody = {
          merchantTransactionId: txnId,
          amount: parseInt((Number(amount) * 100).toString()),
          merchantUserId: user.id
        }

        const callBackUrl = process.env.PHONEPE_CALLBACK_URL
        payment = await runPayAPI(data, callBackUrl!);
      }
    }

    await query("begin", []);

    txnStarted = true;

    const purchaseLog = await query(insertCoursePurchaseLogQuery, [
      courseId,
      user.id,
      payment ? payment.order_id : null,
      PHONE_PE_STATUS.PAYMENT_INIT.id,
      currency,
      addressData.address_id,
      discountData ? discountData.id : null,
      amount,
      payment.payment_method,
      baseAmount
    ]);

    if (!purchaseLog.rowCount) {
      await query("rollback", []);
      response = {
        status: false,
        message: 'Failed creating log'
      }
      return handleResponse(res, response, 400);
    }

    await query("commit", []);

    response = {
      status: true,
      message: "Success",
      data: {
        selectedPayment: {
          ...payment,
          id: purchaseLog.rows[0].id
        }
      },
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    if (txnStarted) {
      await query("rollback", []);
    }
    console.error("Error on placing order v2 : ", JSON.stringify(error));
    response = {
      status: false,
      message: "Request Failed",
    };
    return handleResponse(res, response, 500);
  }
};

export const coursePurchaseCallback = async (req: any, res: any) => {
  try {
    let response = req.body.response;

    response = decodeBase64(response) as PaymentCallback;

    await query(insertPgLogsQuery, [response.success, response.code, response]);

    const coursePurchaseLog = await query(getPurchaseByTxnIdQuery, [response.data.merchantTransactionId, [PHONE_PE_STATUS.PAYMENT_INIT.id, PHONE_PE_STATUS.PAYMENT_PENDING.id]]);

    if (!coursePurchaseLog.rowCount) {
      logger.error("Transaction Id not found");
      return res.send("OK");
    }

    if (response.code == PHONE_PE_STATUS.PAYMENT_SUCCESS.name) {
      const invoice = await generateInvoice(coursePurchaseLog.rows[0].id, coursePurchaseLog.rows[0].user_id, 'COURSE')

      let orderDetail = await query(updatePurchaseStatusQuery, [PHONE_PE_STATUS.PAYMENT_SUCCESS.id, response.data.merchantTransactionId, invoice]);

      if (!orderDetail.rowCount) {
        logger.error("Course Purchase Id not found & updated");
        return res.send("OK");
      }

      const course = await query(getCourseDetailQuery, [coursePurchaseLog.rows[0].course_id, coursePurchaseLog.rows[0].user_id]);

      if (!course.rowCount) {
        logger.error("Course Id not found");
        return res.send("OK");
      }

      const userCourse = await query(insertUserCourseQuery, [coursePurchaseLog.rows[0].course_id, coursePurchaseLog.rows[0].user_id, coursePurchaseLog.rows[0].validity]);

      if (!userCourse.rowCount) {
        logger.error("User course not created");
        return res.send("OK");
      }

      return res.send("OK");
    } else if (response.code == PHONE_PE_STATUS.TIMED_OUT.name || response.code == PHONE_PE_STATUS.PAYMENT_ERROR.name || response.code == PHONE_PE_STATUS.PAYMENT_DECLINED.name) {
      let orderDetail = await query(updatePurchaseStatusQuery, [PHONE_PE_STATUS.PAYMENT_ERROR.id, response.data.merchantTransactionId, null]);
      logger.log("Upadted Payment Count : ", orderDetail.rowCount);
      return res.send("OK");
    }
  } catch (error) {
    logger.log("Error on Upadted Payment Count : ", error);
    return res.send("OK");
  }
}

export const coursePurchaseCallbackV2 = async (req: any, res: any, next: any) => {
  try {
    let response = {
      success: false,
      code: 400
    }

    const data: PaymentCallbackDefault = req.payment_data;
    // const paymentStatus = await paymentStatusCheck(response.data.merchantTransactionId);

    if (data.isCompleted) {
      response.success = true;
      response.code = 200;
    }

    // await query(insertPgLogsQuery, [response.success, response.code, req.body]);

    const razorpay_order_id = data.orderId

    const coursePurchaseLog = await query(getPurchaseByTxnIdQuery, [razorpay_order_id, [PHONE_PE_STATUS.PAYMENT_INIT.id, PHONE_PE_STATUS.PAYMENT_PENDING.id]]);

    if (!coursePurchaseLog.rowCount) {
      return next();
    }

    if (response.success) {
      const invoice = await generateInvoice(coursePurchaseLog.rows[0].id, coursePurchaseLog.rows[0].user_id, 'COURSE')

      let orderDetail = await query(updatePurchaseStatusQuery, [PHONE_PE_STATUS.PAYMENT_SUCCESS.id, razorpay_order_id, invoice]);

      if (!orderDetail.rowCount) {
        logger.error("Course Purchase Id not found & updated");
        return res.send("OK");
      }

      const course = await query(getCourseDetailQuery, [coursePurchaseLog.rows[0].course_id, coursePurchaseLog.rows[0].user_id]);

      if (!course.rowCount) {
        logger.error("Course Id not found");
        return res.send("OK");
      }

      const userCourse = await query(insertUserCourseQuery, [coursePurchaseLog.rows[0].course_id, coursePurchaseLog.rows[0].user_id, coursePurchaseLog.rows[0].validity]);

      if (!userCourse.rowCount) {
        logger.error("User course not created");
        return res.send("OK");
      }
      return res.send("OK");
    } else if (!response.success) {
      await query(updatePurchaseStatusQuery, [PHONE_PE_STATUS.PAYMENT_ERROR.id, razorpay_order_id, null]);
      logger.error("Upadted Payment failed");
      return res.send("OK");
    }
  } catch (error) {
    logger.error(`Error on Upadted Payment Count : ${JSON.stringify(error)}`);
    return res.send("OK");
  }
}

export const cancelPurchase = async (req: any, res: any) => {
  let response = {};

  try {
    const { mTxnId } = req.body;

    if (!mTxnId) {
      response = {
        status: false,
        message: `Missing Params`
      }
      return handleResponse(res, response, 400);
    }

    const coursePurchaseLog = await query(getPurchaseByTxnIdQuery, [mTxnId, [PHONE_PE_STATUS.PAYMENT_INIT.id, PHONE_PE_STATUS.PAYMENT_PENDING.id]]);

    if (!coursePurchaseLog.rowCount) {
      response = {
        status: false,
        message: `Purchase transaction not found`
      }
      return handleResponse(res, response, 400);
    }

    const orderDetail = await query(updatePurchaseStatusQuery, [PHONE_PE_STATUS.PAYMENT_DECLINED.id, mTxnId, null]);

    if (!orderDetail.rowCount) {
      response = {
        status: false,
        message: `Purchase transaction not updated`
      }
      return handleResponse(res, response, 400);
    }

    response = {
      status: true,
      message: `Purchase cancelled`
    }
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error
    }
    return handleResponse(res, response, 500);
  }
}

export const processStreaming = async (req: any, res: any) => {
  let response: any = {};
  const { session } = req.query;

  if (!session) {
    response = {
      status: false,
      message: `Missing Session Details`
    }
    return handleResponse(res, response, 400);
  }

  let decoded: any = {};
  try {
    decoded = jwt.verify(session, process.env.SECRET!);

    if (!decoded.video_id) {
      return handleResponse(res, { status: false, message: "Error in Token" }, 401);
    }
  } catch (error) {
    response.error = "Error in decoding token";
    return handleResponse(res, response, 401);
  }

  try {
    const session = await query(checkVidSessionQuery, [decoded.video_id, decoded.session_id]);

    if (!session.rowCount) {
      await query(insertVideoStreaingSessionQuery, [decoded.video_id, decoded.user_id, decoded.access ? decoded.access : null, decoded.session_id]);
    } else {
      await query(updateVidSessionQuery, [decoded.video_id, decoded.session_id]);
    }

    req.query.id = decoded.video_id;

    return getCourseVideo(req, res);
  } catch (error) {
    response = {
      status: false,
      message: `Error in creating video session`
    }
    return handleResponse(res, response, 401);
  }
}