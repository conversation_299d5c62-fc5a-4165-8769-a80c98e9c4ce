import { PayInitBody, PaymentCallback } from "../types/pg_types";
import { handleResponse, reducePercent } from "../common";
import { query } from "../db";
import { v4 as uuidv4 } from 'uuid';
import { CURRENCIES, PAYMENT_METHOD, PAYPAL_PLATFORM_FEES, PHONE_PE_STATUS } from "../common/constants";
import { decodeBase64, runPayAPI } from "../services/pg";
import { addAddressQuery, getCourseDetailQuery, getUserAddressByIdQuery, insertPgLogsQuery, insertUserCourseQuery, offerUsedCountQuery, updatePurchaseStatusQuery } from "../common/db_constants";
import logger from "../logger";
import * as jwt from 'jsonwebtoken';
import { getCourseVideo } from "../services/video";
import { generateInvoice, getOfferData } from "../services/orders";
import { UserAddress } from "../types/db_types";
import { createOrder } from "../services/paypal";
import { PayGLocalTxnData, PaymentCallbackDefault } from "../types";
import { initPayment } from "../services/payglocal";
import { generateSignedUrl, getSignedPlaylistWithSegments } from "../services/aws";
import { getCartItemsByArtistId } from "../services/merch";

const getCoursesQuery = `select cd.*,array_agg(
	JSONB_BUILD_OBJECT(
		'currency',pcr.name,
		'currency_id',pcr.id,
		'price' , pp.price,
		'max_price' , pp.max_price,
		'gst' , pp.gst)
			) currencies from course_details cd
        inner join product_prices pp on pp.course_id = cd.id 
        inner join payment_currency pcr on pcr.id = pp.currency
    where cd.is_active = true 
    group by cd.id
    order by cd.created desc;`

const insertCoursePurchaseLogQuery = `insert into course_purchase_logs(course_id,user_id,merchant_transaction_id,payment_status, payment_currency,address_id,offer_id,purchased_price,payment_method,base_price,is_combo_purchase)
values($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11)
RETURNING *;`

const getPurchaseByTxnIdQuery = `select *,(select validity from course_details cd where cd.id = course_id) validity from course_purchase_logs where merchant_transaction_id = $1 and payment_status = ANY($2);`

const insertVideoStreaingSessionQuery = `insert into course_video_sessions (video,stream_started_time,user_id,is_streaming,access,session_id)
values($1,now(),$2,true,$3,$4);`

const checkVidSessionQuery = `select * from course_video_sessions where video = $1 and session_id = $2 and is_streaming = true;`

const updateVidSessionQuery = `update course_video_sessions set updated = now() where video = $1 and session_id = $2;`

const getUsetCoursesQuery = `select cd.id,cd.id,cd.title,cd.sub_title "subTitle",cd.description,cd.course_level "level",cd.duration,cd.course_language "language",cd.price,cd.author_detials author,cd.course_includes "courseIncludes" from user_courses uc
inner join course_details cd on cd.id = uc.course_id
where uc.user_id = $1 and uc.is_active = true and uc.valid_till >= now()`;

const productIdByCourseIdQuery = `SELECT id FROM product_item WHERE product_id = $1 AND product_type = 2;`;

async function isCourseInCart(cartItems: any, courseId: string): Promise<boolean> {
  const productIds = await query(productIdByCourseIdQuery, [courseId]);
  return cartItems.some((item: any) => item.productItemId === productIds.rows[0].id);
}

export const getCourses = async (req: any, res: any) => {
  let response: any = {};
  try {
    const result = await query(getCoursesQuery, []);

    if (!result.rowCount) {
      response = {
        status: true,
        message: `Not Available`
      }
      return handleResponse(res, response, 200);
    }
    response = {
      status: true,
      message: `Success`,
      data: {
        courses: result.rows
      }
    }
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error
    }
    return handleResponse(res, response, 500);
  }
}

export const getUserCourses = async (req: any, res: any) => {
  const user = req.user;

  let response: any = {};
  try {
    const result = await query(getUsetCoursesQuery, [user.id]);

    if (!result.rowCount) {
      response = {
        status: true,
        message: `Not Available`
      }
      return handleResponse(res, response, 200);
    }
    response = {
      status: true,
      message: `Success`,
      data: {
        courses: result.rows
      }
    }
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error
    }
    return handleResponse(res, response, 500);
  }
}

export const getCoursesDetail = async (req: any, res: any) => {
  let response: any = {};

  const { coupon ,courseId, courselevelavail , CurrencyCode } = req.query;

  if (!courseId) {
    response = {
      status: false,
      message: `Course Level Id Not Available`
    };
    return handleResponse(res, response, 400);
  }

  const user = req.user;
  try {
    let discountData: any;
    if (coupon) {
      const couponData = await getOfferData(coupon, undefined, undefined, courseId, undefined);
      if (couponData.length) {
        discountData = couponData[0];
      }
    }

    if (discountData) {
      const countData = await query(offerUsedCountQuery, [user.id, discountData.id]);

      if (!discountData.unlimited) {
        if (countData.rowCount && countData.rows[0].total_count >= discountData.total_limit) {
          response = {
            status: false,
            message: `Invalid Coupon`
          };
          return handleResponse(res, response, 400);
        }
      }

      if (discountData.user_limited && countData.rows[0].user_count >= discountData.user_limit) {
        response = {
          status: false,
          message: `Invalid Coupon`
        };
        return handleResponse(res, response, 400);
      }
    }

    const result = await query(getCourseDetailQuery, [courseId, user.id, courselevelavail]);
    const userCart = await getCartItemsByArtistId(user.id, null, CurrencyCode === "INR" ? 1 : 2);
    
    if (!result.rowCount) {
      response = {
        status: true,
        message: `No courses found`,
        data: []
      };
      return handleResponse(res, response, 200);
    }
    const courses = result.rows.flatMap((row: any) => row.courses);

    for (const course of courses) {
      if (course.purchased) {
        for (let chapter of course.chapters) {
          chapter.playList = await Promise.all(
            (chapter.playList).map(async (el: any) => {
              const videoPath = generateSignedUrl(`${el.filePath}`, 30 * 60);
              if (!videoPath) {
                throw new Error(`Failed to generate signed URL for file`);
              }

              const signedPlaylist = await getSignedPlaylistWithSegments(videoPath);        
              const streamingUrl = `data:application/vnd.apple.mpegurl;base64,${Buffer.from(signedPlaylist).toString("base64")}`;
              return {
                ...el,
                streamingUrl
              };
            })
          );
        }
      }

      if (Array.isArray(course.currencies)) {
        course.currencies = course.currencies.filter(
          (obj: any, index: any) =>
            index === course.currencies.findIndex((o: any) => obj.currency_id === o.currency_id)
        );
      } else {
        course.currencies = []; // or you can choose to skip the course, log, or throw an error
      }
      
      const NotInCart = await isCourseInCart(userCart, courseId);
      // Default to INR currency
      const currency = course.currencies.find((el: any) => el.currency_id === CURRENCIES.INR);
      course.currency_symbol = "₹";
      course.price = currency?.price || 0;
      course.tax = currency?.gst || 0;
      course.currency = currency?.currency || "INR";
      course.NotInCart = NotInCart;
      
      // Apply discount
      if (discountData && Number(discountData.discount) > 0) {
        course.price = reducePercent(course.price, course.tax, Number(discountData.discount));
        course.currencies = course.currencies.map((el: any) => ({
          ...el,
          price: reducePercent(el.price, el.gst, discountData.discount)
        }));
      }
    }

    response = {
      status: true,
      message: `Success`,
      data: courses
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error
    };
    return handleResponse(res, response, 500);
  }
};

export const purchaseCourseV2 = async (req: any, res: any) => {
  let response: any = {};

  const { courseId, coupon, courseIdAvail } = req.body;
  const user = req.user;
  const address_id: number = req.body.address_id;
  let txnStarted = false;
  const payment_method: number = req.body.payment_method || PAYMENT_METHOD.PAYPAL;

  if (!courseId) {
    response = {
      status: false,
      message: `Course Id Not Available`
    }
    return handleResponse(res, response, 400);
  }

  let address: UserAddress = req.body.address;
  let currency = req.body.currency || CURRENCIES.INR;

  if (!Object.values(CURRENCIES).includes(Number(currency))) {
    response = {
      status: false,
      message: `Invalid Currency`
    }
    return handleResponse(res, response, 400);
  }

  try {
    let discountData: any;
    if (coupon) {
      const couponData = await getOfferData(coupon, undefined, courseId, undefined, undefined);
      if (couponData.length) {
        discountData = couponData[0]
      }
    }

    if (discountData) {
      const countData = await query(offerUsedCountQuery, [user.id, discountData.id]);

      if (!discountData.unlimited) {
        if (countData.rowCount) {
          if (countData.rows[0].total_count >= discountData.total_limit) {
            response = {
              status: false,
              message: `Invalid Coupon`
            }
            return handleResponse(res, response, 400);
          }
        }
      }

      if (discountData.user_limited) {
        if (countData.rows[0].user_count >= discountData.user_limit) {
          response = {
            status: false,
            message: `Invalid Coupon`
          }
          return handleResponse(res, response, 400);
        }
      }
    }

    const result = await query(getCourseDetailQuery, [courseId, user.id, courseIdAvail]);

    if (!result.rowCount) {
      response = {
        status: false,
        message: `Not Available`
      }
      return handleResponse(res, response, 400);
    }

    const hasPurchased = result.rows[0].courses?.some((course: { purchased: boolean; }) => course.purchased === true);

    if (hasPurchased) {
      response = {
        status: false,
        message: `You've already purchased this course`
      };
      return handleResponse(res, response, 400);
    }
    
    const priceData = result.rows[0].courses[0].currencies.find((obj: any) => { return Number(obj.currency_id) === Number(currency) });
    
    const courses = result.rows[0].courses || [];
    
    let totalPrice = 0;
    
    for (const course of courses) {
      const priceObj = course.currencies?.find(
        (obj: any) => Number(obj.currency_id) === Number(currency)
      );
      
      if (priceObj && priceObj.price) {
        totalPrice += Number(priceObj.price); // Assuming `value` is the price
      }
    }

    if (!totalPrice) {
      response = {
        status: false,
        message: `Currency data not found`
      }
      return handleResponse(res, response, 400);
    }

    let addressData: any = {
      address_id
    };

    if (!address_id && address) {
      const addAddressDb = await query(addAddressQuery, [user.id, address.first_name, address.last_name, address.address_line_one, address.address_line_two, address.city, address.state, address.pincode, address.phone, address.email, address.country, address.address_line_three]);

      if (!addAddressDb.rowCount) {
        response = {
          status: false,
          message: `Error in saving Address`
        }
        return handleResponse(res, response, 400);
      }
      addressData = {
        address_id: addAddressDb.rows[0].id,
        ...addAddressDb.rows[0]
      };
    } else {
      const addressRes = await query(getUserAddressByIdQuery, [address_id]);
      if (addressRes.rowCount) {
        address = result.rows[0];
        addressData = {
          address_id: addressRes.rows[0].id,
          ...addressRes.rows[0]
        };
      }
    }

    let amount = Number(totalPrice);
    // let amount = Number(priceData.price);
    const baseAmount = Number(totalPrice);
    if (discountData && Number(discountData.discount) > 0) {
      amount = reducePercent(amount, priceData.gst, Number(discountData.discount));
    }
    let payment: any = null;
    if (amount > 0) {
      if (payment_method == PAYMENT_METHOD.PAYPAL && currency == CURRENCIES.USD) {
        amount += PAYPAL_PLATFORM_FEES * amount;
        payment = await createOrder(Number(amount).toFixed(2).toString(), 'USD');
      } else if (payment_method == PAYMENT_METHOD.PAYGLOCAL && currency == CURRENCIES.USD) {
        const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
        const data: PayGLocalTxnData = {
          totalAmount: Number(amount).toFixed(2).toString(),
          txnCurrency: "USD",
          billingData: {
            firstName: addressData.first_name || addressData.firstName || '',
            lastName: addressData.last_name || addressData.lastName || '',
            addressStreet1: addressData.address_line_one,
            addressCity: addressData.city,
            addressState: addressData.state,
            addressPostalCode: addressData.pincode,
            addressCountry: "US",
            emailId: addressData.email
          }
        }
        payment = await initPayment(txnId, data);
      } else {
        const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
        const data: PayInitBody = {
          merchantTransactionId: txnId,
          amount: parseInt((Number(amount) * 100).toString()),
          merchantUserId: user.id
        }
        const callBackUrl = process.env.PHONEPE_CALLBACK_URL
        payment = await runPayAPI(data, callBackUrl!);
      }
    }

    await query("begin", []);

    txnStarted = true;

    const purchaseLog = await query(insertCoursePurchaseLogQuery, [
      courseId,
      user.id,
      payment ? payment.order_id : null,
      PHONE_PE_STATUS.PAYMENT_INIT.id,
      currency,
      addressData.address_id,
      discountData ? discountData.id : null,
      amount,
      payment.payment_method,
      baseAmount,
      courseIdAvail
    ]);

    if (!purchaseLog.rowCount) {
      await query("rollback", []);
      response = {
        status: false,
        message: 'Failed creating log'
      }
      return handleResponse(res, response, 400);
    }

    await query("commit", []);

    response = {
      status: true,
      message: "Success",
      data: {
        selectedPayment: {
          ...payment,
          id: purchaseLog.rows[0].id
        }
      },
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    if (txnStarted) {
      await query("rollback", []);
    }
    console.error("Error on placing order v2 : ", JSON.stringify(error));
    response = {
      status: false,
      message: "Request Failed",
    };
    return handleResponse(res, response, 500);
  }
};

export const coursePurchaseCallback = async (req: any, res: any) => {
  try {
    let response = req.body.response;

    response = decodeBase64(response) as PaymentCallback;

    await query(insertPgLogsQuery, [response.success, response.code, response]);

    const coursePurchaseLog = await query(getPurchaseByTxnIdQuery, [response.data.merchantTransactionId, [PHONE_PE_STATUS.PAYMENT_INIT.id, PHONE_PE_STATUS.PAYMENT_PENDING.id]]);

    if (!coursePurchaseLog.rowCount) {
      logger.error("Transaction Id not found");
      return res.send("OK");
    }

    if (response.code == PHONE_PE_STATUS.PAYMENT_SUCCESS.name) {
      const invoice = await generateInvoice(coursePurchaseLog.rows[0].id, coursePurchaseLog.rows[0].user_id, 'COURSE')

      let orderDetail = await query(updatePurchaseStatusQuery, [PHONE_PE_STATUS.PAYMENT_SUCCESS.id, response.data.merchantTransactionId, invoice]);

      if (!orderDetail.rowCount) {
        logger.error("Course Purchase Id not found & updated");
        return res.send("OK");
      }


      const course = await query(getCourseDetailQuery, [coursePurchaseLog.rows[0].course_id, coursePurchaseLog.rows[0].user_id, coursePurchaseLog.rows[0].is_combo_purchase]);

      if (!course.rowCount) {
        logger.error("Course Id not found");
        return res.send("OK");
      }

      const courseDetail = course.rows[0].courses;

      // const userCourse = await query(insertUserCourseQuery, [coursePurchaseLog.rows[0].course_id, coursePurchaseLog.rows[0].user_id, coursePurchaseLog.rows[0].validity]);

const userId = coursePurchaseLog.rows[0].user_id;
const validity = coursePurchaseLog.rows[0].validity;

            const userCourse = await Promise.all(
        courseDetail.map((course: { id: number }) =>
          query(insertUserCourseQuery, [course.id, userId, validity])
        )
      );

      // Check if at least one insert was successful
      const allInsertSuccess = userCourse.every(result => result.rowCount && result.rowCount > 0);

      if (!allInsertSuccess) {
        logger.error("One or more user courses were not created");
        return res.send("OK");
      }

      return res.send("OK");
    } else if (response.code == PHONE_PE_STATUS.TIMED_OUT.name || response.code == PHONE_PE_STATUS.PAYMENT_ERROR.name || response.code == PHONE_PE_STATUS.PAYMENT_DECLINED.name) {
      let orderDetail = await query(updatePurchaseStatusQuery, [PHONE_PE_STATUS.PAYMENT_ERROR.id, response.data.merchantTransactionId, null]);
      logger.log("Upadted Payment Count : ", orderDetail.rowCount);
      return res.send("OK");
    }
  } catch (error) {
    logger.log("Error on Upadted Payment Count : ", error);
    return res.send("OK");
  }
}

export const coursePurchaseCallbackV2 = async (req: any, res: any, next: any) => {
  try {
    let response = {
      success: false,
      code: 400
    }


    const data: PaymentCallbackDefault = req.payment_data;
    // const paymentStatus = await paymentStatusCheck(response.data.merchantTransactionId);

    if (data.isCompleted) {
      response.success = true;
      response.code = 200;
    }

    // await query(insertPgLogsQuery, [response.success, response.code, req.body]);

    const razorpay_order_id = data.orderId

    const coursePurchaseLog = await query(getPurchaseByTxnIdQuery, [razorpay_order_id, [PHONE_PE_STATUS.PAYMENT_INIT.id, PHONE_PE_STATUS.PAYMENT_PENDING.id]]);

    if (!coursePurchaseLog.rowCount) {
      return next();
    }

    if (response.success) {
      const invoice = await generateInvoice(coursePurchaseLog.rows[0].id, coursePurchaseLog.rows[0].user_id, 'COURSE')

      let orderDetail = await query(updatePurchaseStatusQuery, [PHONE_PE_STATUS.PAYMENT_SUCCESS.id, razorpay_order_id, invoice]);

      if (!orderDetail.rowCount) {
        logger.error("Course Purchase Id not found & updated");
        return res.send("OK");
      }

      const course = await query(getCourseDetailQuery, [coursePurchaseLog.rows[0].course_id, coursePurchaseLog.rows[0].user_id, coursePurchaseLog.rows[0].is_combo_purchase]);

      if (!course.rowCount) {
        logger.error("Course Id not found");
        return res.send("OK");
      }

      const courseDetail = course.rows[0].courses;

const userId = coursePurchaseLog.rows[0].user_id;
const validity = coursePurchaseLog.rows[0].validity;

            const userCourse = await Promise.all(
        courseDetail.map((course: { id: number }) =>
          query(insertUserCourseQuery, [course.id, userId, validity])
        )
      );

      // Check if at least one insert was successful
      const allInsertSuccess = userCourse.every(result => result.rowCount && result.rowCount > 0);
      if (!allInsertSuccess) {
        logger.error("One or more user courses were not created");
        return res.send("OK");
      }
      return res.send("OK");
    } else if (!response.success) {
      await query(updatePurchaseStatusQuery, [PHONE_PE_STATUS.PAYMENT_ERROR.id, razorpay_order_id, null]);
      logger.error("Upadted Payment failed");
      return res.send("OK");
    }
  } catch (error) {
    logger.error(`Error on Upadted Payment Count : ${JSON.stringify(error)}`);
    return res.send("OK");
  }
}

export const cancelPurchase = async (req: any, res: any) => {
  let response = {};

  try {
    const { mTxnId } = req.body;

    if (!mTxnId) {
      response = {
        status: false,
        message: `Missing Params`
      }
      return handleResponse(res, response, 400);
    }

    const coursePurchaseLog = await query(getPurchaseByTxnIdQuery, [mTxnId, [PHONE_PE_STATUS.PAYMENT_INIT.id, PHONE_PE_STATUS.PAYMENT_PENDING.id]]);

    if (!coursePurchaseLog.rowCount) {
      response = {
        status: false,
        message: `Purchase transaction not found`
      }
      return handleResponse(res, response, 400);
    }

    const orderDetail = await query(updatePurchaseStatusQuery, [PHONE_PE_STATUS.PAYMENT_DECLINED.id, mTxnId, null]);

    if (!orderDetail.rowCount) {
      response = {
        status: false,
        message: `Purchase transaction not updated`
      }
      return handleResponse(res, response, 400);
    }

    response = {
      status: true,
      message: `Purchase cancelled`
    }
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error
    }
    return handleResponse(res, response, 500);
  }
}

export const processStreaming = async (req: any, res: any) => {
  let response: any = {};
  const { session } = req.query;

  if (!session) {
    response = {
      status: false,
      message: `Missing Session Details`
    }
    return handleResponse(res, response, 400);
  }

  let decoded: any = {};
  try {
    decoded = jwt.verify(session, process.env.SECRET!);

    if (!decoded.video_id) {
      return handleResponse(res, { status: false, message: "Error in Token" }, 401);
    }
  } catch (error) {
    response.error = "Error in decoding token";
    return handleResponse(res, response, 401);
  }

  try {
    const session = await query(checkVidSessionQuery, [decoded.video_id, decoded.session_id]);

    if (!session.rowCount) {
      await query(insertVideoStreaingSessionQuery, [decoded.video_id, decoded.user_id, decoded.access ? decoded.access : null, decoded.session_id]);
    } else {
      await query(updateVidSessionQuery, [decoded.video_id, decoded.session_id]);
    }

    req.query.id = decoded.video_id;

    return getCourseVideo(req, res);
  } catch (error) {
    response = {
      status: false,
      message: `Error in creating video session`
    }
    return handleResponse(res, response, 401);
  }
}