import { getArtistBookQuery } from "../common/db_constants";
import { handleResponse } from "../common";
import { query } from "../db";
import { getFileSize, removeFile } from "../services/file";
import { MEET_REQ_STATUS, getMeetRequestByArtistDetailId, getMeetRequestById, saveMeetRequest, sendUserRequestStatusUpdate, updateRequestStatus, sendMailToArtist, sendUserAcknoledgement } from "../services/meet_auth";
import { getAcceptOrRejectBody, getRejectDetailBody } from "../common/constants";
import { uploadFile } from "../services/aws";
import path from "path";

export const baseURL = 'https://d2b4rv4q8lb0q0.cloudfront.net/';


export const videoOutDir = 'meets/user_videos/'

const uploadDir = 'meet/upload'

export const uploadAssets = async (req: any, res: any) => {
    let response: any = {}
    try {
        // Gets User Object
        const user = req.user;

        // Checks for the user file
        if (!req.file) {
            response = {
                status: false,
                message: `File Not Available`
            }
            return handleResponse(res, response, 400);
        }

        const filePath = req.file.path;
        const originalExtension = path.extname(req.file.originalname).toLowerCase();
        const allowedExtensions = ['.mp4', '.mp3', '.wav'];

        if (!allowedExtensions.includes(originalExtension)) {
            response = {
                status: false,
                message: `Invalid file type. Only MP4, MP3, and WAV files are allowed.`
            }
            removeFile(filePath);
            return handleResponse(res, response, 400);
        }

        //Get File Size
        const fileSize = await getFileSize(filePath);
        // Set different size limits for different file types
        const maxSize = originalExtension === '.mp4'
            ? 200 * 1024 * 1024  // 200MB for videos
            : 50 * 1024 * 1024;  // 50MB for audio files

        if (fileSize > maxSize) {
            response = {
                status: false,
                message: `File is too large. Maximum size is ${originalExtension === '.mp4' ? '200MB' : '50MB'}`
            }
            removeFile(filePath);
            return handleResponse(res, response, 400);
        }

        // Track id in database for fetching the track file
        const { id } = req.query;

        if (!id) {
            response = {
                status: false,
                message: `Invalid params`
            }
            removeFile(filePath);
            return handleResponse(res, response, 400);
        }

        const result = await query(getArtistBookQuery, [id]);

        if (!result.rowCount) {
            response = {
                status: false,
                message: `Artist Not Available`
            }
            removeFile(filePath);
            return handleResponse(res, response, 200);
        }

        const timestamp = Date.now();
        const outputFileName = `user_${user.id}_meet_${id}_${timestamp}${originalExtension}`;

        // Upload the saved path to cloud storage
        const awsUrl = await uploadFile(outputFileName, filePath, uploadDir);

        removeFile(filePath);

        response = {
            status: true,
            message: `File uploaded successfully`,
            url: awsUrl
        }
        return handleResponse(res, response, 200);
    } catch (error) {
        if (req.file) {
            removeFile(req.file.path);
        }

        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}

export const userMeetRequest = async (req: any, res: any) => {
    let response: any = {}
    try {
        const user = req.user;

        const { data, id } = req.body;

        if (!data || !id || !data['name'].value) {
            response = {
                status: false,
                message: `Invalid params`
            }
            return handleResponse(res, response, 400);
        }
        const result = await query(getArtistBookQuery, [id]);
        const artistData = result.rows[0];

        if (!result.rowCount) {
            response = {
                status: false,
                message: `Artist Not Available`
            }
            return handleResponse(res, response, 200);
        }

        const oldRequests = await getMeetRequestByArtistDetailId([id], user.id, Object.values(MEET_REQ_STATUS));

        if (oldRequests.data?.length) {
            response = {
                status: false,
                message: `Already Requested`
            }
            return handleResponse(res, response, 200);
        }

        let request: { status: boolean; message: string; id?: string } = {
            status: false,
            message: "Error in saving data"
        };

        if (Number(artistData.meet_type) === 3) {
            request = await saveMeetRequest(data, id, user.id, MEET_REQ_STATUS.ACCEPTED);
        } else {
            request = await saveMeetRequest(data, id, user.id);
        }

        if (!request.status) {
            response = {
                status: false,
                message: request.message
            }
            return handleResponse(res, response, 200);
        }

        // Send Mail to artist about the request received, if meet type is not 11
        if (Number(id) !== 11) {
            await sendMailToArtist(result.rows[0]['name'], request.id ?? '', result.rows[0].email, data, Number(id), Number(artistData.meet_type));

            if ((user.email || data['email'].value) && Number(artistData.meet_type) == 2) {
                await sendUserAcknoledgement(data['email'].value || user.email, data['name'].value, result.rows[0].artist_name, result.rows[0].name);
            }
        }

        response = {
            status: true,
            message: `Success`
        }
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `An error occurred while processing the meet request`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}

export const userMeetRequestDetail = async (req: any, res: any) => {
    let response: any = {};
    const id = req.decoded.reqId;
    const token = req.query.token;

    if (!id || !token) {
        response = {
            status: false,
            message: `Missing required parameters: request ID or token`
        }
        return handleResponse(res, response, 200);
    }

    const item = await getMeetRequestById(id);
    if (!item) {
        response = {
            status: false,
            message: `Meet request not found`
        }
        return handleResponse(res, response, 200);
    }


    const htmlContent = getAcceptOrRejectBody(item.data.userName, process.env.BC_BASE_URL || '', id, token);

    // Send the HTML content as the response
    res.send(htmlContent);
}

export const userRejectRequestDetail = async (req: any, res: any) => {
    let response: any = {};
    const id = req.decoded.reqId;
    const token = req.query.token;

    if (!id || !token) {
        response = {
            status: false,
            message: `Missing required parameters: request ID or token`
        }
        return handleResponse(res, response, 200);
    }

    const item = await getMeetRequestById(id);
    if (!item) {
        response = {
            status: false,
            message: `Meet request not found`
        }
        return handleResponse(res, response, 200);
    }


    const htmlContent = getRejectDetailBody(process.env.BC_BASE_URL || '', id, token);

    // Send the HTML content as the response
    res.send(htmlContent);
}

export const rejectRequest = async (req: any, res: any) => {
    let response: any = {};
    const id = req.query.id;
    const { feedback } = req.body;
    try {
        if (!id) {
            response = {
                status: false,
                message: `Missing required parameter: request ID`
            }
            return handleResponse(res, response, 200);
        }

        const item = await getMeetRequestById(id);
        if (!item) {
            response = {
                status: false,
                message: `Meet request not found`
            }
            return handleResponse(res, response, 200);
        }

        if (item.data.status != MEET_REQ_STATUS.REQUESTED) {
            response = {
                status: false,
                message: `This request has already been processed`
            }
            return handleResponse(res, response, 200);
        }

        const update = await updateRequestStatus(id, MEET_REQ_STATUS.REJECTED, feedback);

        if (update.id) {
            sendUserRequestStatusUpdate(item.data.email, MEET_REQ_STATUS.REJECTED, item.data.userName, item.data.artistName, "", item.data.meet_id, item.data.meetName, feedback);
        }


        response = {
            status: true,
            message: update.message
        }
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Failed to reject meet request`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}

export const acceptRequest = async (req: any, res: any) => {
    let response: any = {};
    const id = req.query.id;
    try {
        if (!id) {
            response = {
                status: false,
                message: `Missing required parameter: request ID`
            }
            return handleResponse(res, response, 200);
        }

        const item = await getMeetRequestById(id);
        if (!item) {
            response = {
                status: false,
                message: `Meet request not found`
            }
            return handleResponse(res, response, 200);
        }

        if (item.data.status != MEET_REQ_STATUS.REQUESTED) {
            response = {
                status: false,
                message: `This request has already been processed`
            }
            return handleResponse(res, response, 200);
        }

        const update = await updateRequestStatus(id, MEET_REQ_STATUS.ACCEPTED, undefined);
        if (update.id) {
            let endPoint = `slotBook`;
            const link = process.env.APP_URL;
            if (item.data.meet_id == 9){
                endPoint = `slot-selection`;
            }
            const url = new URL(link + endPoint + '/' + item.data.meet_id);
            sendUserRequestStatusUpdate(item.data.email, MEET_REQ_STATUS.ACCEPTED, item.data.userName, item.data.artistName, url.toString(), Number(item.data.meet_id), item.data.meetName);
        }


        response = {
            status: true,
            message: update.message
        }
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Failed to accept meet request`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}
