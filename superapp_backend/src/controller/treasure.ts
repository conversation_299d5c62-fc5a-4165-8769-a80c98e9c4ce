import { addClaimHistory, addholding, getAvailableHolding, getCoinDb, getTreasuresDb, getTrsrProducts, getUserCollectedTreasure, updateHolding, updateHoldingStatus, updateHuntAvilability, updateProduct } from "../services/treasure";
import { handleResponse } from "../common";
import { UserAddress } from "types/db_types";
import { addAddressQuery, getProductItemByIdQuery, getUserByIDQuery } from "../common/db_constants";
import { query } from "../db";
import { sendEmail } from "../services/mailer";
import { bodyMetaPass, bodyProduct, subjectMetaPass, subjectProduct } from "../services/treasure_mail";

export const getTreasures = async (req: any, res: any) => {
    let response: any = {}
    try {
        const { artistId } = req.query;

        let coins = await getTreasuresDb(artistId ? Number(artistId) : undefined)

        response = {
            status: true,
            message: `Success`,
            data: coins

        }
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}

export const getCoin = async (req: any, res: any) => {
    let response: any = {}
    const user = req.user;
    try {
        const { huntId, coinId } = req.params;

        if (!huntId || !coinId) {
            response = {
                status: false,
                message: `ArtistId missing`
            }
            return handleResponse(res, response, 400);
        }

        let coins = await getCoinDb(user.id, Number(huntId), Number(coinId));

        response = {
            status: true,
            message: `Success`,
            data: coins
        }
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}

export const claimCoin = async (req: any, res: any) => {
    let response: any = {}
    const user = req.user;
    try {
        const { huntId, coinId } = req.body;

        if (!huntId || !coinId) {
            response = {
                status: false,
                message: `Params missing`
            }
            return handleResponse(res, response, 400);
        }

        const userDb = await query(getUserByIDQuery, [user.id]);

        if (!userDb.rowCount) {
            response = {
                status: false,
                message: `Invalid User`
            }
            return handleResponse(res, response, 400);
        }

        let coins = await getCoinDb(user.id, Number(huntId), Number(coinId))

        if (coins.is_over == true) {
            response = {
                status: false,
                message: `Treasure not available`
            }
            return handleResponse(res, response, 400);
        }

        if (coins.claimed == true) {
            response = {
                status: false,
                message: `You have already collected this coin`
            }
            return handleResponse(res, response, 400);
        }

        let holding = await getAvailableHolding(user.id, coins.hunt_id);

        if (!holding.length) {
            holding = await addholding(user.id, coins.hunt_id, coins.quantity_per_claim)
        } else {
            holding = await updateHolding(coins.quantity_per_claim, holding[0].id);
        }
        coins.claimed = true;

        await addClaimHistory(user.id, Number(coins.coin_id), holding[0].id, coins.quantity_per_claim);

        const claimedAll = holding[0].available == coins.total_coins_to_collect

        if (claimedAll) {
            await updateHoldingStatus(holding[0].id)

            if (coins.is_limited_quantity) {
                await updateHuntAvilability(coins.hunt_id);
                coins.is_over = (coins.available - 1) == 0;
            } else {
                //sending email if it is lottery claim hunt 
                const body = bodyMetaPass(userDb.rows[0].user_name ? userDb.rows[0].user_name : 'User');
                await sendEmail([userDb.rows[0].email], subjectMetaPass, body, undefined, undefined)
            }
        }

        response = {
            status: true,
            message: `Success`,
            data: {
                ...coins,
                coins_collected: coins.coins_collected + 1
            }
        }
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}

export const claimProduct = async (req: any, res: any) => {
    let response: any = {}
    const user = req.user;
    try {
        const { productItemId, productClaimId } = req.body;
        const address: UserAddress = req.body.address;

        if (!productItemId || !productClaimId) {
            response = {
                status: false,
                message: `Params missing`
            }
            return handleResponse(res, response, 400);
        }

        if (!address.first_name || !address.last_name || !address.address_line_one || !address.city || !address.state || !address.pincode || !address.phone || !address.email) {
            response = {
                status: false,
                message: `Missing Params`
            }
            return handleResponse(res, response, 400);
        }

        const pdtItem = await query(getProductItemByIdQuery, [productItemId]);

        if (!pdtItem.rowCount) {
            response = {
                status: false,
                message: `Invalid product item id`
            }
            return handleResponse(res, response, 400);
        }

        const treasure = await getUserCollectedTreasure(productClaimId, user.id);

        if (!treasure.length) {
            response = {
                status: false,
                message: `Invalid claim id`
            }
            return handleResponse(res, response, 400);
        }

        if (treasure[0].product_item_id != null) {
            response = {
                status: false,
                message: `Already claimed`
            }
            return handleResponse(res, response, 400);
        }

        const addAddressDb = await query(addAddressQuery, [user.id, address.first_name, address.last_name, address.address_line_one, address.address_line_two, address.city, address.state, address.pincode, address.phone, address.email, address.country, address.address_line_three]);

        if (!addAddressDb.rowCount) {
            response = {
                status: false,
                message: `Error in saving Address`
            }
            return handleResponse(res, response, 400);
        }

        await updateProduct(productClaimId, productItemId, addAddressDb.rows[0].id);

        const addressStr = `${address.address_line_one}, ${address.address_line_two}, \n ${address.address_line_three}, ${address.city} \n   ${address.state} - ${address.pincode}`
        const body = bodyProduct(address.first_name + ' ' + address.last_name, pdtItem.rows[0].name, addressStr);
        await sendEmail([address.email], subjectProduct, body, undefined, undefined)

        response = {
            status: true,
            message: `Success`
        }
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}

export const getTreasureProducts = async (req: any, res: any) => {
    let response: any = {}
    try {
        const { artistId } = req.params;

        if (!artistId) {
            response = {
                status: false,
                message: `ArtistId missing`
            }
            return handleResponse(res, response, 400);
        }

        const products = await getTrsrProducts(artistId)

        response = {
            status: true,
            message: `Success`,
            data: {
                products
            }
        }
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}