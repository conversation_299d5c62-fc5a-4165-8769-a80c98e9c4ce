import path from 'path';
import { handleResponse } from "../common";
import { checkDir, removeFile } from "../services/file";
import { getArtistTracks, getTrackFile, karaokeLocalOutDir, karaokeOutDir, mixAudio, saveRecording } from "../services/karaoke";
import { uploadFile } from '../services/aws';

export const getKaraokeTracks = async (req: any, res: any) => {
  let response: any = {}
  try {
    const { artistId } = req.params;

    if (!artistId) {
      response = {
        status: false,
        message: `Artist Id Not Available`
      }
      return handleResponse(res, response, 400);
    }

    const result = await getArtistTracks(artistId);

    if (!result.status) {
      response = {
        status: true,
        message: `Track Not Available`
      }
      return handleResponse(res, response, 200);
    }
    response = {
      status: true,
      message: `Success`,
      data: {
        track: result.data
      }
    }
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error
    }
    return handleResponse(res, response, 500);
  }
}


export const karaokeMix = async (req: any, res: any) => {
  let response: any = {}

  try {
    // Gets User Object
    const user = req.user;

    // Checks for the user audio recording
    if (!req.file) {
      response = {
        status: false,
        message: `Recording Not Available`
      }
      return handleResponse(res, response, 400);
    }

    // Track id in database for fetching the track file
    const { track } = req.body;

    if (!track) {
      response = {
        status: false,
        message: `Invalid params`
      }
      return handleResponse(res, response, 400);
    }

    // User audio recordings local path
    const filePath = req.file.path;

    // Track File get URL and download the file to local dir and get local dir path
    const tRes = await getTrackFile(track)

    const outputDir = karaokeLocalOutDir;
    checkDir(outputDir);
    const timestamp = Date.now();
    const outputFileName = `${user.id}_${track}_${timestamp}.mp3`;
    const outputPath = outputDir + outputFileName;

    // Merge 2 audios Karaoke track and user audio and save to local dir
    await mixAudio(tRes.path, filePath, outputPath);

    removeFile(filePath);

    const karaokeFolderName = karaokeOutDir.replace(/^\/|\/$/g, ''); // Remove leading/trailing slashes

    // Upload the saved path to AWS S3
    const awsUrl = await uploadFile(outputFileName, outputPath, karaokeFolderName);

    await saveRecording(track, user.id, awsUrl);
    
    res.setHeader('Content-Type', 'audio/mpeg');
    res.setHeader('Content-Disposition', 'attachment; filename=file.mp3');
    const absolutePath = path.join(__dirname, '../..', outputPath);
    const absolutePathFiltered = absolutePath.replace('src/controller/', '')

    // Stream the file
    res.sendFile(absolutePathFiltered, (err: any) => {
      if (err) {
        console.error('Error sending file:', err);
        res.status(500).send('An error occurred');
      }
      removeFile(outputPath);
      return;
    });

  } catch (error) {
    if (req.file) {
      removeFile(req.file.path);
    }

    response = {
      status: false,
      message: `Request failed`,
      error: error
    }
    return handleResponse(res, response, 500);
  }
}
