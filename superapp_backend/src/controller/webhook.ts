import { PayPalWHRoot } from "types/paypal";
import { PayGLocalBody, PaymentCallbackDefault, PhonePeCallbackBody } from "../types";
import { PaymentCallbackRazor } from "../types/razor";
import logger from "../logger";
import { decodeBase64 } from "../services/pg";

export const paypalCallBack = async (req: any, res: any, next: any) => {
    try {
        const token = req.body as PayPalWHRoot;
        const data: PaymentCallbackDefault = {
            isCompleted: token.resource.status == 'COMPLETED',
            orderId: token.resource.supplementary_data.related_ids.order_id,
            data: token.resource
        };
        req.payment_data = data;
        console.log("Paypal Data", {data : JSON.stringify(req.body)});
        
        return next();
    } catch (error) {
        logger.error(`Error on capturing paypal callback : ${JSON.stringify(error)}`);
        return res.send(error);
    }
}

export const razorPayCallBack = async (req: any, res: any, next: any) => {
    try {
        const resp: PaymentCallbackRazor = req.body
        const data: PaymentCallbackDefault = {
            isCompleted: !resp.payload.payment.entity.error_code,
            orderId: resp.payload.payment.entity.order_id,
            data: resp
        };
        req.payment_data = data;
        return next();
    } catch (error) {
        return res.send(error);
    }
}

export const paygLocalCallback = async (req: any, res: any, next: any) => {
    try {
        const token = req.body as PayGLocalBody;
        const data: PaymentCallbackDefault = {
            isCompleted: token.status == 'SENT_FOR_CAPTURE',
            orderId: token.gid,
            data: token
        };
        req.payment_data = data;
        return next();
    } catch (error) {
        logger.error(`Error on capturing paypal callback : ${JSON.stringify(error)}`);
        return res.send(error);
    }
}

export const phonePeCallback = async (req: any, res: any, next: any) => {
    try {
        let response = req.body.response;
        response = decodeBase64(response) as PhonePeCallbackBody;
        
        logger.info("PhonePe decoded data:", {
            success: response.success,
            state: response.data?.state,
            merchantTransactionId: response.data?.merchantTransactionId
        });
        
        const data: PaymentCallbackDefault = {
            isCompleted: response.success && response.data.state === 'COMPLETED',
            orderId: response.data.merchantTransactionId,
            data: response
        };
        
        req.payment_data = data;
        logger.info("PhonePe Data processed successfully");
        
        return next();
    } catch (error) {
        logger.error(`Error on capturing phonepe callback : ${error}`);
        return res.status(500).send({ error: error instanceof Error ? error.message : String(error) });
    }
}
