import { v4 as uuidv4 } from "uuid";
import { query } from "../db";
import { handleResponse, reducePercent } from "../common";
import { addAddressQuery, getArtistMeetByArtIdQuery, getCalendlyCheckoutByUser, getInviteIDquery, getUserAddressByIdQuery, insertCalendlyCheckout, insertMeetLogsQuery, offerUsedCountQuery, udpateMeetLogcldlyQuery, updateCalendlyStatus, updateCalendlyWebhook, updateCalendlyWebhookOther, updatePrevCalendlyCheckout } from "../common/db_constants";
import { cleanUserName, CURRENCIES, PAYMENT_METHOD, PAYPAL_PLATFORM_FEES } from "../common/constants";
import { getMeetRequestByArtistDetailId, MEET_REQ_STATUS } from "../services/meet_auth";
import { cancelEvent, getEventAvailableSlots } from "../services/calendlyV1";
import { UserAddress } from "../types/db_types";
import { getOfferData } from "../services/orders";
import { createOrder } from "../services/paypal";
import { PayGLocalTxnData } from "../types";
import { initPayment } from "../services/payglocal";
import { PayInitBody } from "../types/pg_types";
import { runPayAPI } from "../services/pg";

const getFormattedDate = (daysToAdd = 0) => {
  const now = new Date();
  now.setDate(now.getDate() + daysToAdd);
  const formattedDate = now.toISOString().replace(/T\d{2}:\d{2}:\d{2}.\d{3}Z/, 'T24:00:00.000000Z');
  return formattedDate;
}

const fetchSlots = async (uuid: string) => {
  try {
    const startTime = getFormattedDate();
    const endTime = getFormattedDate(7);
    const eventURI = process.env.CALENDLY_URL + '/event_types/' + uuid;
    const data = await getEventAvailableSlots(eventURI, startTime, endTime);
    return data;
  } catch (error) {
    throw error;
  }
}

export const getAvailableSlots = async (req: any, res: any) => {
  let response = {};
  try {
    const user = req.user;
    const { artistMeetId } = req.body;
    if (!artistMeetId) {
      response = {
        status: false,
        message: "Artist Id Missing",
      };
      return handleResponse(res, response, 400);
    }
    const aritstMeets = await query(getArtistMeetByArtIdQuery, [
      artistMeetId,
      CURRENCIES.INR,
    ]);

    if (!aritstMeets.rowCount) {
      response = {
        status: false,
        message: "Artist Not Found",
      };
      return handleResponse(res, response, 400);
    }
    const meetData = aritstMeets.rows[0];
    const userMeetRequests = await getMeetRequestByArtistDetailId(
      meetData.book_ids,
      user.id,
      [MEET_REQ_STATUS.ACCEPTED]
    );
    if (!userMeetRequests.data.length && meetData.iswhitelisted) {
      response = {
        status: false,
        message: "Not eligible",
      };
      return handleResponse(res, response, 400);
    }
    const slotData = await fetchSlots(meetData.source_id);
    response = {
      status: true,
      message: 'Success',
      data: {
        price: Number(meetData.price),
        currencies: meetData.currencies,
        tax: meetData.tax,
        name: meetData.name,
        image: meetData.image,
        duration: meetData.duration,
        artsit_name: meetData.artist_name,
        slots: slotData,
        source_id: meetData.source_id
      }
    }
    return handleResponse(res, response, 200);

  } catch (error: any) {
    response = {
      status: false,
      message: 'Request Failed'
    }
    return handleResponse(res, response, 500);
  }
}

export const startCheckout = async (req: any, res: any) => {
  let response = {};
  const address: UserAddress = req.body.address;
  const address_id: number = req.body.address_id;
  const { artistMeetId, coupon, email_id } = req.body;
  let currency = req.body.currency || CURRENCIES.INR;

  try {
    if (!email_id) {
      response = {
        status: false,
        message: "Email Not Found",
      };
      return handleResponse(res, response, 400);
    }
    const email = cleanUserName(email_id)
    const user = req.user;
    const aritstMeets = await query(getArtistMeetByArtIdQuery, [
      artistMeetId,
      currency,
    ]);

    if (!aritstMeets.rowCount) {
      response = {
        status: false,
        message: "Artist Not Found",
      };
      return handleResponse(res, response, 400);
    }

    let addressData: any = {
      address_id
    };

    if (!Object.values(CURRENCIES).includes(Number(currency))) {
      response = {
        status: false,
        message: `Invalid Currency`,
      };
      return handleResponse(res, response, 400);
    }

    if (address && !address_id) {
      const addAddressDb = await query(addAddressQuery, [
        user.id,
        address.first_name,
        address.last_name,
        address.address_line_one,
        address.address_line_two,
        address.city,
        address.state,
        address.pincode,
        address.phone,
        address.email,
        address.country,
        address.address_line_three,
      ]);

      if (!addAddressDb.rowCount) {
        response = {
          status: false,
          message: `Error in saving Address`,
        };
        return handleResponse(res, response, 400);
      }
      addressData = {
        address_id: addAddressDb.rows[0].id,
        ...addAddressDb.rows[0]
      };
    } else {
      const address = await query(getUserAddressByIdQuery, [address_id]);
      addressData = {
        address_id: address.rows[0].id,
        ...address.rows[0]
      };
    }

    let discountData: any;
    if (coupon) {
      const couponData = await getOfferData(coupon, artistMeetId, undefined, undefined, undefined);
      if (couponData.length) {
        discountData = couponData[0]
      }
    }

    if (discountData) {
      const countData = await query(offerUsedCountQuery, [user.id, discountData.id]);
      if (!discountData.unlimited) {
        if (countData.rowCount) {
          if (countData.rows[0].total_count >= discountData.total_limit) {
            response = {
              status: false,
              message: `Invalid Coupon`
            }
            return handleResponse(res, response, 400);
          }
        }
      }

      if (discountData.user_limited) {
        if (countData.rows[0].user_count >= discountData.user_limit) {
          response = {
            status: false,
            message: `Invalid Coupon`
          }
          return handleResponse(res, response, 400);
        }
      }
    }
    let amount = Number(aritstMeets.rows[0].price)
    const payment_method: number = req.body.payment_method || PAYMENT_METHOD.PAYPAL;
    const priceData = aritstMeets.rows[0].currencies.find((obj: any) => { return Number(obj.currency_id) === Number(currency) });

    //Discounting price
    if (discountData && Number(discountData.discount) > 0) {
      amount = reducePercent(amount, priceData.gst, Number(discountData.discount));
    }
    await query(updatePrevCalendlyCheckout, [user.id, payment_method, artistMeetId])
    const insertResponse = await query(insertCalendlyCheckout,
      [
        addressData.address_id, artistMeetId, discountData ? discountData.id : null,
        user.id, payment_method,
        false, 'INIT',
        null, null,
        amount, email
      ]);
    if (!insertResponse.rowCount) {
      response = {
        status: false,
        message: 'Error in saving data'
      }
      return handleResponse(res, response, 400);
    }
    handleResponse(res, {}, 200);
  } catch (error: any) {
    response = {
      status: false,
      message: 'Request Failed'
    }
    return handleResponse(res, response, 500);
  }
}

export const calendlyMeetPayment = async (req: any, res: any) => {
  let response = {};
  const event_id = req.body.event_id;
  if (!event_id) {
    response = {
      status: false,
      message: "Event Not Found",
    };
    return handleResponse(res, response, 400);
  }
  try {
    const user = req.user;
    const paymentData = await query(getCalendlyCheckoutByUser, [user.id, event_id]);
    const data = paymentData.rows[0];
    if (!data) {
      response = {
        status: false,
        message: "Booking not found",
      };
      return handleResponse(res, response, 400);
    }
    let payment: any;
    let price = data.price;
    const address = await query(getUserAddressByIdQuery, [data.address_id]);
    const addressData = address.rows[0];
    if (data.payment_id == PAYMENT_METHOD.PAYPAL) {
      price += PAYPAL_PLATFORM_FEES * price;
      payment = await createOrder(Number(price).toFixed(2).toString(), 'USD');
    } else if (data.payment_id == PAYMENT_METHOD.PAYGLOCAL) {
      const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
      const payGlocalData: PayGLocalTxnData = {
        totalAmount: Number(price).toFixed(2).toString(),
        txnCurrency: "USD",
        billingData: {
          firstName: addressData.first_name || addressData.firstName || '',
          lastName: addressData.last_name || addressData.lastName || '',
          addressStreet1: addressData.address_line_one,
          addressCity: addressData.city,
          addressState: addressData.state,
          addressPostalCode: addressData.pincode,
          addressCountry: "US",
          emailId: addressData.email
        }
      }
      payment = await initPayment(txnId, payGlocalData);
    } else {
      const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
      const phonepeData: PayInitBody = {
        merchantTransactionId: txnId,
        amount: parseInt((Number(price) * 100).toString()),
        merchantUserId: user.id
      }
      const callBackUrl = process.env.MEET_CALLBACK_URL
      payment = await runPayAPI(phonepeData, callBackUrl!!);
    }

    let discountData: any;
    if (data.coupon) {
      const couponData = await getOfferData(data.coupon, data.meet_id, undefined, undefined, undefined);
      if (couponData.length) {
        discountData = couponData[0]
      }
    }

    if (discountData) {
      const countData = await query(offerUsedCountQuery, [user.id, discountData.id]);
      if (!discountData.unlimited) {
        if (countData.rowCount) {
          if (countData.rows[0].total_count >= discountData.total_limit) {
            response = {
              status: false,
              message: `Invalid Coupon`
            }
            return handleResponse(res, response, 400);
          }
        }
      }

      if (discountData.user_limited) {
        if (countData.rows[0].user_count >= discountData.user_limit) {
          response = {
            status: false,
            message: `Invalid Coupon`
          }
          return handleResponse(res, response, 400);
        }
      }
    }

    const insertLog = await query(insertMeetLogsQuery, [
      user.id, null, null, null,
      data.meet_id, payment.order_id,
      Number(data.payment_id) === 1 ? 1 : 2,
      data.address_id,
      discountData ? discountData.id : null,
      price,  
      data.payment_id, 
      data.base_amount
    ]);
    if (!insertLog.rowCount) {
      response = {
        status: false,
        message: "Failed creating meet",
      };
      return handleResponse(res, response, 400);
    }

    await query(udpateMeetLogcldlyQuery, [insertLog.rows[0].id, data.id])

    response = {
      status: true,
      message: "Success",
      data: {
        selectedPayment: {
          ...payment,
          id: insertLog.rows[0].id
        }
      },
    };
    return handleResponse(res, response, 200);
  } catch (error: any) {
    response = {
      status: false,
      message: 'Request Failed'
    }
    return handleResponse(res, response, 500);
  }
}

export const calendlyCallback = async (req: any, res: any) => {
  const data = req.body;
  if (!data) {
    return res.status(200).send("No Body data");
  }

  if (data.event != 'invitee.created') {
    return res.status(200).send("Ok");
  }

  const payload = data.payload;

  if (!payload) {
    return res.status(200).send("No Payload data");
  }
  const eventUrl = payload.event;
  const email = cleanUserName(payload.email);
  const eventUUID = eventUrl.match(/\/([^\/]+)$/)[1];

  const slotData = payload.scheduled_event;

  if (!slotData) {
    return res.status(200).send("No Slot data");
  }

  const slotUrl = slotData.event_type;
  const slotUUID = slotUrl.match(/\/([^\/]+)$/)[1];
  try {
    const result = await query(updateCalendlyWebhook, [eventUUID, 'PAYMENT_PENDING', email, slotUUID]);
    await query(updateCalendlyWebhookOther, ['DROPPED', email, slotUUID])
    if (!result.rowCount) {
      await cancelEvent(eventUUID, 'Invalid Email ID')
      return res.send('No Record Found');
    }
    return res.send('Success');
  } catch (error) {
    console.error("Calendly Callback Error", { error });

    return res.status(200).send("Error updating DB");
  }
}

export const cancelCalendlyEvent = async (req: any, res: any) => {
  let response = {};

  try {
    const { source_id, email_id } = req.body;

    if (!source_id || !email_id) {
      response = {
        status: false,
        message: "Event Id or Email is Not Found",
      };
      return handleResponse(res, response, 400);
    }
    const email = cleanUserName(email_id);
    const calendlyData = await query(getInviteIDquery, [email, source_id]);

    if (!calendlyData.rowCount) {
      response = {
        status: false,
        message: "Event Id or Email is Invalid",
      };
      return handleResponse(res, response, 400);
    }

    const inviteId = calendlyData.rows[0].invite_id;

    await cancelEvent(inviteId)

    const dbRes = await query(updateCalendlyStatus, ['PAYMENT_FAILED', email, source_id, inviteId, false])
    if (!dbRes.rowCount) {
      response = {
        status: false,
        message: "Error updating DB",
      };
      return handleResponse(res, response, 400);
    }

    response = {
      status: true,
      message: 'Canelled event'
    }
    return handleResponse(res, response, 200);

  } catch (error: any) {
    response = {
      status: false,
      message: 'Request Failed',
      error: error
    }
    return handleResponse(res, response, 500);
  }
}
