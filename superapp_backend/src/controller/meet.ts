import { PayInitBody, PaymentCallback } from "../types/pg_types";
import {
  handleResponse,
  reducePercent,
} from "../common";
import { query } from "../db";
import {
  getSlotsApi,
  registerUser
} from "../services/sales_assist";
import { ResisterRoot } from "../types/sale_assist";
import { v4 as uuidv4 } from "uuid";
import { decodeBase64, runPayAPI } from "../services/pg";
import { addAddressQuery, getArtistBookQuery, getArtistMeetByArtIdQuery, getCldlyCheckoutByIDquery, getUserAddressByIdQuery, getUserByFormIdQuery, insertPgLogsQuery, offerUsedCountQuery, updateCalendlyStatus, updateMeetScheduledQuery, updatePayStatusQuery, getArtistMeetCurrencyOptionsQuery } from "../common/db_constants";
import { CURRENCIES, PAYMENT_METHOD, PAYPAL_PLATFORM_FEES, PHONE_PE_STATUS } from "../common/constants";
import logger from "../logger";
import {
  MEET_REQ_STATUS,
  getMeetRequestByArtistDetailId,
  sendMailToArtist,
} from "../services/meet_auth";
// import { userMeetRequest } from "../controller/meet_auth";
import { generateInvoice, getOfferData } from "../services/orders";
import { UserAddress } from "types/db_types";
import { createOrder } from "../services/paypal";
import { PayGLocalTxnData, PaymentCallbackDefault } from "../types";
import { initPayment } from "../services/payglocal";
import {   generateSignedUrl, getSignedPlaylistWithSegments } from "../services/aws";
import { cancelEvent } from "../services/calendlyV1";

const insertUSer = `insert into artist_meet_users(user_id,people_id,session_id,unique_field_value,form_id)
values ($1,$2,$3,$4,$5)
on conflict on constraint unq_id do update set
form_id = $5,
session_id = $3,
updated = now();`;

const insertMeetLogsQuery = `insert into artist_meeting_logs (user_id,slot_date,slot_time,form_id,artist_meet_id,merchant_transaction_id,payment_currency,address_id,offer_id,purchased_price,payment_method,base_price)
values ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12)
returning *;`;

const selectMeetLogsQuery = `select * from artist_meeting_logs where slot_date = $1 and slot_time = $2 and payment_status in (1,2,3)`;

const getLogsByMidQuery = `select * from artist_meeting_logs where merchant_transaction_id = $1 and payment_status = any($2)`;

const getBlockedSlotsQuery = `select slot_date,ARRAY_AGG(slot_time) slot_times from artist_meeting_logs 
where payment_status in (1,2,3)
group by slot_date
having TO_TIMESTAMP(slot_date, 'MM/DD/YYYY') >= date(now());`;

export const getMeetIdFromMeetValidation=`SELECT *
FROM public.artist_meet_validation
WHERE user_id = $1 AND artist_meet_detail_id = $2;`;


export const regUserMeet = async (
  userId: number,
  payload: ResisterRoot,
  source_id: string
) => {
  try {
    const registerUsers = await registerUser(source_id, payload);

    await query(insertUSer, [
      userId,
      registerUsers.people_id,
      payload.session_id,
      payload.user_data.country_code + payload.user_data.mobile_number,
      registerUsers.form_id,
    ]);

    const slots = await getSlotsApi(registerUsers.form_id, source_id);

    const dbSlots = await query(getBlockedSlotsQuery, []);

    if (dbSlots.rowCount) {
      for (let index = 0; index < dbSlots.rowCount; index++) {
        const element = dbSlots.rows[index];
        let times = slots[element.slot_date];
        times = times
          ? times.filter((el: any) => !element.slot_times.includes(el))
          : [];
        slots[element.slot_date] = times;
      }
    }

    return {
      status: true,
      registerUsers,
      slots,
    };
  } catch (error) {
    return Promise.reject({
      status: false,
      message: `Error on registering user for artist meet`,
    });
  }
};

export const registerUserMeet = async (req: any, res: any) => {
  let response = {};
  try {
    const user = req.user;
    const { mobile, artistMeetId, name } = req.body;
    if (!user.mobile && !mobile) {
      response = {
        status: false,
        message: "Invalid Mobile",
      };
      return handleResponse(res, response, 400);
    }

    if (!artistMeetId) {
      response = {
        status: false,
        message: "Artist Id Missing",
      };
      return handleResponse(res, response, 400);
    }

    const aritstMeets = await query(getArtistMeetByArtIdQuery, [
      artistMeetId,
      CURRENCIES.INR,
    ]);

    if (!aritstMeets.rowCount) {
      response = {
        status: false,
        message: "Artist Not Found",
      };
      return handleResponse(res, response, 400);
    }

    const meetData = aritstMeets.rows[0];

    const userMeetRequests = await getMeetRequestByArtistDetailId(
      meetData.book_ids,
      user.id,
      [MEET_REQ_STATUS.ACCEPTED]
    );

    if (!userMeetRequests.data.length && meetData.iswhitelisted) {
      response = {
        status: false,
        message: "Not eligible",
      };
      return handleResponse(res, response, 400);
    }

    const payload: ResisterRoot = {
      session_id: uuidv4(),
      user_data: {
        name: name ? name : "",
        mobile_number: user.mobile ? user.mobile.slice(2) : mobile,
        country_code: "+91",
      },
    };

    const registerUser = await regUserMeet(
      user.id,
      payload,
      meetData.source_id
    );

    response = {
      status: true,
      message: 'Success',
      data: {
        price: Number(meetData.price),
        currencies: meetData.currencies,
        tax: meetData.tax,
        name: meetData.name,
        image: meetData.image,
        duration: meetData.duration,
        artsit_name: meetData.artist_name,
        user: registerUser.registerUsers,
        slots: registerUser.slots
      }
    }
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: 'Request Failed'
    }
    return handleResponse(res, response, 500);
  }
}

export const initMeet = async (req: any, res: any) => {
  let response = {};
  const user = req.user;
  const { slotDate, slotTime, formId, artistMeetId } = req.body;

  const address: UserAddress = req.body.address;
  const address_id: number = req.body.address_id;

  // //update address
  // if (!address || !address.first_name || !address.last_name || !address.address_line_one || !address.city || !address.state || !address.pincode || !address.phone || !address.email) {
  //     response = {
  //         status: false,
  //         message: `Missing Params`
  //     }
  //     return handleResponse(res, response, 400);
  // }

  if (!slotDate || !formId || !slotTime || !artistMeetId) {
    response = {
      status: false,
      message: "Invalid Params",
    };
    return handleResponse(res, response, 400);
  }

  // if (!isValidDateFormat(slotDate)) {
  //     response = {
  //         status: false,
  //         message: 'Invalid Date Format'
  //     }
  //     return handleResponse(res, response, 400);
  // }

  // if (!isValidTimeFormat(slotTime)) {
  //     response = {
  //         status: false,
  //         message: 'Invalid Time Format'
  //     }
  //     return handleResponse(res, response, 400);
  // }
  const coupon = req.body.coupon;

  try {
    let discountData: any;
    if (coupon) {
      const couponData = await getOfferData(coupon, artistMeetId, undefined, undefined, undefined);
      if (couponData.length) {
        discountData = couponData[0]
      }
    }

    if (discountData) {
      const countData = await query(offerUsedCountQuery, [user.id, discountData.id]);

      if (!discountData.unlimited) {
        if (countData.rowCount) {
          if (countData.rows[0].total_count >= discountData.total_limit) {
            response = {
              status: false,
              message: `Invalid Coupon`
            }
            return handleResponse(res, response, 400);
          }
        }
      }

      if (discountData.user_limited) {
        if (countData.rows[0].user_count >= discountData.user_limit) {
          response = {
            status: false,
            message: `Invalid Coupon`
          }
          return handleResponse(res, response, 400);
        }
      }
    }

    const getSlot = await query(selectMeetLogsQuery, [slotDate, slotTime]);

    if (getSlot.rowCount) {
      response = {
        status: false,
        message: "Slot Blocked",
      };
      return handleResponse(res, response, 400);
    }

    const userMeet = await query(getUserByFormIdQuery, [formId]);

    if (!userMeet.rowCount) {
      response = {
        status: false,
        message: "User not found",
      };
      return handleResponse(res, response, 400);
    }

    const aritstMeets = await query(getArtistMeetByArtIdQuery, [
      artistMeetId,
      CURRENCIES.INR,
    ]);

    if (!aritstMeets.rowCount) {
      response = {
        status: false,
        message: "Artist Not Found",
      };
      return handleResponse(res, response, 400);
    }

    let addressId = address_id;
    if (address && !address_id) {
      const addAddressDb = await query(addAddressQuery, [
        user.id,
        address.first_name,
        address.last_name,
        address.address_line_one,
        address.address_line_two,
        address.city,
        address.state,
        address.pincode,
        address.phone,
        address.email,
        address.country,
        address.address_line_three,
      ]);

      if (!addAddressDb.rowCount) {
        response = {
          status: false,
          message: `Error in saving Address`,
        };
        return handleResponse(res, response, 400);
      }
      addressId = addAddressDb.rows[0].id;
    }

    let amount = Number(aritstMeets.rows[0].price)
    const baseAmount = Number(aritstMeets.rows[0].price)
    //Discounting price
    if (discountData && Number(discountData.discount) > 0) {
      amount = reducePercent(amount, aritstMeets.rows[0].tax, Number(discountData.discount));
    }

    const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
    const data: PayInitBody = {
      merchantTransactionId: txnId,
      amount: parseInt((Number(amount) * 100).toString()),
      merchantUserId: user.id
    }

    const callBackUrl = process.env.MEET_CALLBACK_URL

    const payment = await runPayAPI(data, callBackUrl!!);

    const insertLog = await query(insertMeetLogsQuery, [
      user.id,
      slotDate,
      slotTime,
      formId,
      artistMeetId,
      payment.order_id,
      CURRENCIES.INR,
      addressId,
      discountData ? discountData.id : null,
      amount,
      PAYMENT_METHOD.PHONEPE,
      baseAmount
    ]);

    if (!insertLog.rowCount) {
      response = {
        status: false,
        message: "Failed creating meet",
      };
      return handleResponse(res, response, 400);
    }

    response = {
      status: true,
      message: "Success",
      data: {
        selectedPayment: payment
      },
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: "Request Failed",
    };
    return handleResponse(res, response, 500);
  }
};

export const initMeetV2 = async (req: any, res: any) => {
  let response = {};
  const user = req.user;
  const { artistMeetId, address_id, address,coupon, slotDate,slotTime} = req.body;

  if (!artistMeetId) {
    response = {
      status: false,
      message: "Invalid Params",
    };
    return handleResponse(res, response, 400);
  }

  try {
    let currency = req.body.currency || CURRENCIES.INR;

    if (!Object.values(CURRENCIES).includes(Number(currency))) {
      response = {
        status: false,
        message: `Invalid Currency`,
      };
      return handleResponse(res, response, 400);
    }

    let discountData: any;
    if (coupon) {
      const couponData = await getOfferData(coupon, artistMeetId, undefined, undefined, undefined);
      if (couponData.length) {
        discountData = couponData[0]
      }
    }

    if (discountData) {
      const countData = await query(offerUsedCountQuery, [user.id, discountData.id]);

      if (!discountData.unlimited) {
        if (countData.rowCount) {
          if (countData.rows[0].total_count >= discountData.total_limit) {
            response = {
              status: false,
              message: `Invalid Coupon`
            }
            return handleResponse(res, response, 400);
          }
        }
      }

      if (discountData.user_limited) {
        if (countData.rows[0].user_count >= discountData.user_limit) {
          response = {
            status: false,
            message: `Invalid Coupon`
          }
          return handleResponse(res, response, 400);
        }
      }
    }


    const aritstMeets = await query(getArtistMeetByArtIdQuery, [
      artistMeetId,
      currency,
    ]);

    if (!aritstMeets.rowCount) {
      response = {
        status: false,
        message: "Artist Not Found",
      };
      return handleResponse(res, response, 400);
    }

    let addressData: any = {
      address_id
    };
    if (address && !address_id) {
      const addAddressDb = await query(addAddressQuery, [
        user.id,
        address.first_name,
        address.last_name,
        address.address_line_one,
        address.address_line_two,
        address.city,
        address.state,
        address.pincode,
        address.phone,
        address.email,
        address.country,
        address.address_line_three,
      ]);

      if (!addAddressDb.rowCount) {
        response = {
          status: false,
          message: `Error in saving Address`,
        };
        return handleResponse(res, response, 400);
      }
      addressData = {
        address_id: addAddressDb.rows[0].id,
        ...addAddressDb.rows[0]
      };
    } else {
      const address = await query(getUserAddressByIdQuery, [address_id]);
      addressData = {
        address_id: address.rows[0].id,
        ...address.rows[0]
      };
    }

    let amount = Number(aritstMeets.rows[0].price)
    const payment_method: number = req.body.payment_method || PAYMENT_METHOD.PAYPAL;
    const baseAmount = Number(aritstMeets.rows[0].price)


    const priceData = aritstMeets.rows[0].currencies.find((obj: any) => { return Number(obj.currency_id) === Number(currency) });

    //Discounting price
    if (discountData && Number(discountData.discount) > 0) {
      amount = reducePercent(amount, priceData.gst, Number(discountData.discount));
    }

    let payment: any;
    if (payment_method == PAYMENT_METHOD.PAYPAL && currency == CURRENCIES.USD) {
      amount += PAYPAL_PLATFORM_FEES * amount;
      payment = await createOrder(Number(amount).toFixed(2).toString(), 'USD');
    } else if (payment_method == PAYMENT_METHOD.PAYGLOCAL && currency == CURRENCIES.USD) {
      const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
      const data: PayGLocalTxnData = {
        totalAmount: Number(amount).toFixed(2).toString(),
        txnCurrency: "USD",
        billingData: {
          firstName: addressData.first_name || addressData.firstName || '',
          lastName: addressData.last_name || addressData.lastName || '',
          addressStreet1: addressData.address_line_one,
          addressCity: addressData.city,
          addressState: addressData.state,
          addressPostalCode: addressData.pincode,
          addressCountry: "US",
          emailId: addressData.email
        }
      }
      payment = await initPayment(txnId, data);
    } else {
      const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
      const data: PayInitBody = {
        merchantTransactionId: txnId,
        amount: parseInt((Number(amount) * 100).toString()),
        merchantUserId: user.id
      }
      const callBackUrl = process.env.PHONEPE_CALLBACK_URL
      payment = await runPayAPI(data, callBackUrl!);
    }

    const insertLog = await query(insertMeetLogsQuery, [
      user.id,
      slotDate,
      slotTime,
      null,
      artistMeetId,
      payment.order_id,
      currency,
      addressData.address_id,
      discountData ? discountData.id : null,
      amount,
      payment.payment_method,
      baseAmount
    ]);

    if (!insertLog.rowCount) {
      response = {
        status: false,
        message: "Failed creating meet",
      };
      return handleResponse(res, response, 400);
    }

    response = {
      status: true,
      message: "Success",
      data: {
        selectedPayment: {
          ...payment,
          id: insertLog.rows[0].id
        }
      },
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: "Request Failed",
    };
    return handleResponse(res, response, 500);
  }
};

export const meetPayment = async (req: any, res: any) => {
  try {
    let response = req.body.response;
    response = decodeBase64(response) as PaymentCallback;
    await query(insertPgLogsQuery, [response.success, response.code, response]);

    const meetLog = await query(getLogsByMidQuery, [
      response.data.merchantTransactionId,
      [PHONE_PE_STATUS.PAYMENT_INIT.id, PHONE_PE_STATUS.PAYMENT_PENDING.id],
    ]);

    if (!meetLog.rowCount) {
      logger.error("Transaction Id not found");
      return res.send("OK");
    }

    if (response.code == PHONE_PE_STATUS.PAYMENT_SUCCESS.name) {
      let orderDetail = await query(updatePayStatusQuery, [
        PHONE_PE_STATUS.PAYMENT_SUCCESS.id,
        response.data.merchantTransactionId,
      ]);

      if (!orderDetail.rowCount) {
        logger.error("Transaction Id not found");
        return res.send("OK");
      }

      const aritstMeets = await query(getArtistMeetByArtIdQuery, [
        meetLog.rows[0].artist_meet_id,
        CURRENCIES.INR,
      ]);

      if (!aritstMeets.rowCount) {
        logger.error("Transaction Id not found");
        return res.send("OK");
      }

      await generateInvoice(
        meetLog.rows[0].id,
        meetLog.rows[0].user_id,
        "MEET"
      );

      const meet_id = meetLog.rows[0].artist_meet_id
      if(meet_id == 11){
        const userId = meetLog.rows[0].user_id
        const result = await query(getArtistBookQuery, [meet_id]);
        const artistData = result.rows[0];

        if (!result.rowCount) {
          response = {
            success: false,
            code: 404
          }
        }
        const request = await query(getMeetIdFromMeetValidation, [userId, meet_id]);

        await sendMailToArtist(
          result.rows[0]['name'],
          request.rows[0].id ?? '',
          result.rows[0].email,
          request.rows[0].request_meta_data,
          Number(meet_id),
          Number(artistData.meet_type)
        );

        logger.info(`Sent mail to the artist`);
      }

      // const meet = await scheduleMeeting(
      //   meetPaylod,
      //   aritstMeets.rows[0].source_id
      // );

      // if (meet) {
      // await query(updateMeetScheduledQuery, [meetLog.rows[0].id, invoice]);
      // }

      // const calendlyData = await query(getCldlyCheckoutByIDquery, [meetLog.rows[0].id]);

      // if(!calendlyData.rowCount) {
      //   logger.error(
      //     `Event Id is Invalid : ${meetLog.rows[0].artist_meet_id}`
      //   );
      //   return
      // }

      // await query(updateCalendlyStatus, ['PAYMENT_SUCCESS', calendlyData.rows[0].email, calendlyData.rows[0].source_id, calendlyData.rows[0].invite_id, true])

      return res.send("OK");
    } else if (
      response.code == PHONE_PE_STATUS.TIMED_OUT.name ||
      response.code == PHONE_PE_STATUS.PAYMENT_ERROR.name ||
      response.code == PHONE_PE_STATUS.PAYMENT_DECLINED.name
    ) {
      let orderDetail = await query(updatePayStatusQuery, [
        PHONE_PE_STATUS.PAYMENT_ERROR.id,
        response.data.merchantTransactionId,
      ]);

      const calendlyData = await query(getCldlyCheckoutByIDquery, [meetLog.rows[0].id]);

      if (!calendlyData.rowCount) {
        response = {
          status: false,
          message: "Event Id is Invalid",
        };
        return handleResponse(res, response, 400);
      }

      await cancelEvent(calendlyData.rows[0].invite_id)

      await query(updateCalendlyStatus, ['PAYMENT_FAILED', calendlyData.rows[0].email, calendlyData.rows[0].source_id, calendlyData.rows[0].invite_id, false])

      logger.log("Upadted Payment Count : ", orderDetail.rowCount);
      return res.send("OK");
    }
  } catch (error) {
    logger.log("Error on Upadted Payment Count : ", error);
    return res.send("OK");
  }
};

export const meetPaymentV2 = async (req: any, res: any, next: any) => {
  try {
    let response = {
      success: false,
      code: 400,
    };

    const data: PaymentCallbackDefault = req.payment_data;

    if (data.isCompleted) {
      response.success = true;
      response.code = 200;
    }

    const razorpay_order_id = data.orderId;

    const meetLog = await query(getLogsByMidQuery, [
      razorpay_order_id,
      [PHONE_PE_STATUS.PAYMENT_INIT.id, PHONE_PE_STATUS.PAYMENT_PENDING.id],
    ]);

    if (!meetLog.rowCount) {
      return next();
    }

    if (response.success) {
      let orderDetail = await query(updatePayStatusQuery, [
        PHONE_PE_STATUS.PAYMENT_SUCCESS.id,
        razorpay_order_id,
      ]);

      if (!orderDetail.rowCount) {
        logger.error(
          `Meet purchase order not updated order id : ${razorpay_order_id}`
        );
        return res.send("OK");
      }

      const invoice = await generateInvoice(
        meetLog.rows[0].id,
        meetLog.rows[0].user_id,
        "MEET",
        true,
        ""
      );

      const meet_id = meetLog.rows[0].artist_meet_id
      if(meet_id == 11){
        const userId = meetLog.rows[0].user_id
        const result = await query(getArtistBookQuery, [meet_id]);
        const artistData = result.rows[0];

        if (!result.rowCount) {
          response = {
            success: false,
            code: 404
          }
        }
        const request = await query(getMeetIdFromMeetValidation, [userId, meet_id]);

        await sendMailToArtist(
          result.rows[0]['name'],
          request.rows[0].id ?? '',
          result.rows[0].email,
          request.rows[0].request_meta_data,
          Number(meet_id),
          Number(artistData.meet_type)
        );

        logger.info(`Sent mail to the artist`);
      }

      await query(updateMeetScheduledQuery, [meetLog.rows[0].id, invoice]);

      const calendlyData = await query(getCldlyCheckoutByIDquery, [meetLog.rows[0].id]);

      if (!calendlyData.rowCount) {
        logger.error(
          `Event Id is Invalid : ${meetLog.rows[0].artist_meet_id}`
        );
        return
      }

      await query(updateCalendlyStatus, ['PAYMENT_SUCCESS', calendlyData.rows[0].email, calendlyData.rows[0].source_id, calendlyData.rows[0].invite_id, true])
      return res.send("OK");
    } else if (!response.success) {
      await query(updatePayStatusQuery, [
        PHONE_PE_STATUS.PAYMENT_ERROR.id,
        razorpay_order_id,
      ]);

      const calendlyData = await query(getCldlyCheckoutByIDquery, [meetLog.rows[0].id]);

      if (!calendlyData.rowCount) {
        logger.error(
          `Event Id is Invalid : ${meetLog.rows[0].artist_meet_id}`
        );
        return res.send("OK");
      }

      await cancelEvent(calendlyData.rows[0].invite_id)

      await query(updateCalendlyStatus, ['PAYMENT_FAILED', calendlyData.rows[0].email, calendlyData.rows[0].source_id, calendlyData.rows[0].invite_id, false])
      return res.send("OK");
    }
  } catch (error) {
    logger.error("Error on Upadted Payment Count : ", error);
    return res.send("OK");
  }
};

export const cancelPaymentMeet = async (req: any, res: any) => {
  let response: any = {};
  try {
    const { mTxnId } = req.body;
    let orderDetail = await query(updatePayStatusQuery, [
      PHONE_PE_STATUS.PAYMENT_DECLINED.id,
      mTxnId,
    ]);

    if (orderDetail.rowCount) {
      response = {
        status: false,
        message: "Not Updated",
      };
      return handleResponse(res, response, 400);
    }

    response = {
      status: true,
      message: "Cancelled",
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: "Request Failed",
    };
    return handleResponse(res, response, 500);
  }
};

export const getArtistBook = async (req: any, res: any) => {
  let response: any = {};
  const user = req.user;
  try {
    const { id } = req.params;

    if (!id) {
      response = {
        status: false,
        message: `Artist Id Not Available`,
      };
      return handleResponse(res, response, 400);
    }

    let aritstMeets: any;
    if(Number(id) == 10){
      aritstMeets = await query(getArtistMeetCurrencyOptionsQuery, [
        id,
      ]);
    }

    const result = await query(getArtistBookQuery, [id]);

    if (!result.rowCount) {
      response = {
        status: true,
        message: `Artist Not Available`,
      };
      return handleResponse(res, response, 200);
    }

    let artistBooks = result.rows[0];

    artistBooks = {
      ...artistBooks,
      eligibile: true,
      request_status: null,
      meet: null,
    };

    const userMeetRequests = await getMeetRequestByArtistDetailId(
      result.rows.map((el) => {
        return el.id;
      }),
      user.id,
      Object.keys(MEET_REQ_STATUS)
    );

    if (artistBooks.email) {
      const requests = userMeetRequests.data;

      artistBooks.request_status = requests.length ? requests[0].status : null;
      artistBooks.eligibile = requests.some(
        (req) =>
          Number(req.artist_meet_detail_id) === Number(artistBooks.id) &&
          req.status === MEET_REQ_STATUS.ACCEPTED
      );
    }

    if (artistBooks.metadata) {
      let videoPath = artistBooks.metadata.video_file;
      if (videoPath) {
        let result = videoPath.substring(0, videoPath.lastIndexOf('/') + 1) + 'output.m3u8';
        videoPath= generateSignedUrl(result, 30*60);

        let signed_playlist=await getSignedPlaylistWithSegments(videoPath)
        let signed_video_stream_url=`data:application/vnd.apple.mpegurl;base64,${Buffer.from(signed_playlist).toString('base64')}`
        artistBooks.metadata.video_stream_url = signed_video_stream_url;
      }
    }

    if (Number(id) == 10 && aritstMeets?.rows?.[0]?.currencies) {
      artistBooks.currencies = aritstMeets.rows[0].currencies;
    }

    response = {
      status: true,
      message: `Success`,
      data: [artistBooks],
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};

export const getArtistBookWithoutLogin = async (req: any, res: any) => {
  let response: any = {};
  try {
    const { id } = req.params;

    if (!id) {
      response = {
        status: false,
        message: `Artist Id Not Available`,
      };
      return handleResponse(res, response, 400);
    }

    const result = await query(getArtistBookQuery, [id]);

    if (!result.rowCount) {
      response = {
        status: true,
        message: `Artist Not Available`,
      };
      return handleResponse(res, response, 200);
    }

    let artistBooks = result.rows[0];

    artistBooks = {
      ...artistBooks,
      eligibile: !artistBooks.iswhitelisted,
      request_status: null,
      meet: null,
    };


    if (artistBooks.metadata) {
      let videoPath = artistBooks.metadata.video_file;
      if (videoPath) {
        let result = videoPath.substring(0, videoPath.lastIndexOf('/') + 1) + 'output.m3u8';
        videoPath= generateSignedUrl(result, 30*60);
        let signed_playlist=await getSignedPlaylistWithSegments(videoPath)
        let signed_video_stream_url=`data:application/vnd.apple.mpegurl;base64,${Buffer.from(signed_playlist).toString('base64')}`
        artistBooks.metadata.video_stream_url = signed_video_stream_url;
      }
    }

    response = {
      status: true,
      message: `Success`,
      data: [artistBooks],
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};
