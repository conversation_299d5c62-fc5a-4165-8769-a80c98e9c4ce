import { PayInitBody, PaymentCallback } from "../types/pg_types";
import {
    handleResponse,
    reducePercent,
} from "../common";
import { query } from "../db";
import { v4 as uuidv4 } from "uuid";
import { decodeBase64, runPayAPI } from "../services/pg";
import { CURRENCIES, PAYMENT_METHOD, PAYPAL_PLATFORM_FEES, PHONE_PE_STATUS } from "../common/constants";
import { addAddressQuery, getProductIdByItemIdQuery, getUserAddressByIdQuery, inserPaidVideoEntryQuery, insertPgLogsQuery, insertTxnQuery, offerUsedCountQuery, updatePaymentTxnQuery, updateVideoPurchasedQuery } from "../common/db_constants";
import logger from "../logger";
import { generateInvoice, getOfferData } from "../services/orders";
import { UserAddress } from "types/db_types";
import { createOrder } from "../services/paypal";
import { PayGLocalTxnData, PaymentCallbackDefault } from "../types";
import { initPayment } from "../services/payglocal";
import {  generateSignedUrl, getSignedPlaylistWithSegments } from "../services/aws";

const getPaidVideosQuery = `select pv.*,
array_agg(
	JSONB_BUILD_OBJECT(
		'currency',pcr.name,
		'currency_id',pcr.id,
		'price' , pp.price,
		'max_price' , pp.max_price,
		'gst' , pp.gst)
			) currencies
from paid_videos pv
inner join product_prices pp on pp.video_id = pv.id 
inner join payment_currency pcr on pcr.id = pp.currency
where pv.is_active = true
group by pv.id;`;

const getPaideVideoByIdQuery = `select pv.*,
EXISTS(select 1 from user_paid_video_entries where video_id = pv.id and user_id = $2 and valid_till >= now()) "purchased",
array_agg(
	JSONB_BUILD_OBJECT(
		'currency',pcr.name,
		'currency_id',pcr.id,
		'price' , pp.price,
		'max_price' , pp.max_price,
		'gst' , pp.gst)
			) currencies
from paid_videos pv
inner join product_prices pp on pp.video_id = pv.id 
inner join payment_currency pcr on pcr.id = pp.currency
where pv.id = $1 and pv.is_active = true
group by pv.id;`;

const getTxnByMerchantTxnQuery = `select * from payment_transactions where merchant_transaction_id = $1 and payment_status = ANY($2);`;

const userSubscribedVideosQuery = `select pv.*,upe.valid_till from user_paid_video_entries upe
inner join paid_videos pv on pv.id = upe.video_id
where upe.user_id = $1 and upe.valid_till >= now()`;

export const getVideos = async (req: any, res: any) => {
    let response: any = {};
    try {
        const videos = await query(getPaidVideosQuery, []);
        response = {
            status: true,
            message: `Success`,
            data: {
                videos: videos.rows,
            },
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error,
        };
        return handleResponse(res, response, 500);
    }
};

export const getVideoDetails = async (req: any, res: any) => {
    let response: any = {};
    const { videoId } = req.params;
    const { coupon } = req.query;

    if (!videoId) {
        response = {
            status: false,
            message: `Video Id Not Available`,
        };
        return handleResponse(res, response, 400);
    }

    const user = req.user;

    try {
        let discountData: any;
        if (coupon) {
            const couponData = await getOfferData(coupon, undefined, undefined, videoId, undefined);
            if (couponData.length) {
                discountData = couponData[0]
            }
        }

        if (discountData) {
            const countData = await query(offerUsedCountQuery, [user.id, discountData.id]);

            if (!discountData.unlimited) {
                if (countData.rowCount) {
                    if (countData.rows[0].total_count >= discountData.total_limit) {
                        response = {
                            status: false,
                            message: `Invalid Coupon`
                        }
                        return handleResponse(res, response, 400);
                    }
                }
            }

            if (discountData.user_limited) {
                if (countData.rows[0].user_count >= discountData.user_limit) {
                    response = {
                        status: false,
                        message: `Invalid Coupon`
                    }
                    return handleResponse(res, response, 400);
                }
            }
        }

        const videoDetails = await query(getPaideVideoByIdQuery, [
            videoId,
            user.id,
        ]);

        if (!videoDetails.rowCount) {
            response = {
                status: true,
                message: `Not Available`,
            };
            return handleResponse(res, response, 200);
        }

        let trailerPath = videoDetails.rows[0].trailer;
        let videoPath = videoDetails.rows[0].video;

        let videoDetail = videoDetails.rows[0];

        const isPurchased = videoDetails.rows[0].purchased;

        if (!isPurchased && trailerPath) {
            let result = trailerPath.substring(0, trailerPath.lastIndexOf('/') + 1) + 'output.m3u8';
            trailerPath = generateSignedUrl(result, 30*60);
            let signed_playlist=await getSignedPlaylistWithSegments(trailerPath)
            let signed_video_stream_url=`data:application/vnd.apple.mpegurl;base64,${Buffer.from(signed_playlist).toString('base64')}`

            videoDetail = {
                ...videoDetail,
                trailer_stream_url: signed_video_stream_url,
                video_stream_url: null,
            };
        } else if (isPurchased && videoPath) {
            let result = videoPath.substring(0, videoPath.lastIndexOf('/') + 1) + 'output.m3u8';
            videoPath=generateSignedUrl(result, 30*60)
            let signed_playlist=await getSignedPlaylistWithSegments(videoPath)
            let signed_video_stream_url=`data:application/vnd.apple.mpegurl;base64,${Buffer.from(signed_playlist).toString('base64')}`

            videoDetail = {
                ...videoDetail,
                trailer_stream_url: null,
                video_stream_url: signed_video_stream_url
            };
        }

        let currency = videoDetail.currencies.find(
            (el: any) => el.currency_id === CURRENCIES.INR
        );
        videoDetail.currency_symbol = "₹";
        videoDetail.price = currency.price;
        videoDetail.tax = currency.gst;
        videoDetail.currency = currency.currency;

        if (discountData && Number(discountData.discount) > 0) {
            videoDetail.price = reducePercent(videoDetail.price, videoDetail.tax, Number(discountData.discount));
            videoDetail.currencies = videoDetail.currencies.map((el: any) => {
                return {
                    ...el,
                    price: reducePercent(el.price, videoDetail.gst, discountData!.discount)
                }
            })
        }


        response = {
            status: true,
            message: `Success`,
            data: videoDetail,
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error,
        };
        return handleResponse(res, response, 500);
    }
};

export const subscribeVideo = async (req: any, res: any) => {
    let response: any = {};

    const { videoId, coupon } = req.body;
    const user = req.user;
    const address: UserAddress = req.body.address;
    const address_id: number = req.body.address_id;

    // //update address
    // if (!address || !address.first_name || !address.last_name || !address.address_line_one || !address.city || !address.state || !address.pincode || !address.phone || !address.email) {
    //     response = {
    //         status: false,
    //         message: `Missing Params`
    //     }
    //     return handleResponse(res, response, 400);
    // }

    if (!videoId) {
        response = {
            status: false,
            message: `Course Id Not Available`
        }
        return handleResponse(res, response, 400);
    }
    try {
        let discountData: any;
        if (coupon) {
            const couponData = await getOfferData(coupon, undefined, undefined, videoId, undefined);
            if (couponData.length) {
                discountData = couponData[0]
            }
        }

        if (discountData) {
            const countData = await query(offerUsedCountQuery, [user.id, discountData.id]);

            if (!discountData.unlimited) {
                if (countData.rowCount) {
                    if (countData.rows[0].total_count >= discountData.total_limit) {
                        response = {
                            status: false,
                            message: `Invalid Coupon`
                        }
                        return handleResponse(res, response, 400);
                    }
                }
            }

            if (discountData.user_limited) {
                if (countData.rows[0].user_count >= discountData.user_limit) {
                    response = {
                        status: false,
                        message: `Invalid Coupon`
                    }
                    return handleResponse(res, response, 400);
                }
            }
        }

        const videoDetails = await query(getPaideVideoByIdQuery, [
            videoId,
            user.id,
        ]);

        if (!videoDetails.rowCount) {
            response = {
                status: false,
                message: `Not Available`,
            };
            return handleResponse(res, response, 200);
        }

        if (videoDetails.rows[0].purchased) {
            response = {
                status: false,
                message: `Already Purchased`,
            };
            return handleResponse(res, response, 200);
        }

        let addressId = address_id;
        if (address && !address_id) {
            const addAddressDb = await query(addAddressQuery, [
                user.id,
                address.first_name,
                address.last_name,
                address.address_line_one,
                address.address_line_two,
                address.city,
                address.state,
                address.pincode,
                address.phone,
                address.email,
                address.country,
                address.address_line_three,
            ]);

            if (!addAddressDb.rowCount) {
                response = {
                    status: false,
                    message: `Error in saving Address`,
                };
                return handleResponse(res, response, 400);
            }
            addressId = addAddressDb.rows[0].id;
        }

        let amount = Number(videoDetails.rows[0].price);
        const baseAmount = Number(videoDetails.rows[0].price);

        if (discountData && Number(discountData.discount) > 0) {
            amount = reducePercent(amount, videoDetails.rows[0].gst, Number(discountData.discount));
        }

        const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
        let payment: any = null;
        let paymentStatus = PHONE_PE_STATUS.PAYMENT_SUCCESS.id;
        if (amount > 0) {
            const data: PayInitBody = {
                merchantTransactionId: txnId,
                amount: parseInt((Number(amount) * 100).toString()),
                merchantUserId: user.id
            }

            const callBackUrl = process.env.PAID_VIDEO_CALLBACK_URL

            payment = await runPayAPI(data, callBackUrl!!);

            paymentStatus = PHONE_PE_STATUS.PAYMENT_INIT.id;
        }

        const txn = await query(insertTxnQuery, [
            user.id,
            amount,
            txnId,
            paymentStatus,
            CURRENCIES.INR,
            PAYMENT_METHOD.PHONEPE,
            baseAmount
        ]);

        const order = await query(inserPaidVideoEntryQuery, [
            user.id,
            videoId,
            txn.rows[0].id,
            paymentStatus == PHONE_PE_STATUS.PAYMENT_SUCCESS.id ? true : false,
            videoDetails.rows[0].validity,
            addressId,
            discountData ? discountData.id : null,
        ]);

        response = {
            status: true,
            message: "Success",
            data: {
                id: order.rows[0].id,
                payment: payment
                    ? {
                        merchantTransactionId: payment.data.data.merchantTransactionId,
                        redirectInfo: payment.data.data.instrumentResponse.redirectInfo,
                    }
                    : {},
            },
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error,
        };
        return handleResponse(res, response, 500);
    }
};

export const subscribeVideoV2 = async (req: any, res: any) => {
    let response: any = {};

    const { videoId, coupon } = req.body;
    const user = req.user;
    const address: UserAddress = req.body.address;
    const address_id: number = req.body.address_id;

    if (!videoId) {
        response = {
            status: false,
            message: `Course Id Not Available`,
        };
        return handleResponse(res, response, 400);
    }

    const payment_method: number = req.body.payment_method || PAYMENT_METHOD.PAYPAL;
    let currency = req.body.currency || CURRENCIES.INR;
    if (!Object.values(CURRENCIES).includes(Number(currency))) {
        response = {
            status: false,
            message: `Invalid Currency`
        }
        return handleResponse(res, response, 400);
    }
    try {
        let discountData: any;
        if (coupon) {
            const couponData = await getOfferData(coupon, undefined, undefined, videoId, undefined);
            if (couponData.length) {
                discountData = couponData[0]
            }
        }

        if (discountData) {
            const countData = await query(offerUsedCountQuery, [user.id, discountData.id]);

            if (!discountData.unlimited) {
                if (countData.rowCount) {
                    if (countData.rows[0].total_count >= discountData.total_limit) {
                        response = {
                            status: false,
                            message: `Invalid Coupon`
                        }
                        return handleResponse(res, response, 400);
                    }
                }
            }

            if (discountData.user_limited) {
                if (countData.rows[0].user_count >= discountData.user_limit) {
                    response = {
                        status: false,
                        message: `Invalid Coupon`
                    }
                    return handleResponse(res, response, 400);
                }
            }
        }

        const videoDetails = await query(getPaideVideoByIdQuery, [
            videoId,
            user.id,
        ]);

        if (!videoDetails.rowCount) {
            response = {
                status: false,
                message: `Not Available`,
            };
            return handleResponse(res, response, 200);
        }

        if (videoDetails.rows[0].purchased) {
            response = {
                status: false,
                message: `Already Purchased`,
            };
            return handleResponse(res, response, 200);
        }

        const priceData = videoDetails.rows[0].currencies.find((obj: any) => {
            return Number(obj.currency_id) === Number(currency);
        });

        if (!priceData) {
            response = {
                status: false,
                message: `Currency data not found`,
            };
            return handleResponse(res, response, 400);
        }

        let addressData: any = {
            address_id
        };
        if (address && !address_id) {
            const addAddressDb = await query(addAddressQuery, [
                user.id,
                address.first_name,
                address.last_name,
                address.address_line_one,
                address.address_line_two,
                address.city,
                address.state,
                address.pincode,
                address.phone,
                address.email,
                address.country,
                address.address_line_three,
            ]);

            if (!addAddressDb.rowCount) {
                response = {
                    status: false,
                    message: `Error in saving Address`,
                };
                return handleResponse(res, response, 400);
            }
            addressData = {
                address_id: addAddressDb.rows[0].id,
                ...addAddressDb.rows[0]
            };
        } else {
            const address = await query(getUserAddressByIdQuery, [address_id]);
            addressData = {
                address_id: address.rows[0].id,
                ...address.rows[0]
            };
        }

        let amount = Number(priceData.price);
        const baseAmount = Number(priceData.price);
        if (discountData && Number(discountData.discount) > 0) {
            amount = reducePercent(amount, priceData.gst, Number(discountData.discount));
        }

        let payment: any = null;
        let paymentStatus = PHONE_PE_STATUS.PAYMENT_SUCCESS.id;
        if (amount > 0) {
            if (payment_method == PAYMENT_METHOD.PAYPAL && currency == CURRENCIES.USD) {
                amount += PAYPAL_PLATFORM_FEES * amount;
                payment = await createOrder(Number(amount).toFixed(2).toString(), 'USD');
            } else if (payment_method == PAYMENT_METHOD.PAYGLOCAL && currency == CURRENCIES.USD) {
                const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
                const data: PayGLocalTxnData = {
                    totalAmount: Number(amount).toFixed(2).toString(),
                    txnCurrency: "USD",
                    billingData: {
                        firstName: addressData.first_name || addressData.firstName || '',
                        lastName: addressData.last_name || addressData.lastName || '',
                        addressStreet1: addressData.address_line_one,
                        addressCity: addressData.city,
                        addressState: addressData.state,
                        addressPostalCode: addressData.pincode,
                        addressCountry: "US",
                        emailId: addressData.email
                    }
                }
                payment = await initPayment(txnId, data);
            } else {
                const txnId = uuidv4().replace(/-/g, '').substring(0, 20);
                const data: PayInitBody = {
                    merchantTransactionId: txnId,
                    amount: parseInt((Number(amount) * 100).toString()),
                    merchantUserId: user.id
                }
                const callBackUrl = process.env.PHONEPE_CALLBACK_URL
                payment = await runPayAPI(data, callBackUrl!);
            }
            paymentStatus = PHONE_PE_STATUS.PAYMENT_INIT.id;
        }

        const txn = await query(insertTxnQuery, [
            user.id,
            amount,
            payment.order_id,
            paymentStatus,
            currency,
            payment.payment_method,
            baseAmount
        ]);

        const order = await query(inserPaidVideoEntryQuery, [
            user.id,
            videoId,
            txn.rows[0].id,
            paymentStatus == PHONE_PE_STATUS.PAYMENT_SUCCESS.id ? true : false,
            videoDetails.rows[0].validity,
            addressData.address_id,
            discountData ? discountData.id : null,
        ]);

        response = {
            status: true,
            message: "Success",
            data: {
                selectedPayment: {
                    ...payment,
                    id: order.rows[0].id
                }
            },
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error,
        };
        return handleResponse(res, response, 500);
    }
};

export const videoTxnCallback = async (req: any, res: any) => {
    try {
        let response = req.body.response;
        response = decodeBase64(response) as PaymentCallback;
        await query(insertPgLogsQuery, [response.success, response.code, response]);

        const txnEntry = await query(getTxnByMerchantTxnQuery, [
            response.data.merchantTransactionId,
            [PHONE_PE_STATUS.PAYMENT_INIT.id, PHONE_PE_STATUS.PAYMENT_PENDING.id],
        ]);

        if (!txnEntry.rowCount) {
            logger.error("Transaction Id not found");
            return res.send("OK");
        }

        if (response.code == PHONE_PE_STATUS.PAYMENT_SUCCESS.name) {
            let orderDetail = await query(updatePaymentTxnQuery, [
                PHONE_PE_STATUS.PAYMENT_SUCCESS.id,
                response.data.merchantTransactionId,
            ]);

            if (!orderDetail.rowCount) {
                logger.error("Merchat Txn Id not found & updated");
                return res.send("OK");
            }

            
            const videoEntry = await query(
                `SELECT id FROM user_paid_video_entries WHERE txn_id = $1`,
                [txnEntry.rows[0].id]
            );

            if (!videoEntry.rowCount) {
                logger.error("Video entry not found for transaction");
                return res.send("OK");
            }

            const invoice = await generateInvoice(
                videoEntry.rows[0].id,
                txnEntry.rows[0].user_id,
                "PAID_VIDEO"
            );

            const course = await query(updateVideoPurchasedQuery, [
                txnEntry.rows[0].id,
                invoice,
            ]);

            if (!course.rowCount) {
                logger.error("Video Entry not found");
                return res.send("OK");
            }

            return res.send("OK");
        } else if (
            response.code == PHONE_PE_STATUS.TIMED_OUT.name ||
            response.code == PHONE_PE_STATUS.PAYMENT_ERROR.name ||
            response.code == PHONE_PE_STATUS.PAYMENT_DECLINED.name
        ) {
            let orderDetail = await query(updatePaymentTxnQuery, [
                PHONE_PE_STATUS.PAYMENT_ERROR.id,
                response.data.merchantTransactionId,
            ]);
            logger.log("Upadted Payment Count : ", orderDetail.rowCount);
            return res.send("OK");
        }
    } catch (error) {
        logger.log("Error on Upadted Payment Count : ", error);
        return res.send("OK");
    }
};

export const videoTxnCallbackV2 = async (req: any, res: any) => {
    try {
        let response = {
            success: false,
            code: 400,
        };

        const data: PaymentCallbackDefault = req.payment_data;

        if (data.isCompleted) {
            response.success = true;
            response.code = 200;
        }

        const razorpay_order_id = data.orderId;

        if (response.success) {
            const txnEntry = await query(getTxnByMerchantTxnQuery, [
                razorpay_order_id,
                [PHONE_PE_STATUS.PAYMENT_INIT.id, PHONE_PE_STATUS.PAYMENT_PENDING.id],
            ]);

            if (!txnEntry.rowCount) {
                return res.send("OK");
            }

            let orderDetail = await query(updatePaymentTxnQuery, [
                PHONE_PE_STATUS.PAYMENT_SUCCESS.id,
                razorpay_order_id,
            ]);

            if (!orderDetail.rowCount) {
                return res.send("OK");
            }

            // Get the user_paid_video_entries ID using the payment transaction ID
            const videoEntry = await query(
                `SELECT id FROM user_paid_video_entries WHERE txn_id = $1`,
                [txnEntry.rows[0].id]
            );

            if (!videoEntry.rowCount) {
                logger.error("Video entry not found for transaction");
                return res.send("OK");
            }

            const invoice = await generateInvoice(
                videoEntry.rows[0].id,
                txnEntry.rows[0].user_id,
                "PAID_VIDEO"
            );

            const course = await query(updateVideoPurchasedQuery, [
                txnEntry.rows[0].id,
                invoice,
            ]);

            if (!course.rowCount) {
                return res.send("OK");
            }
            return res.send("OK");
        } else if (!response.success) {
            await query(updatePaymentTxnQuery, [
                PHONE_PE_STATUS.PAYMENT_ERROR.id,
                razorpay_order_id,
            ]);
            logger.error("Upadted Payment failed");
            return res.send("OK");
        }
    } catch (error) {
        logger.error(`Error on Updating video payment : ${JSON.stringify(error)}`);
        return res.send("OK");
    }
};

export const getUserVideos = async (req: any, res: any) => {
    let response: any = {};
    const user = req.user;
    try {
        const videos = await query(userSubscribedVideosQuery, [user.id]);
        response = {
            status: true,
            message: `Success`,
            data: {
                videos: videos.rows,
            },
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error,
        };
        return handleResponse(res, response, 500);
    }
};

export const cancelVideoPurchase = async (req: any, res: any) => {
    let response = {};

    try {
        const { mTxnId } = req.body;

        if (!mTxnId) {
            response = {
                status: false,
                message: `Missing Params`,
            };
            return handleResponse(res, response, 400);
        }

        const txnEntry = await query(getTxnByMerchantTxnQuery, [
            mTxnId,
            [PHONE_PE_STATUS.PAYMENT_INIT.id, PHONE_PE_STATUS.PAYMENT_PENDING.id],
        ]);

        if (!txnEntry.rowCount) {
            response = {
                status: false,
                message: `Purchase transaction not found`,
            };
            return handleResponse(res, response, 400);
        }

        const orderDetail = await query(updatePaymentTxnQuery, [
            PHONE_PE_STATUS.PAYMENT_DECLINED.id,
            mTxnId,
        ]);

        if (!orderDetail.rowCount) {
            response = {
                status: false,
                message: `Purchase transaction not updated`,
            };
            return handleResponse(res, response, 400);
        }

        response = {
            status: true,
            message: `Purchase cancelled`,
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error,
        };
        return handleResponse(res, response, 500);
    }
};

export const getProductIdByItemId = async (req: any, res: any) => {
    let response = {};

    try {
        const { itemId, productType } = req.body;

        if (!itemId || !productType) {
            response = {
                status: false,
                message: `Missing Params`,
            };
            return handleResponse(res, response, 400);
        }

        const product = await query(getProductIdByItemIdQuery, [itemId, productType]);

        if (!product.rowCount) {
            response = {
                status: false,
                message: `Product not found`,
            };
            return handleResponse(res, response, 400);
        }

        response = {
            status: true,
            message: `Success`,
            data: {
                productId: product.rows[0].id,
            },
        };
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error,
        };
        return handleResponse(res, response, 500);
    }
};
