import { handleResponse } from "../common";
import { query } from "../db";

export const getHTMLPopupQuery = `
select html from popup_html
where popup_id = $1;
`

export const getHTMLPopup = async (req: any, res: any) => {
    let response: any = {}
    const { popupId } = req.query;
    try {
        if (!popupId) {
            response = {
                status: false,
                message: `Popup Id Missing`
            }
            return handleResponse(res, response, 400);
        }

        const result = await query(getHTMLPopupQuery, [popupId]);
        if (!result.rowCount) {
            response = {
                status: false,
                message: `Popup Id not available`
            }
            return handleResponse(res, response, 400);
        }
        const data = result.rows[0].html;
        response = {
            status: true,
            message: 'Success',
            data: {
                htmlContent: data
            }
        }
        return handleResponse(res, response, 200);
    } catch (error: any) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}
