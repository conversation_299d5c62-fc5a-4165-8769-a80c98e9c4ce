export interface PaymentCallback {
    success: boolean
    code: string
    message: string
    data: Data
}

export interface Data {
    merchantId: string
    merchantTransactionId: string
    transactionId: string
    amount: number
    state: string
    responseCode: string
    paymentInstrument: PaymentInstrument
}

export interface PaymentInstrument {
    type: string
    utr: string
}



export interface PayInitBody {
    merchantTransactionId: string,
    amount: number,
    merchantUserId: string,
}