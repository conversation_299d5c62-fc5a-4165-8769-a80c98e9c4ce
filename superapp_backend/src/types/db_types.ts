export interface USER {
  uid?: string;
  id?: number;
  user_name?: string;
  password_hash?: string | null;
  mobile_number?: string | null;
  email?: string;
  client_id: number;
  login_method: number;
  email_verified?: boolean;
  display_name?: string;
  display_pic?: string;
}

export interface KaraokeTracks {
  id: number;
  name: string;
  artist_id: number;
  description: string;
  thumbnail_url: string;
  video_url: string;
  track_url: string;
  original_url: string;
  is_active: boolean;
}

export interface UserAddress {
  id: number;
  first_name: string;
  last_name: string;
  country: string;
  address_line_one: string;
  address_line_two?: string;
  address_line_three?: string;
  city: string;
  state: string;
  pincode: string;
  phone: string;
  email: string;
}

export interface OrderBody {
  productItemId: number;
  quantity: number;
  price: number;
  original_price: number;
  price_per_qty: number;
  currency: number;
}
