export interface PayPalWHRoot {
  id: string
  event_version: string
  create_time: string
  resource_type: string
  resource_version: string
  event_type: string
  summary: string
  resource: Resource
  links: Link2[]
}

export interface Resource {
  id: string
  amount: Amount
  final_capture: boolean
  seller_protection: SellerProtection
  seller_receivable_breakdown: SellerReceivableBreakdown
  status: string
  supplementary_data: SupplementaryData
  payee: Payee
  create_time: string
  update_time: string
  links: Link[]
}

export interface Amount {
  currency_code: string
  value: string
}

export interface SellerProtection {
  status: string
  dispute_categories: string[]
}

export interface SellerReceivableBreakdown {
  gross_amount: GrossAmount
  paypal_fee: PaypalFee
  net_amount: NetAmount
}

export interface GrossAmount {
  currency_code: string
  value: string
}

export interface PaypalFee {
  currency_code: string
  value: string
}

export interface NetAmount {
  currency_code: string
  value: string
}

export interface SupplementaryData {
  related_ids: RelatedIds
}

export interface RelatedIds {
  order_id: string
}

export interface Payee {
  email_address: string
  merchant_id: string
}

export interface Link {
  href: string
  rel: string
  method: string
}

export interface Link2 {
  href: string
  rel: string
  method: string
}
