export interface ResisterRoot {
  session_id: string
  user_data: UserData
}

export interface UserData {
  name: string
  mobile_number: string
  country_code: string
}

export interface RegisterResp {
  people_id: string
  unique_field_value: string
  form_id: string
  session_id: string
}

export interface ScheduleMeetingBody {
  meta: Meum[]
  timezone: string
  date: string
  time: string
  invite_friends: any[]
  form_id: string
  session_id: string
  people_id: string
  unique_field_value: string
  source_referrer: string
}

export interface Meum {
  message: string
}

export interface MeetResp {
  message_content: string
  message_type: string
  message_from: string
  meta: Meum2[]
  raw: Raw
  acknowledged: boolean
  is_bot_handover: boolean
  message_to: string
  people_id: string
  meeting_id: string
  form_id: string
  unique_field_value: string
  source_id: string
  source_name: string
  source_type: string
  source_referrer: string
  session_id: string
  chat_group_id: string
  id: string
  is_moderated: boolean
  client_id: string
  created_on: number
  updated_on: number
}

export interface Meum2 {
  moderators?: string[]
  participants?: string[]
  date?: string
  time?: string
  timezone?: string
  duration?: number
  duration_mode?: string
  watermark?: string
  rrule?: string
  registration_enabled?: boolean
  products_enabled?: boolean
  registration_form?: string
  capture_registration_otp_params?: boolean
  registration_otp_params?: string
  unique_field_name?: string
  is_recurring?: boolean
  auto_record?: boolean
  end_recurring?: number
  room_name?: string
  meeting_name?: string
  description?: string
  meeting_password?: string
  meeting_type?: string
  can_co_browse?: boolean
  channels?: any[]
  widgets?: any[]
  products?: any[]
  products_listing_mode?: string
  is_moderated_chat?: boolean
  moderation_mode?: string
  moderated_keywords?: any[]
  pinned_messages?: any[]
  id?: string
  created_by_user_id?: string
  recording_url?: string
  events?: any[]
  tokens?: Tokens
  recurring_batch_id?: string
  start_time?: number
  end_time?: number
  run_time?: number
  recording_time?: number
  streaming_time?: number
  start_by: any
  end_by: any
  cumulative_participant_time?: number
  billing_cumulative_participant_time?: number
  scheduled_time?: number
  meeting_status?: string
  participant_count?: number
  active_participant_count?: number
  public_session_count?: number
  co_browsing_time?: number
  client_id?: string
  notify_meeting_participants?: NotifyMeetingParticipants
  co_browsings?: any[]
  recordings?: any[]
  streamings?: any[]
  participant_durations?: ParticipantDurations
  host_room?: string
  view_room?: string
  external_id?: string
  provider?: string
  created_on?: number
  updated_on?: number
  source_referrer?: string
  uid_map?: UidMap
  message?: string
}

export interface Tokens {}

export interface NotifyMeetingParticipants {
  first: boolean
  second: boolean
}

export interface ParticipantDurations {}

export interface UidMap {}

export interface Raw {}

