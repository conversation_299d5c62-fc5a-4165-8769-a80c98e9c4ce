export interface OrderDetailsPDF {
    id: number,
    date: string,
    paymentMethod: string,
    products: OD_Products[],
    symbol: string,
    total: number,
    discounted: number,
    coupon_code: string
}

export interface OD_Products {
    name: string,
    price: string,
    qty: number,
    total: number,
    symbol: string,
    image: string,
    discounted: number,
    size?: number
}

export interface CustomerDetails {
    firstName: string,
    lastName: string,
    email: string,
    phone: string,
    address: {
        address_line_one: string,
        address_line_two: string,
        address_line_three: string,
        city: string,
        state: string,
        country: string,
        pincode: number
    }
}

export interface OrderStausCSV {
    id: number,
    status: string
}
export interface OrderExcelItems {
    "Order #": string
    Date: string
    Time: string
    Customer: string
    Email: string
    Phone: string
    "Shipping Name": string
    "Shipping Phone": string
    "Shipping Address": string
    "Shipping City": string
    "Shipping State": string
    "Shipping Country": string
    "Shipping Pincode": string
    "Billing Name": string
    "Billing Phone": string
    "Billing Address": string
    "Billing City": string
    "Billing State": string
    "Billing Country": string
    "Billing Pincode": string
    "Total No of Products": string
    "Total Quantity of all the products": string
    "Total Amount with tax": string
    Status: string
    "Acceptance Status": string
    "Payment Mode": string
    "Shipping Type": string
    "Shipping Cost": number
    "Created by": string
}

export interface PaymentCallbackDefault {
    isCompleted: boolean,
    orderId: string,
    data: any
}

export interface PayGLocalBody {
    country: string
    amount: string
    gid: string
    merchantId: string
    cardType: string
    merchantTxnId: string
    paymentMethod: string
    currency: string
    cardBrand: string
    status: string
}

export interface PayGLocalTxnData {
    totalAmount: string
    txnCurrency: string
    billingData: BillingData
}

export interface BillingData {
    firstName: string
    lastName: string
    addressStreet1: string
    addressCity: string
    addressState: string
    addressPostalCode: string
    addressCountry: string
    emailId: string
}

export interface PhonePeCallbackBody {
    success: boolean;
    code: string;
    message: string;
    data: {
        merchantId: string;
        merchantTransactionId: string;
        transactionId: string;
        amount: number;
        state: string;
        responseCode: string;
        paymentInstrument: PhonePePaymentInstrument;
    };
    timestamp?: string;
}

type PhonePePaymentInstrument = 
    | {
        type: "UPI";
        utr: string;
    }
    | {
        type: "CARD";
        cardType: string;
        pgTransactionId: string;
        bankTransactionId: string;
        pgAuthorizationCode: string;
        arn: string;
        bankId: string;
    }
    | {
        type: "NETBANKING";
        pgTransactionId: string;
        pgServiceTransactionId: string;
        bankTransactionId: string | null;
        bankId: string;
    };