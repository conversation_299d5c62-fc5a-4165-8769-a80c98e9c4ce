import 'dotenv/config';
import express, { Application, Request, Response } from 'express';
import bodyParser from 'body-parser';
import cron from 'node-cron';
import router from './routes';
import admin from './routes/admin';
import dashboard from './routes/dashboard';
import uploadMerch from './routes/uploadMerch';
import logger from './logger/index';
import { initDB } from './db/index';
import cors from "cors";
import dotenv from 'dotenv';
import { sendReport } from './admin/mail_report';
import { processOrders } from './services/merch';
import { processPdtReport, processReport } from './services/admin';
import moment from "moment";
import { processMeetOrders } from './services/sales_assist';
import { processCoursePurchases } from './services/course';
import { processPaidVideoPurchases } from './services/paid_video';
import { validateCourseOrders, validateMeetOrders, validateMerchOrders, validatePaidVideoOrders } from './services/payglocal';
import { timeOutCalendlyPendings, timeOutCalendlyV2 } from './services/calendlyV1';
import { deleteOldRecords } from './chatbox';
dotenv.config();

const app: Application = express();

// parse application/x-www-form-urlencoded
app.use(bodyParser.urlencoded({ extended: false }));

// parse application/json
app.use(bodyParser.json());

const port = process.env.PORT || 3000;


app.use('/api', cors({
    origin: process.env.APP_ENV === 'PROD' ? ['https://artisteverse.com', 'https://admin.metastarmedia.io'] : ['https://localhost:3001', 'http://localhost:3001', 'https://localhost:3000', 'http://localhost:3000', 'http://localhost:5500', 'https://dev.artisteverse.com'],
    credentials: true,
    exposedHeaders: ['Set-Cookie']
}), router);


app.use('/admin', cors(), admin);
app.use('/dashboard', cors(), dashboard);
app.use('/uploadMerch', cors(), uploadMerch);


// This is the default end point, If endpoint doesn't match any of the defined one
router.get('/', (req: Request, res: Response) => {
    res.send('Health is fine');
});

async function init() {
    try {
        await initDB();
        app.listen(port, () => logger.info(`Server running on ${port}`));
    } catch (e) {
        logger.error('Error in starting the server', e);
    }
}
init();

cron.schedule('*/5 * * * *', async () => {
    if (process.env.APP_ENV === 'PROD') {
        await processOrders();
        await processMeetOrders()
        await processCoursePurchases()
        await processPaidVideoPurchases()

        await validateCourseOrders();
        await validatePaidVideoOrders();
        await validateMerchOrders();
        await validateMeetOrders();
    }
    await timeOutCalendlyPendings();
    await timeOutCalendlyV2();
    await deleteOldRecords('bickram_gosh');
    await deleteOldRecords('classic_cool');
    await deleteOldRecords('indian_ocean');
    await deleteOldRecords('manasi_scott');
    await deleteOldRecords('priyadarsini_govind');
})

if (process.env.APP_ENV === 'PROD') {

    // Weekly User Report
    cron.schedule('30 4 * * MON', async () => {
        await sendReport();
    });

    // Daily Sales Report
    cron.schedule('30 12 * * *', async () => {
        const todayTimestamp = moment().set({ hour: 18, minute: 0, second: 0, millisecond: 0 });
        const yesterdayTimestamp = moment().subtract(1, 'day').set({ hour: 18, minute: 0, second: 0, millisecond: 0 });
        await processReport(yesterdayTimestamp, todayTimestamp);
    })

    // Monthly Sales Report
    cron.schedule('0 0 1 * *', async () => {
        const currentDate = moment().subtract(2, 'day');
        const endDate = currentDate.clone().endOf('month').set({ hour: 18, minute: 0, second: 0, millisecond: 0 });
        const startDate = currentDate.clone().startOf('month').set({ hour: 18, minute: 0, second: 0, millisecond: 0 });
        await processReport(startDate, endDate);

        await processPdtReport(startDate, endDate);
    })
}