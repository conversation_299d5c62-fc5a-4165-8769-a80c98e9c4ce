import { createLogger, format, transports } from 'winston';

const { combine, timestamp, json } = format;
const logger = createLogger({
  format: combine(
    timestamp({
      format: 'YYYY-MM-DD HH:mm:ss',
    }),
    json(),
  ),
  transports: [
    new transports.File({
      filename: 'logs/error.log',
      level: 'error',
      maxsize: 5242880, // 5MB
    }),
    new transports.File({
      filename: 'logs/info.log',
      level: 'info',
      maxsize: 5242880, // 5MB
    }),
    new transports.File({
      filename: 'logs/debug.log',
      level: 'debug',
      maxsize: 5242880, // 5MB
    }),
    new transports.File({
      filename: 'logs/combined.log',
      maxsize: 5242880,
    }),
  ],
  exitOnError: false,
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new transports.Console({
    format: format.simple(),
  }));
}

export default logger;
