import {query} from '../db/index';

class AdminUser {
  static async findByEmail(email : string) {
    const queryStr = 'SELECT * FROM admin WHERE email = $1';
    const result = await query(queryStr, [email]);
    return result.rows[0];
  }

  static async findById(id : number) {
    const queryStr = 'SELECT * FROM admin WHERE id = $1';
    const result = await query(queryStr, [id]);
    return result.rows[0];
  }

}

export default AdminUser;