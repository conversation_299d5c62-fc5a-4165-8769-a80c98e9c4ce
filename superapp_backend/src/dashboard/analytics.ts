import { BetaAnalyticsDataClient } from "@google-analytics/data";
import serviceAccount from "../assets/analytics.json";
import { handleResponse } from "../common";
import { AdminType } from "./auth";
import { artistGAPropertyMap } from "./utils";

const createAnalyticsClient = () => {
  return new BetaAnalyticsDataClient({
    credentials: serviceAccount,
    projectId: serviceAccount.project_id,
  });
};

// Helpers to handle date strings like "30daysAgo"
const subtractDays = (days: number) => {
  const date = new Date();
  date.setDate(date.getDate() - days);
  return date;
};

const formatDate = (date: Date) => {
  const yyyy = date.getFullYear();
  const mm = String(date.getMonth() + 1).padStart(2, "0");
  const dd = String(date.getDate()).padStart(2, "0");
  return `${yyyy}-${mm}-${dd}`;
};

const generateDateRange = (start: string, end: string): string[] => {
  const startDate = new Date(start);
  const endDate = new Date(end);
  const dates: string[] = [];

  while (startDate <= endDate) {
    const yyyy = startDate.getFullYear();
    const mm = String(startDate.getMonth() + 1).padStart(2, "0");
    const dd = String(startDate.getDate()).padStart(2, "0");
    dates.push(`${yyyy}${mm}${dd}`); // format: YYYYMMDD
    startDate.setDate(startDate.getDate() + 1);
  }

  return dates;
};

const resolveDate = (input: string): string => {
  if (input === "today") return formatDate(new Date());
  const match = input.match(/^(\d+)daysAgo$/);
  if (match) {
    const days = parseInt(match[1]);
    return formatDate(subtractDays(days));
  }
  return input;
};

export const getTimeSpentData = async (req: any, res: any) => {
  let response: any = {};
  let { startDate, endDate, compareStartDate, compareEndDate, artist_id } =
    req.query;

  startDate = startDate || "30daysAgo";
  endDate = endDate || "today";
  compareStartDate = compareStartDate || "60daysAgo";
  compareEndDate = compareEndDate || "31daysAgo";

  let managed_artist_id: number | null = Number(req.user.managed_artist_id);

  if (Number(req.user.user_type) == AdminType.Admin) {
    managed_artist_id = artist_id ? Number(artist_id) : null;
  }

  if (!managed_artist_id) {
    response = {
      status: false,
      message: `Artist Id Needed`,
      error: "Artist Id Needed",
    };
    return handleResponse(res, response, 400);
  }

  managed_artist_id = Number(managed_artist_id);
  const propertyId =
    artistGAPropertyMap[managed_artist_id as keyof typeof artistGAPropertyMap];

  if (!propertyId) {
    response = {
      status: false,
      message: `Artist Id Invalid`,
      error: "Artist Id Invalid",
    };
    return handleResponse(res, response, 400);
  }

  try {
    const analyticsDataClient = createAnalyticsClient();

    const resolvedStartDate = resolveDate(startDate);
    const resolvedEndDate = resolveDate(endDate);

    const [currentPeriodResponse] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [
        {
          startDate,
          endDate,
        },
      ],
      dimensions: [
        {
          name: "date",
        },
      ],
      metrics: [
        {
          name: "averageSessionDuration",
        },
      ],
    });

    // Get previous period data for comparison
    const [previousPeriodResponse] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [
        {
          startDate: compareStartDate,
          endDate: compareEndDate,
        },
      ],
      metrics: [
        {
          name: "averageSessionDuration",
        },
      ],
    });

    let totalDuration = 0;
    const timeData: Array<{ date: string; value: number }> = [];
    const dataMap: Record<string, number> = {};

    if (currentPeriodResponse.rows && currentPeriodResponse.rows.length) {
      currentPeriodResponse.rows.forEach((row) => {
        if (row.dimensionValues && row.metricValues) {
          const date = row.dimensionValues[0]?.value || "";
          const avgTimeSpent = parseFloat(row.metricValues[0]?.value || "0");
          dataMap[date] = avgTimeSpent;
          totalDuration += avgTimeSpent;
        }
      });
    }

    const fullDates = generateDateRange(resolvedStartDate, resolvedEndDate);
    fullDates.forEach((date) => {
      const value = dataMap[date] ?? 0;
      timeData.push({
        date,
        value: value / 60,
      });
    });

    const avgTimeSpent = totalDuration / fullDates.length;
    const avgTimeSpentMinutes = Math.floor(avgTimeSpent / 60);

    // Calculate percentage change
    let percentChange = 0;
    if (previousPeriodResponse.rows && previousPeriodResponse.rows.length) {
      const prevAvgTimeSpent = parseFloat(
        previousPeriodResponse.rows[0]?.metricValues?.[0]?.value || "0"
      );
      percentChange =
        prevAvgTimeSpent > 0
          ? ((avgTimeSpent - prevAvgTimeSpent) / prevAvgTimeSpent) * 100
          : 0;
    }

    response = {
      status: true,
      message: "Success",
      result: {
        average: avgTimeSpentMinutes,
        percentChange: Math.round(percentChange * 100) / 100,
        trend: timeData,
        isPositive: percentChange >= 0,
      },
    };

    return handleResponse(res, response, 200);
  } catch (error) {
    console.error("Error fetching time spent data:", error);
    response = {
      status: false,
      message: "Request failed",
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};

export const getVisitsData = async (req: any, res: any) => {
  let response: any = {};
  let { startDate, endDate, compareStartDate, compareEndDate, artist_id } =
    req.query;

  startDate = startDate || "30daysAgo";
  endDate = endDate || "today";
  compareStartDate = compareStartDate || "60daysAgo";
  compareEndDate = compareEndDate || "31daysAgo";

  let managed_artist_id: number | null = Number(req.user.managed_artist_id);

  if (Number(req.user.user_type) == AdminType.Admin) {
    managed_artist_id = artist_id ? Number(artist_id) : null;
  }

  if (!managed_artist_id) {
    response = {
      status: false,
      message: `Artist Id Needed`,
      error: "Artist Id Needed",
    };
    return handleResponse(res, response, 400);
  }

  managed_artist_id = Number(managed_artist_id);
  const propertyId =
    artistGAPropertyMap[managed_artist_id as keyof typeof artistGAPropertyMap];

  if (!propertyId) {
    response = {
      status: false,
      message: `Artist Id Invalid`,
      error: "Artist Id Invalid",
    };
    return handleResponse(res, response, 400);
  }

  try {
    const analyticsDataClient = createAnalyticsClient();
    const resolvedStartDate = resolveDate(startDate);
    const resolvedEndDate = resolveDate(endDate);

    const [currentPeriodResponse] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [
        {
          startDate,
          endDate,
        },
      ],
      dimensions: [
        {
          name: "date",
        },
      ],
      metrics: [
        {
          name: "sessions",
        },
      ],
    });

    const [previousPeriodResponse] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [
        {
          startDate: compareStartDate,
          endDate: compareEndDate,
        },
      ],
      metrics: [
        {
          name: "sessions",
        },
      ],
    });

    let totalVisits = 0;
    const visitsData: { date: string; value: number }[] = [];
    const dataMap: Record<string, number> = {};

    if (currentPeriodResponse.rows?.length) {
      currentPeriodResponse.rows.forEach((row) => {
        if (row.dimensionValues?.[0] && row.metricValues?.[0]) {
          const date = row.dimensionValues[0]?.value || "";
          const visits = parseInt(row.metricValues[0]?.value || "0");

          totalVisits += visits;
          dataMap[date] = visits;
        }
      });
    }
    
    const fullDates = generateDateRange(resolvedStartDate, resolvedEndDate);
    fullDates.forEach((date) => {
      const value = dataMap[date] ?? 0;
      visitsData.push({
        date,
        value: value / 60, 
      });
    });

    // Calculate percentage change
    let percentChange = 0;
    let previousTotal = 0;

    if (previousPeriodResponse.rows && previousPeriodResponse.rows.length) {
      previousTotal = parseInt(
        previousPeriodResponse.rows[0]?.metricValues?.[0]?.value || "0"
      );
      percentChange = ((totalVisits - previousTotal) / previousTotal) * 100;
    }

    response = {
      status: true,
      message: "Success",
      result: {
        total: totalVisits,
        percentChange: Math.round(percentChange * 100) / 100,
        trend: visitsData,
        isPositive: percentChange >= 0,
      },
    };

    return handleResponse(res, response, 200);
  } catch (error) {
    console.error("Error fetching visits data:", error);
    response = {
      status: false,
      message: "Request failed",
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};

export const getCountriesData = async (req: any, res: any) => {
  let response: any = {};
  let { startDate, endDate, page, pageSize, artist_id } = req.query;

  startDate = startDate || "30daysAgo";
  endDate = endDate || "today";
  page = Number(page) ? Number(page) : 1;
  pageSize = Number(pageSize) ? Number(pageSize) : 10;

  const offset = (page - 1) * pageSize;

  let managed_artist_id: number | null = Number(req.user.managed_artist_id);

  if (Number(req.user.user_type) == AdminType.Admin) {
    managed_artist_id = artist_id ? Number(artist_id) : null;
  }

  if (!managed_artist_id) {
    response = {
      status: false,
      message: `Artist Id Needed`,
      error: "Artist Id Needed",
    };
    return handleResponse(res, response, 400);
  }

  managed_artist_id = Number(managed_artist_id);
  const propertyId =
    artistGAPropertyMap[managed_artist_id as keyof typeof artistGAPropertyMap];

  if (!propertyId) {
    response = {
      status: false,
      message: `Artist Id Invalid`,
      error: "Artist Id Invalid",
    };
    return handleResponse(res, response, 400);
  }

  try {
    const analyticsDataClient = createAnalyticsClient();

    const [countResponse] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [
        {
          startDate,
          endDate,
        },
      ],
      dimensions: [
        {
          name: "country",
        },
      ],
      metrics: [
        {
          name: "sessions",
        },
      ],
    });

    const totalCountries = countResponse.rowCount || 0;

    const [dataResponse] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [
        {
          startDate,
          endDate,
        },
      ],
      dimensions: [
        {
          name: "country",
        },
      ],
      metrics: [
        {
          name: "sessions",
        },
        {
          name: "totalRevenue",
        },
      ],
      orderBys: [
        {
          metric: { metricName: "sessions" },
          desc: true,
        },
      ],
      limit: pageSize,
      offset: offset,
    });

    const countries: Array<{
      country: string;
      visits: number;
      revenue: string;
    }> = [];

    if (dataResponse.rows && dataResponse.rows.length) {
      dataResponse.rows.forEach((row) => {
        if (row.dimensionValues && row.metricValues) {
          const country = row.dimensionValues[0]?.value || "Unknown";
          const visits = parseInt(row.metricValues[0]?.value || "0");
          const revenue = parseFloat(row.metricValues[1]?.value || "0").toFixed(
            2
          );

          countries.push({
            country,
            visits,
            revenue: `$${revenue}`,
          });
        }
      });
    }

    response = {
      status: true,
      message: "Success",
      result: {
        items: countries,
        total: totalCountries,
        page: page,
        pageSize: pageSize,
        totalPages: Math.ceil(totalCountries / pageSize),
      },
    };

    return handleResponse(res, response, 200);
  } catch (error) {
    console.error("Error fetching countries data:", error);
    response = {
      status: false,
      message: "Request failed",
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};

export const getGenderData = async (req: any, res: any) => {
  let response: any = {};
  let { startDate, endDate, artist_id } = req.query;

  startDate = startDate || "30daysAgo";
  endDate = endDate || "today";

  let managed_artist_id: number | null = Number(req.user.managed_artist_id);

  if (Number(req.user.user_type) == AdminType.Admin) {
    managed_artist_id = artist_id ? Number(artist_id) : null;
  }

  if (!managed_artist_id) {
    response = {
      status: false,
      message: `Artist Id Needed`,
      error: "Artist Id Needed",
    };
    return handleResponse(res, response, 400);
  }

  managed_artist_id = Number(managed_artist_id);
  const propertyId =
    artistGAPropertyMap[managed_artist_id as keyof typeof artistGAPropertyMap];

  if (!propertyId) {
    response = {
      status: false,
      message: `Artist Id Invalid`,
      error: "Artist Id Invalid",
    };
    return handleResponse(res, response, 400);
  }

  try {
    const analyticsDataClient = createAnalyticsClient();

    const [dataResponse] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [
        {
          startDate,
          endDate,
        },
      ],
      dimensions: [
        {
          name: "userGender",
        },
      ],
      metrics: [
        {
          name: "activeUsers",
        },
      ],
    });

    let maleUsers = 0;
    let femaleUsers = 0;
    let otherUsers = 0;
    let totalUsers = 0;

    if (dataResponse.rows && dataResponse.rows.length) {
      dataResponse.rows.forEach((row) => {
        if (row.dimensionValues && row.metricValues) {
          const gender = row.dimensionValues[0]?.value || "unknown";
          const users = parseInt(row.metricValues[0]?.value || "0");

          totalUsers += users;

          if (gender.toLowerCase() === "male") {
            maleUsers = users;
          } else if (gender.toLowerCase() === "female") {
            femaleUsers = users;
          } else {
            otherUsers += users;
          }
        }
      });
    }

    response = {
      status: true,
      message: "Success",
      result: {
        male: {
          value: maleUsers,
          percentage: totalUsers
            ? Math.round((maleUsers / totalUsers) * 100)
            : 0,
        },
        female: {
          value: femaleUsers,
          percentage: totalUsers
            ? Math.round((femaleUsers / totalUsers) * 100)
            : 0,
        },
        other: {
          value: otherUsers,
          percentage: totalUsers
            ? Math.round((otherUsers / totalUsers) * 100)
            : 0,
        },
        total: totalUsers,
      },
    };

    return handleResponse(res, response, 200);
  } catch (error) {
    console.error("Error fetching gender data:", error);
    response = {
      status: false,
      message: "Request failed",
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};

export const getDeviceCategoryData = async (req: any, res: any) => {
  let response: any = {};
  let { startDate, endDate, artist_id } = req.query;

  startDate = startDate || "30daysAgo";
  endDate = endDate || "today";

  let managed_artist_id: number | null = Number(req.user.managed_artist_id);

  if (Number(req.user.user_type) == AdminType.Admin) {
    managed_artist_id = artist_id ? Number(artist_id) : null;
  }

  if (!managed_artist_id) {
    response = {
      status: false,
      message: `Artist Id Needed`,
      error: "Artist Id Needed",
    };
    return handleResponse(res, response, 400);
  }

  managed_artist_id = Number(managed_artist_id);
  const propertyId =
    artistGAPropertyMap[managed_artist_id as keyof typeof artistGAPropertyMap];

  if (!propertyId) {
    response = {
      status: false,
      message: `Artist Id Invalid`,
      error: "Artist Id Invalid",
    };
    return handleResponse(res, response, 400);
  }

  try {
    const analyticsDataClient = createAnalyticsClient();

    const [dataResponse] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [
        {
          startDate,
          endDate,
        },
      ],
      dimensions: [
        {
          name: "deviceCategory",
        },
      ],
      metrics: [
        {
          name: "sessions",
        },
        {
          name: "activeUsers",
        },
        {
          name: "screenPageViews",
        },
      ],
    });

    const deviceData: {
      deviceCategory: string;
      sessions: number;
      users: number;
      pageViews: number;
      percentage: number;
    }[] = [];
    let totalSessions = 0;

    if (dataResponse.rows && dataResponse.rows.length) {
      dataResponse.rows.forEach((row) => {
        if (row.metricValues) {
          totalSessions += parseInt(row.metricValues[0]?.value || "0");
        }
      });

      dataResponse.rows.forEach((row) => {
        if (row.dimensionValues && row.metricValues) {
          const deviceCategory = row.dimensionValues[0]?.value || "unknown";
          const sessions = parseInt(row.metricValues[0]?.value || "0");
          const users = parseInt(row.metricValues[1]?.value || "0");
          const pageViews = parseInt(row.metricValues[2]?.value || "0");

          deviceData.push({
            deviceCategory,
            sessions,
            users,
            pageViews,
            percentage: Math.round((sessions / totalSessions) * 100),
          });
        }
      });
    }

    const [browserResponse] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [
        {
          startDate,
          endDate,
        },
      ],
      dimensions: [
        {
          name: "browser",
        },
      ],
      metrics: [
        {
          name: "sessions",
        },
      ],
      orderBys: [
        {
          metric: { metricName: "sessions" },
          desc: true,
        },
      ],
      limit: 5,
    });

    const browserData: {
      browser: string;
      sessions: number;
      percentage: number;
    }[] = [];

    if (browserResponse.rows && browserResponse.rows.length) {
      browserResponse.rows.forEach((row) => {
        if (row.dimensionValues && row.metricValues) {
          const browser = row.dimensionValues[0]?.value || "Unknown";
          const sessions = parseInt(row.metricValues[0]?.value || "0");

          browserData.push({
            browser,
            sessions,
            percentage: Math.round((sessions / totalSessions) * 100),
          });
        }
      });
    }

    response = {
      status: true,
      message: "Success",
      result: {
        devices: deviceData,
        browsers: browserData,
        total: totalSessions,
      },
    };

    return handleResponse(res, response, 200);
  } catch (error) {
    console.error("Error fetching device category data:", error);
    response = {
      status: false,
      message: "Request failed",
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};

export const getUserAcquisitionData = async (req: any, res: any) => {
  let { artist_id, timeline } = req.query;

  const today = new Date();
  const getDateString = (d: Date) => d.toISOString().split("T")[0]; // YYYY-MM-DD
  let startDate: string;
  let endDate: string = getDateString(today);
  let groupByDimension = "date";

  let managed_artist_id: number | null = Number(req.user.managed_artist_id);
  if (Number(req.user.user_type) == AdminType.Admin) {
    managed_artist_id = artist_id ? Number(artist_id) : null;
  }

  if (!managed_artist_id) {
    return handleResponse(
      res,
      {
        status: false,
        message: `Artist Id Needed`,
        error: "Artist Id Needed",
      },
      400
    );
  }

  const propertyId =
    artistGAPropertyMap[managed_artist_id as keyof typeof artistGAPropertyMap];

  if (!propertyId) {
    return handleResponse(
      res,
      {
        status: false,
        message: `Artist Id Invalid`,
        error: "Artist Id Invalid",
      },
      400
    );
  }

  const analyticsDataClient = createAnalyticsClient();

  try {
    // ========== Handle "yearly" case ==============
    if (timeline === "yearly") {
      // Run all 12 monthly requests in parallel
      const promises = [];

      for (let i = 11; i >= 0; i--) {
        const monthStart = new Date(today.getFullYear(), today.getMonth() - i, 1);
        const monthEnd = new Date(monthStart.getFullYear(), monthStart.getMonth() + 1, 0);

        const p = analyticsDataClient.runReport({
          property: `properties/${propertyId}`,
          dateRanges: [
            {
              startDate: getDateString(monthStart),
              endDate: getDateString(monthEnd),
            },
          ],
          dimensions: [{ name: "sessionDefaultChannelGrouping" }],
          metrics: [{ name: "sessions" }],
        });

        promises.push(p);
      }

      const results = await Promise.all(promises);

      const monthlyData: any[] = [];

      results.forEach(([acquisitionResponse], idx) => {
        const monthStart = new Date(today.getFullYear(), today.getMonth() - (11 - idx), 1);

        const monthlySummary: any = {
          name: monthStart.toLocaleString("default", { month: "short" }),
        };

        if (acquisitionResponse.rows?.length) {
          acquisitionResponse.rows.forEach((row) => {
            const channel = row.dimensionValues?.[0]?.value || "Unknown";
            const sessions = parseInt(row.metricValues?.[0]?.value || "0");
            monthlySummary[channel] = (monthlySummary[channel] || 0) + sessions;
          });
        }

        monthlyData.push(monthlySummary);
      });

      return handleResponse(
        res,
        {
          status: true,
          message: "Success",
          result: monthlyData,
        },
        200
      );
    }

    // ========== Handle other timelines ==========
    switch (timeline) {
      case "weekly": {
        const lastWeek = new Date(today);
        lastWeek.setDate(today.getDate() - 6);
        startDate = getDateString(lastWeek);
        break;
      }
      case "monthly": {
        const lastMonth = new Date(today);
        lastMonth.setDate(today.getDate() - 29);
        startDate = getDateString(lastMonth);
        break;
      }
      case "all_time": {
        const minAllowedDate = new Date("2023-08-14");
        const tenYearsAgo = new Date(today);
        tenYearsAgo.setFullYear(today.getFullYear() - 10);
        const start = tenYearsAgo > minAllowedDate ? tenYearsAgo : minAllowedDate;
        startDate = getDateString(start);
        groupByDimension = "year";
        break;
      }
      default: {
        const fallback = new Date(today);
        fallback.setDate(today.getDate() - 29);
        startDate = getDateString(fallback);
        break;
      }
    }

    const [acquisitionResponse] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [{ startDate, endDate }],
      dimensions: [
        { name: groupByDimension },
        { name: "sessionDefaultChannelGrouping" },
      ],
      metrics: [
        { name: "activeUsers" },
        { name: "sessions" },
        { name: "newUsers" },
      ],
      orderBys: [{ dimension: { dimensionName: groupByDimension } }],
    });

    const chartDataMap: Record<string, any> = {};

    // ======= X-Axis Label Formatter =======
    const formatLabel = (rawTime: string): string => {
      if (timeline === "all_time") return rawTime;

      if (rawTime.length === 8) {
        const year = Number(rawTime.slice(0, 4));
        const month = Number(rawTime.slice(4, 6)) - 1;
        const day = Number(rawTime.slice(6, 8));
        const date = new Date(year, month, day);

        if (timeline === "weekly") {
          return ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"][
            date.getDay()
          ];
        } else if (timeline === "monthly") {
          return String(date.getDate()).padStart(2, "0");
        } else if (timeline === "yearly") {
          return [
            "Jan",
            "Feb",
            "Mar",
            "Apr",
            "May",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Oct",
            "Nov",
            "Dec",
          ][month];
        }
      }

      return rawTime;
    };

    // ======= Pre-fill chartDataMap with labels =======
    if (timeline === "weekly") {
      for (let i = 6; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);
        const label = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"][date.getDay()];
        chartDataMap[label] = { name: label };
      }
    }

    if (timeline === "monthly") {
      for (let i = 29; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);
        const label = String(date.getDate()).padStart(2, "0");
        chartDataMap[label] = { name: label };
      }
    }

    if (timeline === "all_time") {
      const startYear = 2023;
      const currentYear = today.getFullYear();
      for (let y = startYear; y <= currentYear; y++) {
        chartDataMap[y.toString()] = { name: y.toString() };
      }
    }

    // ======= Parse Analytics Data into Map =======
    if (acquisitionResponse.rows?.length) {
      acquisitionResponse.rows.forEach((row) => {
        const rawTime = row.dimensionValues?.[0]?.value || "";
        const timeKey = formatLabel(rawTime);
        const channel = row.dimensionValues?.[1]?.value || "Unknown";
        const sessions = parseInt(row.metricValues?.[1]?.value || "0");

        if (!chartDataMap[timeKey]) {
          chartDataMap[timeKey] = { name: timeKey };
        }

        chartDataMap[timeKey][channel] =
          (chartDataMap[timeKey][channel] || 0) + sessions;
      });
    }

    // ======= Final Chart Data Output =======
    const chartData = Object.values(chartDataMap).sort((a, b) => {
      const aDate = parseInt(a.name);
      const bDate = parseInt(b.name);
      return aDate - bDate;
    });

    return handleResponse(
      res,
      {
        status: true,
        message: "Success",
        result: chartData,
      },
      200
    );
  } catch (error) {
    console.error("Error fetching user acquisition data:", error);
    return handleResponse(
      res,
      {
        status: false,
        message: "Request failed",
        error,
      },
      500
    );
  }
};
