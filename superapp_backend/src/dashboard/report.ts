import { query } from "../db";
import excelJS from "exceljs";
import { formatRegex, invalidArray } from "../common/constants";
import { handleResponse } from "../common";

const userReportQuery = `select u.id,coalesce(email,mobile_number) user_name,u.created date_created,email,mobile_number,c.name platform,lm.name login_method from users u
inner join login_methods lm on lm.id = u.login_method
inner join clients c on c.id = u.client_id
where (u.created between ($1 at time zone 'asia/kolkata') and (($2 at time zone 'asia/kolkata') + interval '1 day' - interval '1 second') OR $1 IS NUll)
AND (lm.id = $3 or $3 is null) AND (c.id = $4 or $4 is null)
order by u.created desc`;

export const karokeReportQuery = `
    select 
	kr.id songid, 
 	coalesce(us.email,us.mobile_number) user_name,
	ks.name song_name,
	ar.name artist_name,
	kr.url song_url,
    TO_CHAR(kr.created at time zone 'asia/kolkata','Mon DD YYYY') song_created
from karaoke_recordings kr
inner join karaoke_songs ks on ks.id = kr.song_id
inner join users us on us.id = kr.user_id
inner join artist ar on ar.id = ks.artist_id
WHERE (kr.created between ($1 at time zone 'asia/kolkata') and (($2 at time zone 'asia/kolkata') + interval '1 day' - interval '1 second') OR $2 IS NUll)
AND (ar.id = $3 or $3 is null)
ORDER BY kr.created desc
    `;

const createKarokeReportExcel = async (report: any[]) => {
    try {
        const workbook = new excelJS.Workbook();  // Create a new workbook
        const worksheet = workbook.addWorksheet("My Users"); // New Worksheet
        const path = "./src/assets";  // Path to download excel
        // Column for data in excel. key must match data key
        worksheet.columns = [
            { header: "Song Id", key: "songid", width: 10 },
            { header: "Username", key: "user_name", width: 10 },
            { header: "Song Name", key: "song_name", width: 10 },
            { header: "Artist Name", key: "artist_name", width: 10 },
            { header: "Song Url", key: "song_url", width: 10 },
            { header: "Song Created", key: "song_created", width: 10 }
        ];
        // Looping through User data
        let counter = 1;
        report.forEach((user) => {
            user.rn = counter;
            worksheet.addRow(user); // Add data in worksheet
            counter++;
        });
        // Making first line in excel bold
        worksheet.getRow(1).eachCell((cell) => {
            cell.font = { bold: true };
        });

        const fPath = `${path}/karoke.xlsx`

        await workbook.xlsx.writeFile(fPath)

        return fPath;
    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Error on creating Report Excel`
        })
    }
}

const createUserReportExcel = async (report: any[]) => {
    try {
        const workbook = new excelJS.Workbook();
        const worksheet = workbook.addWorksheet("My Users");
        const path = "./src/assets";
        worksheet.columns = [
            { header: "User Id", key: "id", width: 10 },
            { header: "Username", key: "user_name", width: 10 },
            { header: "Date Created", key: "date_created", width: 10 },
            { header: "Email", key: "email", width: 10 },
            { header: "Mobile", key: "mobile_number", width: 10 },
            { header: "Platform", key: "platform", width: 10 },
            { header: "Login Method", key: "login_method", width: 10 },
        ];
        let counter = 1;
        report.forEach((user) => {
            user.rn = counter;
            worksheet.addRow(user);
            counter++;
        });
        worksheet.getRow(1).eachCell((cell) => {
            cell.font = { bold: true };
        });

        const fPath = `${path}/users.xlsx`

        await workbook.xlsx.writeFile(fPath)

        return fPath;
    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Error on creating Report Excel`
        })
    }
}

export const getUserReport = async (req: any, res: any) => {
    let response = {};
    let { fromDate, toDate, clientId, loginType } = req.query;


    if ((fromDate && toDate) && (!formatRegex.test(fromDate) || !formatRegex.test(toDate))) {
        response = {
            status: false,
            message: `Invalid Date Format`
        }
        return handleResponse(res, response, 400);
    }
    else if ((fromDate && !toDate) || (toDate && !fromDate)) {
        response = {
            status: false,
            message: `Invalid Date Provided`
        }
        return handleResponse(res, response, 400);
    }
    try {
        const report = await query(userReportQuery, [!invalidArray.includes(fromDate) ? fromDate : null, !invalidArray.includes(toDate) ? toDate : null, loginType ? loginType : null, clientId ? clientId : null]);

        if (!report.rowCount) {
            res.send('Not Data Found');
        }

        const path = await createUserReportExcel(report.rows);

        res.download(path);
    } catch (error) {
        res.send({
            status: "error",
            message: "Something went wrong",
            error
        });
    }
}

export const getKaraokeReport = async (req: any, res: any) => {
    let response: any = {}
    let { fromDate, toDate, artistId } = req.query;

    if ((fromDate && toDate) && (!formatRegex.test(fromDate) || !formatRegex.test(toDate))) {
        response = {
            status: false,
            message: `Invalid Date Format`
        }
        return handleResponse(res, response, 400);
    }
    else if ((fromDate && !toDate) || (toDate && !fromDate)) {
        response = {
            status: false,
            message: `Invalid Date Provided`
        }
        return handleResponse(res, response, 400);
    }

    try {
        const report = await query(karokeReportQuery, [!invalidArray.includes(fromDate) ? fromDate : null, !invalidArray.includes(toDate) ? toDate : null, !invalidArray.includes(artistId) ? artistId : null]);

        if (!report.rowCount) {
            res.send('Not Data Found');
        }

        const path = await createKarokeReportExcel(report.rows);

        res.download(path);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}