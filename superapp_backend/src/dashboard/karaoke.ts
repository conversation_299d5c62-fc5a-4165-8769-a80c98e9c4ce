import { handleResponse } from "../common";
import { formatRegex, invalidArray } from "../common/constants";
import { query } from "../db";
import { AdminType } from "./auth";
import { allArtistId } from "./utils";

export const countKaraokeQuery = `
SELECT 
 COUNT(*)
FROM karaoke_recordings kr
INNER JOIN karaoke_songs ks ON ks.id = kr.song_id
INNER JOIN artist ar ON ar.id = ks.artist_id
WHERE ar.id = ANY($1)`;

export const karokeQuery = (key: string, order: string) => {
    return `
    SELECT 
        kr.id songid, 
        COALESCE(us.email, us.mobile_number) user_name,
        us.id user_id,
        ks.name song_name,
        ar.name artist_name,
        kr.url song_url,
        TO_CHAR(kr.created AT TIME ZONE 'asia/kolkata', 'Mon DD YYYY') song_created
    FROM karaoke_recordings kr
    INNER JOIN karaoke_songs ks ON ks.id = kr.song_id
    INNER JOIN users us ON us.id = kr.user_id
    INNER JOIN artist ar ON ar.id = ks.artist_id
    WHERE (kr.created BETWEEN ($3 AT TIME ZONE 'asia/kolkata') AND (($4 AT TIME ZONE 'asia/kolkata') + INTERVAL '1 day' - INTERVAL '1 second') OR $3 IS NULL)
    AND ar.id = ANY($5)
    ORDER BY ${key} ${order}
    LIMIT $1
    OFFSET $2
    `
};

export const getKaraoke = async (req: any, res: any) => {
    let response: any = {}
    let { page, pageSize, key, order, fromDate, toDate, artist_id } = req.query;
    page = Number(page) ? Number(page) : 1;
    pageSize = Number(pageSize) ? Number(pageSize) : 10;
    key = key ? key : 'user_id';
    order = order ? order : 'desc';

    let managed_artist_id = [req.user.managed_artist_id];
    
    if (Number(req.user.user_type) == AdminType.Admin) {
        managed_artist_id = artist_id ? [artist_id] : allArtistId
    }
        
    const offset = (page - 1) * pageSize;

    if ((fromDate && toDate) && (!formatRegex.test(fromDate) || !formatRegex.test(toDate))) {
        response = {
            status: false,
            message: `Invalid Date Format`
        }
        return handleResponse(res, response, 400);
    }
    else if ((fromDate && !toDate) || (toDate && !fromDate)) {
        response = {
            status: false,
            message: `Invalid Date Provided`
        }
        return handleResponse(res, response, 400);
    }

    try {
        const totalResp = await query(countKaraokeQuery, [managed_artist_id]);
        const resp = await query(karokeQuery(key, order), [
            pageSize, 
            offset, 
            !invalidArray.includes(fromDate) ? fromDate : null, 
            !invalidArray.includes(toDate) ? toDate : null, 
            managed_artist_id
        ]);
        
        response = {
            status: true,
            message: "Success",
            result: {
                items: resp.rows,
                total: parseInt(totalResp.rows[0].count)
            }
        }
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}

export const getTopSongs = async (req: any, res: any) => {
    let response: any = {};
    let { fromDate, toDate, artist_id, limit = 5 } = req.query;
    
    // Convert limit to number and ensure it has a reasonable value
    limit = Number(limit) ? Number(limit) : 5;
    
    // Get managed artist IDs based on user type
    let managed_artist_id = [req.user.managed_artist_id];
    if (Number(req.user.user_type) == AdminType.Admin) {
        managed_artist_id = artist_id ? [artist_id] : allArtistId;
    }
    
    // Validate date format if provided
    if ((fromDate && toDate) && (!formatRegex.test(fromDate) || !formatRegex.test(toDate))) {
        response = {
            status: false,
            message: `Invalid Date Format`
        };
        return handleResponse(res, response, 400);
    }
    else if ((fromDate && !toDate) || (toDate && !fromDate)) {
        response = {
            status: false,
            message: `Invalid Date Provided`
        };
        return handleResponse(res, response, 400);
    }
    
    try {
        // SQL query to get top songs based on play count
        const topSongsQuery = `
            SELECT 
                ks.id AS song_id,
                ks.name AS song_name,
                ar.name AS artist_name,
                COUNT(kr.id) AS play_count
            FROM 
                karaoke_songs ks
            LEFT JOIN 
                karaoke_recordings kr ON ks.id = kr.song_id
            INNER JOIN 
                artist ar ON ar.id = ks.artist_id
            WHERE 
                ar.id = ANY($1)
                AND (
                    (kr.created BETWEEN ($2 AT TIME ZONE 'asia/kolkata') AND (($3 AT TIME ZONE 'asia/kolkata') + INTERVAL '1 day' - INTERVAL '1 second'))
                    OR $2 IS NULL
                )
            GROUP BY 
                ks.id, ks.name, ar.name
            ORDER BY 
                play_count DESC
            LIMIT $4
        `;
        
        // Execute query
        const topSongs = await query(topSongsQuery, [
            managed_artist_id,
            !invalidArray.includes(fromDate) ? fromDate : null,
            !invalidArray.includes(toDate) ? toDate : null,
            limit
        ]);
        
        // Format the response
        response = {
            status: true,
            message: "Success",
            result: {
                top_songs: topSongs.rows.map(song => ({
                    song_id: song.song_id,
                    song_name: song.song_name,
                    artist_name: song.artist_name,
                    play_count: parseInt(song.play_count)
                }))
            }
        };
        
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        };
        
        return handleResponse(res, response, 500);
    }
};