import jwt from 'jsonwebtoken';
import { query } from '../db/index';
import AdminUser from './admin';

const JWT_SECRET = process.env.SECRET || 'your_jwt_secret_key';

export enum AdminType {
    Admin,
    Artiste
}

export const generateToken = (userId: number) => {
    return jwt.sign({ id: userId }, JWT_SECRET, { expiresIn: '24h' });
};

export const saveSession = async (userId: number, token: string) => {
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24);

    const queryStr = `
    INSERT INTO sessions (admin_id, token, expires_at) 
    VALUES ($1, $2, $3) 
    RETURNING *
  `;
    await query(queryStr, [userId, token, expiresAt]);
};

export const auth = async (req: any, res: any, next: any) => {
    try {
        const token = req.header('Authorization')?.replace('Bearer ', '');

        if (!token) {
            return res.status(401).json({ message: 'Authentication required' });
        }

        const decoded: any = jwt.verify(token, JWT_SECRET);

        const sessionQuery = `
            SELECT * FROM sessions 
            WHERE token = $1 AND expires_at > CURRENT_TIMESTAMP
        `;
        const sessionResult = await query(sessionQuery, [token]);

        if (sessionResult.rows.length === 0) {
            throw new Error('Session expired or invalid');
        }

        const user = await AdminUser.findById(decoded.id);

        if (!user) {
            throw new Error('AdminUser not found');
        }

        req.user = user;
        req.token = token;

        next();
    } catch (error: any) {
        res.status(401).json({ message: 'Please authenticate', error: error.message });
    }
};

export const adminOnly = (req: any, res: any, next: any) => {
    if (Number(req.user.user_type) !== AdminType.Admin && Number(req.user.user_type) !== AdminType.Artiste) {
        return res.status(403).json({ message: 'Access denied. Admin only.' });
    }
    next();
};



export const checkArtistAccess = (req: any, res: any, next: any) => {
    if (Number(req.user.user_type) === AdminType.Admin) {
        if (req.user.managed_artist_id !== null &&
            parseInt(req.params.id) !== req.user.managed_artist_id) {
            return res.status(403).json({
                message: 'Access denied. You can only access your assigned artist\'s data.'
            });
        }
        return next();
    }

    if (Number(req.user.user_type) === AdminType.Artiste &&
        req.params.id &&
        parseInt(req.params.id) !== req.user.id) {
        return res.status(403).json({
            message: 'Access denied. You can only access your own data.'
        });
    }

    next();
};
