import { comparePasswprd, handleResponse } from "../common";
import { cleanUserName, REGEX } from "../common/constants";
import AdminUser from './admin';
import { AdminType, generateToken, saveSession } from "./auth";
import { query } from "../db";
import { allArtistId, artistNameMap } from "./utils";

export const dashboardLogin = async (req: any, res: any) => {
    try {
        let { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({ message: 'Email and password are required' });
        }

        email = cleanUserName(email);
        email = REGEX.EMAIL.test(email) ? email : null;

        if (!email) {
            return res.status(400).json({ message: 'Invalid Email' });
        }

        const user = await AdminUser.findByEmail(email);

        if (!user) {
            return res.status(401).json({ message: 'Invalid credentials' });
        }
        const isMatch = await comparePasswprd(user.password, password);
        if (!isMatch) {
            return res.status(401).json({ message: 'Invalid credentials' });
        }

        const token = generateToken(user.id);
        await saveSession(user.id, token);

        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + 24);

        res.json({
            message: 'Login successful',
            token,
            expiresAt,
            user: {
                id: user.id,
                name: user.name,
                email: user.email,
                userType: user.user_type,
                artistMap : artistNameMap
            }
        });

    } catch (error) {
        console.error(error);
    }
}

export const logout = async (req: any, res: any) => {
    try {
        const queryStr = 'DELETE FROM sessions WHERE token = $1';
        await query(queryStr, [req.token]);

        res.json({ message: 'Logged out successfully' });
    } catch (error: any) {
        console.error('Logout error:', error);
        res.status(500).json({ message: 'Server error', error: error.message });
    }
};

export const countUserQuery = `
SELECT 
    COUNT(*)
FROM 
    users us
WHERE 
    (
        $1 = 'all_time' OR
        ($1 = 'weekly' AND us.created >= CURRENT_DATE - INTERVAL '7 days') OR
        ($1 = 'monthly' AND us.created >= CURRENT_DATE - INTERVAL '30 days') OR
        ($1 = 'yearly' AND us.created >= CURRENT_DATE - INTERVAL '1 year')
    )`;

export const userQuery = (key: string, order: string) => {
    return `SELECT 
    us.id AS user_id,
    COALESCE(email, mobile_number) AS user_name, 
    TO_CHAR(us.created AT TIME ZONE 'Asia/Kolkata', 'Mon DD YYYY') AS user_created, 
    display_name,
    cl.name AS client_name,
    lm.name AS login_method_name
FROM 
    users us
INNER JOIN 
    clients cl ON cl.id = us.client_id
INNER JOIN 
    login_methods lm ON lm.id = us.login_method
WHERE 
    (
        $3 = 'all_time' OR
        ($3 = 'weekly' AND us.created >= CURRENT_DATE - INTERVAL '7 days') OR
        ($3 = 'monthly' AND us.created >= CURRENT_DATE - INTERVAL '30 days') OR
        ($3 = 'yearly' AND us.created >= CURRENT_DATE - INTERVAL '1 year')
    )
ORDER BY 
    ${key} ${order}
LIMIT $1 OFFSET $2;`  
};

export const getUser = async (req: any, res: any) => {
    let response: any = {}
    let { page, pageSize, key, order, artist_id,timeline } = req.query;
    page = Number(page) ? Number(page) : 1;
    pageSize = Number(pageSize) ? Number(pageSize) : 10;
    key = key ? key : 'user_id';
    order = order ? order : 'desc';

    const offset = (page - 1) * pageSize;
    let managed_artist_id = [req.user.managed_artist_id];

    if (Number(req.user.user_type) == AdminType.Admin) {
        managed_artist_id = artist_id ? [artist_id] : allArtistId
    }
    console.log(managed_artist_id);
    
    try {
   const totalResp = await query(countUserQuery, [timeline]);
        const resp = await query(userQuery(key, order), [pageSize, offset, timeline]);
        response = {
            status: true,
            message: "Success",
            result: {
                items: resp.rows,
                total: parseInt(totalResp.rows[0].count)
            }
        }
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}




