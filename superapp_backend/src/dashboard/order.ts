import { handleResponse } from "../common";
import { query } from "../db";
import { AdminType } from "./auth";
import { allArtistId } from "./utils";

export const getSaleData = async (req: any, res: any) => {
  let response: any = {};
  let { page, pageSize, key, order, artist_id, timeline, currency, product_name } = req.query;

  page = Number(page) ? Number(page) : 1;
  pageSize = Number(pageSize) ? Number(pageSize) : 10;
  key = key ? key : "created";
  order = order ? order : "desc";
  product_name = product_name ? product_name.trim() : "";

  let managed_artist_id = [req.user.managed_artist_id];

  if (Number(req.user.user_type) == AdminType.Admin) {
    managed_artist_id = artist_id ? [artist_id] : allArtistId;
  }

  const offset = (page - 1) * pageSize;

  try {
    const countSaleItemsQuery = `
      WITH enriched_sales AS (
        SELECT 
          si.*,
          sd.payment_currency,
          CASE 
            WHEN si.produtct_type = 1 THEN (
              SELECT p.artist_id FROM products p 
              JOIN product_item pi 
                ON (
                  (p.product_type != 2 AND p.id = pi.product_id)
                  OR
                  (p.product_type = 2 AND p.id = pi.course_product_id)
                )
              WHERE pi.id = si.product_item_id
            )
            WHEN si.produtct_type = 2 THEN (
              SELECT cd.artist_id FROM course_details cd 
              WHERE cd.id = si.course_id
            )
            WHEN si.produtct_type = 3 THEN (
              SELECT pv.artist_id FROM paid_videos pv 
              WHERE pv.id = si.video_id
            )
            WHEN si.produtct_type = 4 THEN (
              SELECT am.artist_id FROM artist_meets am 
              WHERE am.id = si.meet_id
            )
          END as artist_id,
          CASE 
            WHEN si.produtct_type = 1 THEN (
              SELECT pi.title FROM product_item pi WHERE pi.id = si.product_item_id
            )
            WHEN si.produtct_type = 2 THEN (
              SELECT cd.title FROM course_details cd WHERE cd.id = si.course_id
            )
            WHEN si.produtct_type = 3 THEN (
              SELECT pv.title FROM paid_videos pv WHERE pv.id = si.video_id
            )
            WHEN si.produtct_type = 4 THEN (
              SELECT amd.name FROM artist_meet_detail amd
              JOIN artist_meets am ON am.id = amd.artist_meet_id
              WHERE am.id = si.meet_id LIMIT 1
            )
          END as product_name
        FROM sale_item si
        JOIN sale_details sd ON si.sale_id = sd.id
      )
      SELECT COUNT(*) FROM enriched_sales
      WHERE artist_id = ANY($1)
        AND payment_currency = $2
        AND (
          $3 = 'all_time' OR
          ($3 = 'weekly' AND created >= CURRENT_DATE - INTERVAL '6 days') OR
          ($3 = 'monthly' AND created >= CURRENT_DATE - INTERVAL '30 days') OR
          ($3 = 'yearly' AND created >= CURRENT_DATE - INTERVAL '1 year')
        )
        ${product_name ? `AND product_name ILIKE $4` : ''}
    `;

    const saleItemsQuery = (sortKey: string, sortOrder: string, filterByName: boolean) => `
      WITH enriched_sales AS (
        SELECT 
          si.*, 
          sd.user_id,
          sd.payment_status,
          sd.payment_method,
          sd.payment_currency,
          sd.invoice_url,
          sd.order_status,
          CASE 
            WHEN si.produtct_type = 1 THEN (
              SELECT p.artist_id FROM products p 
              JOIN product_item pi 
                ON (
                  (p.product_type != 2 AND p.id = pi.product_id)
                  OR
                  (p.product_type = 2 AND p.id = pi.course_product_id)
                )
              WHERE pi.id = si.product_item_id
            )
            WHEN si.produtct_type = 2 THEN (
              SELECT cd.artist_id FROM course_details cd 
              WHERE cd.id = si.course_id
            )
            WHEN si.produtct_type = 3 THEN (
              SELECT pv.artist_id FROM paid_videos pv 
              WHERE pv.id = si.video_id
            )
            WHEN si.produtct_type = 4 THEN (
              SELECT am.artist_id FROM artist_meets am 
              WHERE am.id = si.meet_id
            )
          END as artist_id,
          CASE 
            WHEN si.produtct_type = 1 THEN (
              SELECT pi.title FROM product_item pi WHERE pi.id = si.product_item_id
            )
            WHEN si.produtct_type = 2 THEN (
              SELECT cd.title FROM course_details cd WHERE cd.id = si.course_id
            )
            WHEN si.produtct_type = 3 THEN (
              SELECT pv.title FROM paid_videos pv WHERE pv.id = si.video_id
            )
            WHEN si.produtct_type = 4 THEN (
              SELECT amd.name FROM artist_meet_detail amd
              JOIN artist_meets am ON am.id = amd.artist_meet_id
              WHERE am.id = si.meet_id LIMIT 1
            )
          END as product_name,
          CASE 
            WHEN si.produtct_type = 1 THEN (
              SELECT pi.image_url FROM product_item pi WHERE pi.id = si.product_item_id
            )
            WHEN si.produtct_type = 2 THEN (
              SELECT cd.image FROM course_details cd WHERE cd.id = si.course_id
            )
            WHEN si.produtct_type = 3 THEN (
              SELECT pv.image FROM paid_videos pv WHERE pv.id = si.video_id
            )
            WHEN si.produtct_type = 4 THEN (
              SELECT amd.image FROM artist_meet_detail amd
              JOIN artist_meets am ON am.id = amd.artist_meet_id
              WHERE am.id = si.meet_id LIMIT 1
            )
          END as product_image,
          (SELECT pt.name FROM product_type pt WHERE pt.id = si.produtct_type) as product_type_name
        FROM sale_item si
        JOIN sale_details sd ON si.sale_id = sd.id
      )
      SELECT * FROM enriched_sales
      WHERE artist_id = ANY($3)
        AND payment_currency = $5
        AND (
          $4 = 'all_time' OR
          ($4 = 'weekly' AND created >= CURRENT_DATE - INTERVAL '6 days') OR
          ($4 = 'monthly' AND created >= CURRENT_DATE - INTERVAL '30 days') OR
          ($4 = 'yearly' AND created >= CURRENT_DATE - INTERVAL '1 year')
        )
        ${filterByName ? `AND product_name ILIKE $6` : ''}
      ORDER BY ${sortKey} ${sortOrder}
      LIMIT $1 OFFSET $2
    `;

    const filterByName = !!product_name;

    const countParams = filterByName
      ? [managed_artist_id, currency, timeline, `%${product_name}%`]
      : [managed_artist_id, currency, timeline];

    const totalResp = await query(countSaleItemsQuery, countParams);

    const dataParams = filterByName
      ? [pageSize, offset, managed_artist_id, timeline, currency, `%${product_name}%`]
      : [pageSize, offset, managed_artist_id, timeline, currency];

    const resp = await query(saleItemsQuery(key, order, filterByName), dataParams);

    response = {
      status: true,
      message: "Success",
      result: {
        items: resp.rows,
        total: parseInt(totalResp.rows[0].count),
      },
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};


export const getTotalRevenue = async (req: any, res: any) => {
  let response: any = {};
  let { timeline, artist_id, currency } = req.query;

  timeline = timeline ? timeline.toLowerCase() : "weekly";
  let managed_artist_id = [req.user.managed_artist_id];
  if (Number(req.user.user_type) == AdminType.Admin) {
    managed_artist_id = artist_id ? [artist_id] : allArtistId;
  }

  try {
    let timeFilter;
    let groupByFormat;
    let xAxisLabels;
    const today = new Date();

    switch (timeline) {
      case "weekly":
        timeFilter = "sd.created >= CURRENT_DATE - INTERVAL '6 days'";
        groupByFormat = "TO_CHAR(sd.created, 'Day')";
        const weeks = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
        const todayIndexWeek = (today.getDay() + 6) % 7;
        
        const rotatedLabelsWeek = [
          ...weeks.slice(todayIndexWeek + 1),
          ...weeks.slice(0, todayIndexWeek + 1),
        ];

        xAxisLabels = rotatedLabelsWeek
        break;
      case "monthly":
        timeFilter = "sd.created >= CURRENT_DATE - INTERVAL '30 days'";
        groupByFormat = "TO_CHAR(sd.created, 'DD')";
        const todayDate = today.getDate();
        const xAxisLabelsRaw = Array.from({ length: 30 }, (_, i) => {
          const date = new Date();
          date.setDate(todayDate - 29 + i);
          return date.getDate().toString().padStart(2, '0');
        });
        const todayLabel = todayDate.toString().padStart(2, '0');
        const todayIndex = xAxisLabelsRaw.indexOf(todayLabel);
        const rotatedLabels = [
          ...xAxisLabelsRaw.slice(todayIndex + 1),
          ...xAxisLabelsRaw.slice(0, todayIndex + 1),
        ];
        xAxisLabels = rotatedLabels;
        break;
      case "yearly":
        timeFilter = "sd.created >= CURRENT_DATE - INTERVAL '1 year'";
        groupByFormat = "TO_CHAR(sd.created, 'Mon')";
        const monthLabels = [
          "Jan",
          "Feb",
          "Mar",
          "Apr",
          "May",
          "Jun",
          "Jul",
          "Aug",
          "Sep",
          "Oct",
          "Nov",
          "Dec",
        ];
        const currentMonthIndex = new Date().getMonth();
        const rotatedLabels1 = [
          ...monthLabels.slice(currentMonthIndex + 1),
          ...monthLabels.slice(0, currentMonthIndex + 1)
        ];

        xAxisLabels = rotatedLabels1;
        break;
      case "all_time":
        timeFilter = "";
        groupByFormat = "TO_CHAR(sd.created, 'YYYY')";
        const startYear = 2023;
        const currentYear = new Date().getFullYear();
        xAxisLabels = Array.from({ length: currentYear - startYear + 1 }, (_, i) => (startYear + i).toString());
        break;
      default:
        timeFilter = "sd.created >= CURRENT_DATE - INTERVAL '6 days'";
        groupByFormat = "TO_CHAR(sd.created, 'Day')";
        xAxisLabels = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
    }

    const revenueQuery = `
      SELECT 
        ${groupByFormat} AS time_period,
        SUM(si.purchased_price) AS total_revenue,
        COUNT(DISTINCT sd.id) AS order_count
      FROM 
        sale_item si
      JOIN 
        sale_details sd ON si.sale_id = sd.id
      WHERE 
        ${timeFilter}
        ${timeline === "all_time" ? "" : "AND"} (
          CASE 
            WHEN si.produtct_type = 1 THEN (
              SELECT p.artist_id FROM products p 
              JOIN product_item pi 
                ON (
                  (p.product_type != 2 AND p.id = pi.product_id)
                  OR
                  (p.product_type = 2 AND p.id = pi.course_product_id)
                )
              WHERE pi.id = si.product_item_id
            )
            WHEN si.produtct_type = 2 THEN (
              SELECT cd.artist_id FROM course_details cd 
              WHERE cd.id = si.course_id
            )
            WHEN si.produtct_type = 3 THEN (
              SELECT pv.artist_id FROM paid_videos pv 
              WHERE pv.id = si.video_id
            )
            WHEN si.produtct_type = 4 THEN (
              SELECT am.artist_id FROM artist_meets am 
              WHERE am.id = si.meet_id
            )
          END
        ) = ANY($1)
        AND sd.payment_status = 3
        AND sd.payment_currency = $2
      GROUP BY 
        time_period
      ORDER BY 
        time_period
    `;

    const totalRevenueQuery = `
      SELECT
        SUM(si.purchased_price) AS total_revenue_inr,
        COUNT(DISTINCT sd.id) AS total_orders,
        (
          SELECT 
            SUM(si2.purchased_price)
          FROM 
            sale_item si2
          JOIN 
            sale_details sd2 ON si2.sale_id = sd2.id
          WHERE                         
            ${timeline === "all_time" ? "" : `
              sd2.created >= (CURRENT_DATE - INTERVAL '${timeline === "yearly" ? "2 years" : timeline === "monthly" ? "60 days" : "14 days"}')
              AND sd2.created < (CURRENT_DATE - INTERVAL '${timeline === "yearly" ? "1 year" : timeline === "monthly" ? "30 days" : "6 days"}')
              AND
            `}
            (
              CASE 
                WHEN si2.produtct_type = 1 THEN (
                  SELECT p.artist_id FROM products p 
                  JOIN product_item pi 
                    ON (
                      (p.product_type != 2 AND p.id = pi.product_id)
                      OR
                      (p.product_type = 2 AND p.id = pi.course_product_id)
                    )
                  WHERE pi.id = si2.product_item_id
                )
                WHEN si2.produtct_type = 2 THEN (
                  SELECT cd.artist_id FROM course_details cd 
                  WHERE cd.id = si2.course_id
                )
                WHEN si2.produtct_type = 3 THEN (
                  SELECT pv.artist_id FROM paid_videos pv 
                  WHERE pv.id = si2.video_id
                )
                WHEN si2.produtct_type = 4 THEN (
                  SELECT am.artist_id FROM artist_meets am 
                  WHERE am.id = si2.meet_id
                )
              END
            ) = ANY($1)
            AND sd2.payment_status = 3
            AND sd2.payment_currency = $2
        ) AS previous_period_revenue
      FROM
        sale_item si
      JOIN
        sale_details sd ON si.sale_id = sd.id
      WHERE
        ${timeFilter}
        ${timeline === "all_time" ? "" : "AND"} (
          CASE 
            WHEN si.produtct_type = 1 THEN (
              SELECT p.artist_id FROM products p 
              JOIN product_item pi 
                ON (
                  (p.product_type != 2 AND p.id = pi.product_id)
                  OR
                  (p.product_type = 2 AND p.id = pi.course_product_id)
                ) 
              WHERE pi.id = si.product_item_id
            )
            WHEN si.produtct_type = 2 THEN (
              SELECT cd.artist_id FROM course_details cd 
              WHERE cd.id = si.course_id
            )
            WHEN si.produtct_type = 3 THEN (
              SELECT pv.artist_id FROM paid_videos pv 
              WHERE pv.id = si.video_id
            )
            WHEN si.produtct_type = 4 THEN (
              SELECT am.artist_id FROM artist_meets am 
              WHERE am.id = si.meet_id
            )
          END
        ) = ANY($1)
        AND sd.payment_status = 3
        AND sd.payment_currency = $2
    `;

    const revenueData = await query(revenueQuery, [managed_artist_id, currency]);
    const totalRevenue = await query(totalRevenueQuery, [managed_artist_id, currency]);

    const currentRevenue = parseFloat(totalRevenue.rows[0]?.total_revenue_inr || "0");
    const previousRevenue = parseFloat(totalRevenue.rows[0]?.previous_period_revenue || "0");

    let percentChange = 0;
    if (previousRevenue > 0) {
      percentChange = ((currentRevenue - previousRevenue) / previousRevenue) * 100;
    }

    const chartData = xAxisLabels.map((label) => {
      const dataPoint = revenueData.rows.find((row) =>
        row.time_period.trim().toLowerCase().startsWith(label.toLowerCase())
      );
      return {
        time_period: label,
        revenue: dataPoint ? parseFloat(dataPoint.total_revenue) : 0,
        order_count: dataPoint ? parseInt(dataPoint.order_count) : 0,
      };
    });

    response = {
      status: true,
      message: "Success",
      result: {
        total_revenue: Math.round(currentRevenue),
        percent_change: percentChange.toFixed(1),
        chart_data: chartData,
        total_orders: parseInt(totalRevenue.rows[0]?.total_orders || "0"),
      },
    };

    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error,
    };

    return handleResponse(res, response, 500);
  }
};

export const getTrendingProducts = async (req: any, res: any) => {
  let response: any = {};
  let { timeline, artist_id } = req.query;

  timeline = timeline ? timeline.toLowerCase() : "weekly";

  let managed_artist_id = [req.user.managed_artist_id];
  if (Number(req.user.user_type) == AdminType.Admin) {
    managed_artist_id = artist_id ? [artist_id] : allArtistId;
  }

  try {
    let timeFilter;

    switch (timeline) {
      case "weekly":
        timeFilter = "sd.created >= CURRENT_DATE - INTERVAL '6 days'";
        break;
      case "monthly":
        timeFilter = "sd.created >= CURRENT_DATE - INTERVAL '30 days'";
        break;
      case "yearly":
        timeFilter = "sd.created >= CURRENT_DATE - INTERVAL '1 year'";
        break;
      case "all_time":
        timeFilter = "sd.created >= '2023-01-01'";
        break;
      default:
        timeFilter = "sd.created >= CURRENT_DATE - INTERVAL '6 days'";
    }

    const trendingProductsQuery = `
            WITH product_sales AS (
                SELECT 
                    si.produtct_type,
                    si.product_item_id,
                    si.video_id,
                    si.course_id,
                    si.meet_id,
                    COUNT(*) as item_sold_count,
                    
                    -- Get artist id based on product type
                    CASE 
                        WHEN si.produtct_type = 1 THEN (
                            SELECT p.artist_id FROM products p 
                            JOIN product_item pi 
                              ON (
                                (p.product_type != 2 AND p.id = pi.product_id)
                                OR
                                (p.product_type = 2 AND p.id = pi.course_product_id)
                              )
                            WHERE pi.id = si.product_item_id
                        )
                        WHEN si.produtct_type = 2 THEN (
                            SELECT cd.artist_id FROM course_details cd 
                            WHERE cd.id = si.course_id
                        )
                        WHEN si.produtct_type = 3 THEN (
                            SELECT pv.artist_id FROM paid_videos pv 
                            WHERE pv.id = si.video_id
                        )
                        WHEN si.produtct_type = 4 THEN (
                            SELECT am.artist_id FROM artist_meets am 
                            WHERE am.id = si.meet_id
                        )
                    END as artist_id,
                    
                    -- Get product name based on product type
                    CASE 
                        WHEN si.produtct_type = 1 THEN (
                            SELECT pi.title FROM product_item pi 
                            WHERE pi.id = si.product_item_id
                        )
                        WHEN si.produtct_type = 2 THEN (
                            SELECT cd.title FROM course_details cd 
                            WHERE cd.id = si.course_id
                        )
                        WHEN si.produtct_type = 3 THEN (
                            SELECT pv.title FROM paid_videos pv 
                            WHERE pv.id = si.video_id
                        )
                        WHEN si.produtct_type = 4 THEN (
                            SELECT amd.name FROM artist_meet_detail amd
                            JOIN artist_meets am ON am.id = amd.artist_meet_id
                            WHERE am.id = si.meet_id
                            LIMIT 1
                        )
                    END as product_name,
                    
                    -- Get product image based on product type
                    CASE 
                        WHEN si.produtct_type = 1 THEN (
                            SELECT pi.image_url FROM product_item pi 
                            WHERE pi.id = si.product_item_id
                        )
                        WHEN si.produtct_type = 2 THEN (
                            SELECT cd.image FROM course_details cd 
                            WHERE cd.id = si.course_id
                        )
                        WHEN si.produtct_type = 3 THEN (
                            SELECT pv.image FROM paid_videos pv 
                            WHERE pv.id = si.video_id
                        )
                        WHEN si.produtct_type = 4 THEN (
                            SELECT amd.image FROM artist_meet_detail amd
                            JOIN artist_meets am ON am.id = amd.artist_meet_id
                            WHERE am.id = si.meet_id
                            LIMIT 1
                        )
                    END as product_image,
                    
                    -- Include product type name
                    (SELECT pt.name FROM product_type pt WHERE pt.id = si.produtct_type) as product_type_name,
                    
                    -- Calculate total revenue
                    SUM(
                        CASE 
                            WHEN sd.payment_currency = 1 THEN si.purchased_price
                            WHEN sd.payment_currency = 2 THEN si.purchased_price -- Using hardcoded USD to INR rate
                            ELSE si.purchased_price
                        END
                    ) AS total_revenue
                FROM 
                    sale_item si
                JOIN 
                    sale_details sd ON si.sale_id = sd.id
                WHERE 
                    ${timeFilter}
                    AND sd.payment_status = 3
                GROUP BY 
                    si.produtct_type, 
                    si.product_item_id, 
                    si.video_id, 
                    si.course_id, 
                    si.meet_id
            )
            SELECT 
                ps.produtct_type,
                ps.product_name,
                ps.product_image,
                ps.product_type_name,
                ps.item_sold_count,
                ps.total_revenue
            FROM 
                product_sales ps
            WHERE 
                ps.artist_id = ANY($1)
            ORDER BY 
                ps.item_sold_count DESC
            LIMIT 5
        `;

    // Execute query
    const trendingProducts = await query(trendingProductsQuery, [
      managed_artist_id,
    ]);

    // Format the response
    response = {
      status: true,
      message: "Success",
      result: {
        trending_products: trendingProducts.rows.map((product) => ({
          name: product.product_name,
          image: product.product_image,
          type: product.product_type_name,
          sold_count: parseInt(product.item_sold_count),
          revenue: Math.round(parseFloat(product.total_revenue)),
        })),
      },
    };

    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error,
    };

    return handleResponse(res, response, 500);
  }
};

export const getTopFans = async (req: any, res: any) => {
  let response: any = {};
  let { timeline, artist_id, currency } = req.query;

  // Default to 'weekly' if timeline is not provided
  timeline = timeline ? timeline.toLowerCase() : "weekly";

  // Get managed artist IDs based on user type
  let managed_artist_id = [req.user.managed_artist_id];
  if (Number(req.user.user_type) == AdminType.Admin) {
    managed_artist_id = artist_id ? [artist_id] : allArtistId;
  }

  try {
    // Define time period based on timeline parameter
    let timeFilter;

    switch (timeline) {
      case "weekly":
        timeFilter = "sd.created >= CURRENT_DATE - INTERVAL '6 days'";
        break;
      case "monthly":
        timeFilter = "sd.created >= CURRENT_DATE - INTERVAL '30 days'";
        break;
      case "yearly":
        timeFilter = "sd.created >= CURRENT_DATE - INTERVAL '1 year'";
        break;
      case "all_time":
        timeFilter = "sd.created >= '2023-01-01'";
        break;
      default:
        timeFilter = "sd.created >= CURRENT_DATE - INTERVAL '6 days'";
    }

    const topFansQuery = `
      WITH user_purchases AS (
        SELECT 
          sd.user_id,
          SUM(si.purchased_price) AS total_amount,
          COUNT(DISTINCT sd.id) AS purchase_count
        FROM 
          sale_item si
        JOIN 
          sale_details sd ON si.sale_id = sd.id
        WHERE 
          ${timeFilter}
          AND sd.payment_status = 3
          AND sd.payment_currency = $2
          AND (
            CASE 
              WHEN si.produtct_type = 1 THEN (
                SELECT p.artist_id FROM products p 
                JOIN product_item pi 
                  ON (
                    (p.product_type != 2 AND p.id = pi.product_id)
                    OR
                    (p.product_type = 2 AND p.id = pi.course_product_id)
                  )
                WHERE pi.id = si.product_item_id
              )
              WHEN si.produtct_type = 2 THEN (
                SELECT cd.artist_id FROM course_details cd 
                WHERE cd.id = si.course_id
              )
              WHEN si.produtct_type = 3 THEN (
                SELECT pv.artist_id FROM paid_videos pv 
                WHERE pv.id = si.video_id
              )
              WHEN si.produtct_type = 4 THEN (
                SELECT am.artist_id FROM artist_meets am 
                WHERE am.id = si.meet_id
              )
            END
          ) = ANY($1)
        GROUP BY 
          sd.user_id
      )
      SELECT 
        up.user_id,
        up.total_amount,
        up.purchase_count,
        u.display_name,
        COALESCE(u.email, u.mobile_number) AS contact_info
      FROM 
        user_purchases up
      JOIN 
        users u ON up.user_id = u.id
      ORDER BY 
        up.total_amount DESC
      LIMIT 10
    `;

    const topFans = await query(topFansQuery, [managed_artist_id, currency]);

    response = {
      status: true,
      message: "Success",
      result: {
        top_fans: topFans.rows.map((fan) => ({
          username: fan.display_name || fan.contact_info,
          amount: Math.round(parseFloat(fan.total_amount)),
          purchase_count: parseInt(fan.purchase_count),
        })),
      },
    };

    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error,
    };

    return handleResponse(res, response, 500);
  }
};
