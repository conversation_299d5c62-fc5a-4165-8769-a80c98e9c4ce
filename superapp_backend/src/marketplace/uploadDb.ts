export const insertArtistMeetQuery = `
WITH artist_source AS (
  SELECT source_id FROM artist_meets WHERE artist_id = $1 LIMIT 1
),
inserted_meet AS (
  INSERT INTO artist_meets (artist_id, price, gst, iswhitelisted, gst_code, is_active, source_id)
  SELECT $1, $2, $3, $4, $5, $6, source_id FROM artist_source RETURNING id
),
insert_detail AS (
  INSERT INTO artist_meet_detail (artist_meet_id, image, name, description, is_active, duration,metadata,email)
  SELECT id, $7, $8, $9, $10, $11,$20,$21 FROM inserted_meet
),
insert_price_1 AS (
  INSERT INTO product_prices (meet_id, price, max_price, gst, currency)
  SELECT id, $12, $13, $14, $15 FROM inserted_meet
),
insert_price_2 AS (
  INSERT INTO product_prices (meet_id, price, max_price, gst, currency)
  SELECT id, $16, $17, $18, $19 FROM inserted_meet
),
insert_country1 AS (
  INSERT INTO product_country (country_id, meet_id)
  SELECT 1, id FROM inserted_meet
),
insert_country2 AS (
  INSERT INTO product_country (country_id, meet_id)
  SELECT 2, id FROM inserted_meet
)
SELECT id FROM inserted_meet;
`;

export const insertCourseQuery = `
      WITH inserted_course AS (
        INSERT INTO course_details (
          artist_id, title, sub_title, description, course_level, duration, author_detials,
          validity, price, course_includes, course_language, is_active, gst, filter_keyword,
          image, product_category, course_path, gst_code
        )
        VALUES (
          $1, $2, $3, $4, $5, $6, $7,
          $8, $9, $10, $11, $12, $13, $14,
          $15, $16, $17, $18
        )
        RETURNING id
      ),
      insert_price_1 AS (
        INSERT INTO product_prices (course_id, price, max_price, gst, currency)
        SELECT id, $19, $20, $21, $22 FROM inserted_course
      ),
      insert_price_2 AS (
        INSERT INTO product_prices (course_id, price, max_price, gst, currency)
        SELECT id, $23, $24, $25, $26 FROM inserted_course
      ),
      insert_country1 AS (
        INSERT INTO product_country (country_id, course_id)
        SELECT 1, id FROM inserted_course
      ),
      insert_country2 AS (
        INSERT INTO product_country (country_id, course_id)
        SELECT 2, id FROM inserted_course
      )
      SELECT id FROM inserted_course;
    `;

export const insertProductQuery = `
  WITH 
  inserted_product AS (
    INSERT INTO products (
      product_category_id,
      artist_id,
      name,
      description,
      images,
      price,
      max_price,
      tax,
      is_out_of_stock,
      is_active,
      metadata,
      size_chart,
      is_virtual,
      dispaly_images,
      gst_code,
      is_external,
      is_internal,
      external_link,
      design_data,
      is_cancellable
    )
    VALUES (
      $1, $2, $3, $4, $5, $6::numeric, $7::numeric, $8::numeric, $9, $10,
      $11, $12, $13, $14, $15, $16, $17, $18, $19, $20
    )
    RETURNING id, is_internal
  ),
  insert_price_1 AS (
    INSERT INTO product_prices (product_id, price, max_price, gst, currency)
    SELECT id, $21::numeric, $22::numeric, $23::numeric, $24 FROM inserted_product
  ),
  optional_price AS (
    SELECT id AS product_id, $25::numeric AS price, $26::numeric AS max_price, $27::numeric AS gst, $28::smallint AS currency
    FROM inserted_product
    WHERE $25 IS NOT NULL AND $26 IS NOT NULL AND $27 IS NOT NULL AND $28 IS NOT NULL
  ),
  insert_price_2 AS (
    INSERT INTO product_prices (product_id, price, max_price, gst, currency)
    SELECT product_id, price, max_price, gst, currency FROM optional_price
  ),
  insert_country1 AS (
    INSERT INTO product_country (country_id, product_id)
    SELECT 1, id FROM inserted_product
  ),
  insert_country2 AS (
    INSERT INTO product_country (country_id, product_id)
    SELECT 2, id FROM inserted_product
    WHERE is_internal = true
  ),
  insert_product_item AS (
    INSERT INTO product_item (
      title,
      image_url,
      thumbnail_url,
      product_id,
      sku,
      max_price,
      price,
      is_limited_quantity,
      is_out_of_stock,
      total_quantity,
      sold_quantity,
      is_active
    )
    SELECT
      $3,
      $29,
      $29,
      id,
      $30,
      $22::numeric,
      $21::numeric,
      false,
      false,
      0,
      0,
      true
    FROM inserted_product
  )
SELECT id FROM inserted_product;
`;


export const insertVideoQuery = `
      WITH inserted_paid_video AS (
    INSERT INTO paid_videos (
      title,
      validity,
      price,
      trailer,
      video,
      is_active,
      metadata,
      gst,
      gst_code,
      sku,
      artist_id,
      image,
      product_category
    )
    VALUES (
      $1, $2, $3, $4, $5, $6,
      $7, $8, $9, $10, $11, $12, $13
    )
    RETURNING id
  ),
      insert_price_1 AS (
        INSERT INTO product_prices (video_id, price, max_price, gst, currency)
        SELECT id, $14, $15, $16, $17 FROM inserted_paid_video
      ),
      insert_price_2 AS (
        INSERT INTO product_prices (video_id, price, max_price, gst, currency)
        SELECT id, $18, $19, $20, $21 FROM inserted_paid_video
      ),
      insert_country1 AS (
        INSERT INTO product_country (country_id, video_id)
        SELECT 1, id FROM inserted_paid_video
      ),
      insert_country2 AS (
        INSERT INTO product_country (country_id, video_id)
        SELECT 2, id FROM inserted_paid_video
      )
      SELECT id FROM inserted_paid_video;
    `;

export const insertArtistVideoQuery = `
  INSERT INTO artist_video (
    video_id,
    video,
    description,
    is_active,
    artist_id,
    old_video
  ) VALUES (
    $1, $2, $3, $4, $5, $6
  )
  RETURNING *
`;
