export const getProductsQuery = `
SELECT 
    p.id product_id,
    NULL AS level_id,
    a.name artist_name,
    p.name product_name,
    p.images product_image,
    'PRODUCT' product_type,
    p.product_category_id,
    p.design_data,
    NULL AS course_level,
    (
        SELECT json_agg(
            JSONB_BUILD_OBJECT(
                'currency', pcr.name,
                'currency_id', pcr.id,
                'price', pp.price,
                'max_price', pp.max_price,
                'gst', pp.gst
            )
        )
        FROM product_prices pp
        INNER JOIN payment_currency pcr ON pcr.id = pp.currency
        WHERE pp.product_id = p.id
    ) AS currencies,
    (
        SELECT json_agg(
            json_build_object(
                'id', pc.id,
                'country_name', cl.name,
                'country_id', cl.id,
                'item_id', pc.product_id
            )
        )
        FROM product_country pc
        INNER JOIN country_list cl ON cl.id = pc.country_id
        WHERE pc.product_id = p.id
    ) AS countries,
    uw.id IS NOT NULL AS is_wishlist
FROM products p
INNER JOIN product_category pc ON pc.id = p.product_category_id
INNER JOIN artist a ON a.id = p.artist_id
INNER JOIN product_country cn ON cn.product_id = p.id
LEFT JOIN user_wishlist uw ON uw.product_id = p.id AND uw.user_id = $3
WHERE p.is_active = true 
  AND (p.artist_id = ANY($1) OR $1 IS NULL)
  AND (p.product_category_id = ANY($2) OR $2 IS NULL)
  AND (cn.country_id = ANY($4) OR $4 IS NULL)
GROUP BY p.id, a.name, uw.id

UNION ALL

SELECT 
    cd.course_level_id AS product_id,
    NULL AS level_id,
    a.name AS artist_name,
    cd.title AS product_name,
    array[cd.image] AS product_image,
    'COURSE' AS product_type,
    cd.product_category AS product_category_id,
    NULL AS design_data,
    cd.course_level AS course_level,
    (
        SELECT json_agg(
            JSONB_BUILD_OBJECT(
            'currency', pcr.name,
            'currency_id', pcr.id,
            'price', pp.price,
            'max_price', pp.max_price,
            'gst', pp.gst
            )
        )
        FROM product_prices pp
        INNER JOIN payment_currency pcr ON pcr.id = pp.currency
        WHERE pp.course_id = cd.course_level_id
    ) AS currencies,
    (
        SELECT json_agg(
            json_build_object(
                'id', pc.id,
                'country_name', cl.name,
                'country_id', cl.id,
                'item_id', pc.course_id
            )
        )
        FROM product_country pc
        INNER JOIN country_list cl ON cl.id = pc.country_id
        WHERE pc.course_id = cd.course_level_id
    ) AS countries,
    uw.id IS NOT NULL AS is_wishlist
FROM course_details cd
INNER JOIN artist a ON a.id = cd.artist_id
INNER JOIN product_country cn ON cn.course_id = cd.course_level_id
LEFT JOIN user_wishlist uw ON uw.course_id = cd.course_level_id AND uw.user_id = $3
WHERE cd.is_active = true 
  AND (cd.artist_id = ANY($1) OR $1 IS NULL)
  AND (cd.product_category = ANY($2) OR $2 IS NULL)
  AND (cn.country_id = ANY($4) OR $4 IS NULL)
GROUP BY cd.course_level_id, a.name, cd.title, cd.image, cd.product_category, cd.course_level, uw.id

UNION ALL

SELECT 
    am.id product_id,
    NULL AS level_id,
    a.name artist_name,
    amd.name product_name,
    array[amd.image] product_image,
    'MEET' product_type, 
    am.product_category product_category_id,
    NULL AS design_data,
    NULL AS course_level,
    (
        SELECT json_agg(
            JSONB_BUILD_OBJECT(
                'currency', pcr.name,
                'currency_id', pcr.id,
                'price', pp.price,
                'max_price', pp.max_price,
                'gst', pp.gst
            )
        )
        FROM product_prices pp
        INNER JOIN payment_currency pcr ON pcr.id = pp.currency
        WHERE pp.meet_id = am.id
    ) AS currencies,
    (
        SELECT json_agg(
            json_build_object(
                'id', pc.id,
                'country_name', cl.name,
                'country_id', cl.id,
                'item_id', pc.meet_id
            )
        )
        FROM product_country pc
        INNER JOIN country_list cl ON cl.id = pc.country_id
        WHERE pc.meet_id = am.id
    ) AS countries,
    uw.id IS NOT NULL AS is_wishlist
FROM artist_meets am
INNER JOIN artist_meet_detail amd ON amd.artist_meet_id = am.id
INNER JOIN artist a ON a.id = am.artist_id
INNER JOIN product_country cn ON cn.meet_id = am.id
LEFT JOIN user_wishlist uw ON uw.meet_id = am.id AND uw.user_id = $3
WHERE am.is_active = true 
  AND (am.artist_id = ANY($1) OR $1 IS NULL)
  AND (am.product_category = ANY($2) OR $2 IS NULL)
  AND (cn.country_id = ANY($4) OR $4 IS NULL)
GROUP BY am.id, amd.name, a.name, amd.image, uw.id

UNION ALL

SELECT 
    pv.id product_id,
    NULL AS level_id,
    a.name artist_name,
    pv.title AS product_name,
    array[pv.image] product_image,
    'PAID_VIDEO' product_type,
    pv.product_category product_category_id,
    NULL AS design_data,
    NULL AS course_level,
    (
        SELECT json_agg(
            JSONB_BUILD_OBJECT(
                'currency', pcr.name,
                'currency_id', pcr.id,
                'price', pp.price,
                'max_price', pp.max_price,
                'gst', pp.gst
            )
        )
        FROM product_prices pp
        INNER JOIN payment_currency pcr ON pcr.id = pp.currency
        WHERE pp.video_id = pv.id
    ) AS currencies,
    (
        SELECT json_agg(
            json_build_object(
                'id', pc.id,
                'country_name', cl.name,
                'country_id', cl.id,
                'item_id', pc.video_id
            )
        )
        FROM product_country pc
        INNER JOIN country_list cl ON cl.id = pc.country_id
        WHERE pc.video_id = pv.id
    ) AS countries,
    uw.id IS NOT NULL AS is_wishlist 
FROM paid_videos pv
INNER JOIN artist a ON a.id = pv.artist_id
INNER JOIN product_country cn ON cn.video_id = pv.id
LEFT JOIN user_wishlist uw ON uw.video_id = pv.id AND uw.user_id = $3
WHERE pv.is_active = true 
  AND (pv.artist_id = ANY($1) OR $1 IS NULL)
  AND (pv.product_category = ANY($2) OR $2 IS NULL)
  AND (cn.country_id = ANY($4) OR $4 IS NULL)
GROUP BY pv.id, a.name, pv.title, pv.image, pv.product_category, uw.id
`;

export const queryforTLS = `SELECT 
    cd.id product_id,
    a.name artist_name,
    cd.title AS product_name,
    array[cd.image] product_image,
    'COURSE' product_type,
    cd.product_category product_category_id,
    null design_data,
    cd.course_level AS course_level,
	(
        SELECT json_agg(
            JSONB_BUILD_OBJECT(
            'currency', pcr.name,
            'currency_id', pcr.id,
            'price', pp.price,
            'max_price', pp.max_price,
            'gst', pp.gst)
        )
        FROM product_prices pp
        INNER JOIN payment_currency pcr ON pcr.id = pp.currency
        WHERE pp.course_id = cd.id
        GROUP BY pp.course_id
    ) AS currencies,
    (
        SELECT json_agg(
            json_build_object(
                'id', pc.id,
                'country_name', cl.name,
                'country_id', cl.id,
                'item_id', pc.course_id
            )
        )
        FROM product_country pc
        INNER JOIN country_list cl ON cl.id = pc.country_id
        WHERE pc.course_id = cd.id
        GROUP BY pc.course_id
    ) AS countries,
    uw.id is not null is_wishlist
FROM course_details cd
INNER JOIN artist a ON a.id = cd.artist_id
INNER JOIN product_country cn ON cn.course_id = cd.id
left join user_wishlist uw on uw.course_id = cd.id and uw.user_id = $3
WHERE cd.is_active = true 
  AND (cd.artist_id = ANY($1) OR $1 IS NULL)
  AND (cd.product_category = ANY($2) OR $2 IS NULL)
  AND (cn.country_id = ANY($4) OR $4 IS NULL)
GROUP BY cd.id,a.name,cd.course_level,uw.id`

export const getProdCategoriesQuery = `select id,name from product_category where is_active = true;`;