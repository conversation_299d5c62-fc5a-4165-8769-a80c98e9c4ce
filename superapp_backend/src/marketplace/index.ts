import { getProductsByType, updateWishlist } from "../services/orders";
import { handleResponse } from "../common";
import { query } from "../db";
import { getProdCategoriesQuery, getProductsQuery, getArtistIdByArtistDomain, getAllArtistIds } from "./db";

export const getMarketProductList = async (req: any, res: any) => {
    let response: any = {}
    const userId = req.user != null ? req.user.id : null;
    try {
        const { artistDomain, categoryId, countryId } = req.query;
        const artistDomains = artistDomain ? artistDomain.split(',') : [];
        
        let artistIds: any[] = [];
        
        if (artistDomains.length > 0) {
            const artistIdPromises = artistDomains.map(async (domain: string) => {
                const result = await query(getArtistIdByArtistDomain, [domain]);
                return result.rows[0]?.id || null;
            });
            
            const resolvedArtistIds = await Promise.all(artistIdPromises);
            const validArtistIds = resolvedArtistIds.filter(id => id !== null);
            
            if (validArtistIds.length === 0) {
                const allArtistsResult = await query(getAllArtistIds, []);
                artistIds = allArtistsResult.rows.map(row => row.id);
            } else {
                artistIds = validArtistIds;
            }
        }
        
        const catIds = categoryId ? categoryId.split(',') : [];
        const cntIds = countryId ? countryId.split(',') : [];
        
        const result = await query(getProductsQuery, [
            artistIds.length ? artistIds : null, 
            catIds.length ? catIds : null, 
            userId, 
            cntIds.length ? cntIds : null
        ]);
        
        response = {
            status: true,
            message: `Success`,
            data: result.rows
        }
        
        return handleResponse(res, response, 200);
    } catch (error: any) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}

export const getMarketProductCategories = async (req: any, res: any) => {
    let response: any = {}

    try {
        let result = await query(getProdCategoriesQuery, []);
        response = {
            status: true,
            message: `Success`,
            data: result.rows

        }
        return handleResponse(res, response, 200);
    } catch (error: any) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}

export const addWishList = async (req: any, res: any) => {
    let response: any = {}
    const user = req.user;
    const { id, type } = req.body;
    if (!id || !type) {
        response = {
            status: false,
            message: `Missing Params`

        }
        return handleResponse(res, response, 400);
    }
    try {
        const pdt = await getProductsByType(id, type, user.id);

        if (!pdt.length) {
            response = {
                status: false,
                message: `Product not found`

            }
            return handleResponse(res, response, 400);
        }

        if (!pdt[0].wishlist_id) {
            const wishlist = await updateWishlist(id, type, user.id);
            if (!wishlist.rowCount) {
                throw Error("Wishlist not updated");
            }
        }

        response = {
            status: true,
            message: `Success`

        }
        return handleResponse(res, response, 200);
    } catch (error: any) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}

export const removeWishList = async (req: any, res: any) => {
    let response: any = {}
    const user = req.user;
    const { id, type } = req.body;
    if (!id || !type) {
        response = {
            status: false,
            message: `Missing Params`

        }
        return handleResponse(res, response, 400);
    }
    try {
        const pdt = await getProductsByType(id, type, user.id);

        if (!pdt.length) {
            response = {
                status: false,
                message: `Product not found`

            }
            return handleResponse(res, response, 400);
        }

        if (pdt[0].wishlist_id) {
            const wishlist = await updateWishlist(id, type, user.id, pdt[0].wishlist_id);
            if (!wishlist.rowCount) {
                throw Error("Wishlist not updated");
            }
        }

        response = {
            status: true,
            message: `Success`

        }
        return handleResponse(res, response, 200);
    } catch (error: any) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}