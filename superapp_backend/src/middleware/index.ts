import { handleResponse } from '../common';
import { OAuth2Client } from 'google-auth-library';
import * as jwt from 'jsonwebtoken';


const CLIENT_ID = '408000348929-dh5p33s2jp3invd78brqubhpgh26vfgq.apps.googleusercontent.com';
const client = new OAuth2Client(CLIENT_ID);

export const verifyGoogleToken = async (req: any, res: any, next: any) => {
    let response = {
        status: false,
        message: `Request failed`,
        error: ''
    }
    const token = req.body.token;

    try {
        if (!token) {
            response.error = "No Token Provided";
            handleResponse(res, response, 401);
        }
        const ticket = await client.verifyIdToken({
            idToken: token,
            audience: CLIENT_ID,
        });
        const payload = ticket.getPayload();
        req.user = payload;
        next();
    } catch (error: any) {
        response.error = error;
        return handleResponse(res, response, 500);
    }
}

export const validate = async (req: any, res: any, next: any) => {
    const token = req.headers.authorization;
    let response = {
        status: false,
        message: `Request failed`,
        error: ''
    }
    try {
        if (!token) {
            response.error = "No Token Provided";
            return handleResponse(res, response, 401);
        }
        const decoded: any = jwt.verify(token, process.env.SECRET!);

        if (!decoded.id) {
            return handleResponse(res, { status: false, message: "Error in Token" }, 401);
        }

        req.user = decoded;
        next();
    } catch (error) {
        response.error = "Error in decoding token";
        return handleResponse(res, response, 401);
    }
}

export const optionalValidate = async (req: any, res: any, next: any) => {
    const token = req.headers.authorization;
    let response = {
        status: false,
        message: `Request failed`,
        error: ''
    }
    try {
        if (token) {
            const decoded: any = jwt.verify(token, process.env.SECRET!);
            if (!decoded.id) {
                return handleResponse(res, { status: false, message: "Error in Token" }, 401);
            }
            req.user = decoded;
        } else {
            req.user = null;
        }
        next();
    } catch (error) {
        response.error = "Error in decoding token";
        return handleResponse(res, response, 401);
    }
}

export const adminValidate = async (req: any, res: any, next: any) => {
    const token = req.headers.authorization;
    let response = {
        status: false,
        message: `Request failed`,
        error: ''
    }
    try {
        if (!token) {
            response.error = "No Token Provided";
            return handleResponse(res, response, 401);
        }
        const decoded: any = jwt.verify(token, process.env.SECRET!);

        if (!decoded.id) {
            return handleResponse(res, { status: false, message: "Error in Token" }, 401);
        }

        if (!decoded.is_admin) {
            return handleResponse(res, { status: false, message: "Invalid User" }, 401);
        }

        req.user = decoded;
        next();
    } catch (error) {
        response.error = "Error in decoding token";
        return handleResponse(res, response, 401);
    }
}

export const artistValidate = async (req: any, res: any, next: any) => {
    const token = req.query.token;
    let response = {
        status: false,
        message: `Request failed`,
        error: ''
    }
    try {
        if (!token) {
            response.error = "No Token Provided";
            return handleResponse(res, response, 401);
        }
        const decoded: any = jwt.verify(token, process.env.SECRET!);

        if (!decoded.is_artist) {
            return handleResponse(res, { status: false, message: "Invalid User" }, 401);
        }

        req.decoded = decoded;

        next();
    } catch (error) {
        response.error = "Error in decoding token";
        return handleResponse(res, response, 401);
    }
}