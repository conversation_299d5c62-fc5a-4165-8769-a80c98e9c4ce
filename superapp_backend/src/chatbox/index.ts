import { firestoreDB } from "../services/firebase_auth";

export const deleteOldRecords = async (collectionName: string) => {
    try {
        const collectionRef = firestoreDB.collection(collectionName);
        const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

        const querySnapshot = await collectionRef
            .where('timestamp', '<', twentyFourHoursAgo)
            .get();

        const batch = firestoreDB.batch();
        querySnapshot.forEach(doc => {
            batch.delete(doc.ref);
        });

        await batch.commit();
    } catch (error) {
        console.error('Error deleting records:', error);
    }
}