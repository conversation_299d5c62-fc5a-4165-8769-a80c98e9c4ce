import {
  insertArtistMeetQuery,
  insertArtistVideoQuery,
  insertCourseQuery,
  insertProductQuery,
  insertVideoQuery,
  getArtistVideoCount,
} from "../marketplace/uploadDb";
import { handleResponse } from "../common";
import { query } from "../db";

export const uploadVideo = async (req: any, res: any) => {
  let {
    price1,
    max_price1,
    gst1,
    currency1,
    price2,
    max_price2,
    gst2,
    currency2,

    // Video
    title,
    validity,
    price,
    trailer,
    video,
    is_active,
    metadata,
    gst,
    gst_code,
    sku,
    artist_id,
    image,
    product_category,
  } = req.body;

  sku = sku ? JSON.stringify(sku) : null;
  metadata = metadata ? JSON.stringify(metadata) : null;

  let response: any = {};
  try {
    const result = await query(insertVideoQuery, [
      title,
      validity,
      price,
      trailer,
      video,
      is_active,
      metadata,
      gst,
      gst_code,
      sku,
      artist_id,
      image,
      product_category,
      price1,
      max_price1,
      gst1,
      currency1,
      price2,
      max_price2,
      gst2,
      currency2,
    ]);

    if (!result?.rows?.length) {
      response = {
        status: false,
        message: "Failed to insert artist Product",
      };
      return handleResponse(res, response, 400);
    }

    response = {
      status: true,
      message: "Artist Product inserted successfully",
      data: { id: result.rows[0].id },
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    console.error("Upload error:", error);
    response = {
      status: false,
      message: "Internal server error",
      error,
    };
    return handleResponse(res, response, 500);
  }
};

export const uploadProduct = async (req: any, res: any) => {
  let {
    price1,
    max_price1,
    gst1,
    currency1,
    price2,
    max_price2,
    gst2,
    currency2,

    // product
    product_category_id,
    artist_id,
    name,
    description,
    images,
    price,
    max_price,
    tax,
    is_out_of_stock,
    is_active,
    metadata,
    size_chart,
    is_virtual,
    dispaly_images,
    gst_code,
    is_external,
    is_internal,
    external_link,
    design_data,
    is_cancellable,
  } = req.body;

  design_data = design_data ? JSON.stringify(design_data) : null;
  dispaly_images = dispaly_images ? JSON.stringify(dispaly_images) : null;
  metadata = metadata ? JSON.stringify(metadata) : null;

  let response: any = {};
  try {
    const result = await query(insertProductQuery, [
      product_category_id,
      artist_id,
      name,
      description,
      images,
      price,
      max_price,
      tax,
      is_out_of_stock,
      is_active,
      metadata,
      size_chart,
      is_virtual,
      dispaly_images,
      gst_code,
      is_external,
      is_internal,
      external_link,
      design_data,
      is_cancellable,

      price1,
      max_price1,
      gst1,
      currency1,
      price2,
      max_price2,
      gst2,
      currency2,
    ]);

    if (!result?.rows?.length) {
      response = {
        status: false,
        message: "Failed to insert artist Product",
      };
      return handleResponse(res, response, 400);
    }

    response = {
      status: true,
      message: "Artist Product inserted successfully",
      data: { id: result.rows[0].id },
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    console.error("Upload error:", error);
    response = {
      status: false,
      message: "Internal server error",
      error,
    };
    return handleResponse(res, response, 500);
  }
};

export const uploadMeet = async (req: any, res: any) => {
  const {
    // merchType,
    artist_id,
    price,
    gst,
    gst_code,
    is_active,
    image,
    description,
    duration,
    price1,
    max_price1,
    gst1,
    currency1,
    price2,
    max_price2,
    gst2,
    currency2,

    // meet
    iswhitelisted,
    name,
    detail_active,
    metaData,
    email,
  } = req.body;

  let response: any = {};
  try {
    const result = await query(insertArtistMeetQuery, [
      artist_id,
      price,
      gst,
      iswhitelisted,
      gst_code,
      is_active,
      image,
      name,
      description,
      detail_active,
      duration,
      price1,
      max_price1,
      gst1,
      currency1,
      price2,
      max_price2,
      gst2,
      currency2,
      metaData,
      email,
    ]);

    if (!result?.rows?.length) {
      response = {
        status: false,
        message: "Failed to insert artist meet",
      };
      return handleResponse(res, response, 400);
    }

    response = {
      status: true,
      message: "Artist meet inserted successfully",
      data: { id: result.rows[0].id },
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    console.error("Upload error:", error);
    response = {
      status: false,
      message: "Internal server error",
      error,
    };
    return handleResponse(res, response, 500);
  }
};

export const uploadCourse = async (req: any, res: any) => {
  let {
    artist_id,
    price,
    gst,
    gst_code,
    is_active,
    image,
    description,
    duration,
    price1,
    max_price1,
    gst1,
    currency1,
    price2,
    max_price2,
    gst2,
    currency2,

    //course
    title,
    sub_title,
    course_level,
    author_detials,
    validity,
    course_includes,
    course_language,
    filter_keyword,
    product_category,
    course_path,
  } = req.body;

  let response: any = {};

  try {
    course_includes = course_includes || [];
    filter_keyword = filter_keyword || [];
    duration = JSON.stringify(duration);
    author_detials = JSON.stringify(author_detials);
    is_active = is_active === "true";
    artist_id = Number(artist_id);
    price = Number(price);
    gst = Number(gst);
    price1 = Number(price1);
    max_price1 = Number(max_price1);
    gst1 = Number(gst1);
    currency1 = Number(currency1);
    price2 = Number(price2);
    max_price2 = Number(max_price2);
    gst2 = Number(gst2);
    currency2 = Number(currency2);
    product_category = Number(product_category);
    if (sub_title === "null" || sub_title === "") sub_title = null;

    const result = await query(insertCourseQuery, [
      artist_id,
      title,
      sub_title,
      description,
      course_level,
      duration,
      author_detials,
      validity,
      price,
      course_includes,
      course_language,
      is_active,
      gst,
      filter_keyword,
      image,
      product_category,
      course_path,
      gst_code,
      price1,
      max_price1,
      gst1,
      currency1,
      price2,
      max_price2,
      gst2,
      currency2,
    ]);

    if (!result?.rows?.length) {
      response = {
        status: false,
        message: "Failed to insert Course",
      };
      return handleResponse(res, response, 400);
    }

    response = {
      status: true,
      message: "Course inserted successfully",
      data: { id: result.rows[0].id },
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    console.error("Upload error:", error);
    response = {
      status: false,
      message: "Internal server error",
      error,
    };
    return handleResponse(res, response, 500);
  }
};

interface ArtistVideoPayload {
  video: string;
  is_active: boolean;
  artist_id: number;
  old_video: string;
}

export const uploadArtistVideo = async (
  payload: ArtistVideoPayload
): Promise<{
  status: boolean;
  message: string;
  data?: { id: string };
  error?: any;
}> => {
  const { video, is_active, artist_id, old_video } = payload;

  try {
    const countResult = await query(getArtistVideoCount, [artist_id]); 
    const currentCount = Number(countResult.rows[0]?.total) || 0;
    const video_id = (artist_id * 1000 + currentCount + 1).toString();
    const result = await query(insertArtistVideoQuery, [
      video_id,
      video,
      null,
      is_active,
      artist_id,
      old_video,
    ]);

    if (!result?.rows?.length) {
      return {
        status: false,
        message: "Failed to insert Artist Video",
      };
    }

    return {
      status: true,
      message: "Artist Video inserted successfully",
      data: { id: result.rows[0].id },
    };
  } catch (error) {
    console.error("Upload error:", error);
    return {
      status: false,
      message: "Internal server error",
      error,
    };
  }
};