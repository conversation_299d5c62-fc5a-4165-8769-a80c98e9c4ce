import axios from "axios";
import crypto from "crypto";
import { PaymentCallback, PayInitBody } from "../types/pg_types";
import { PAYMENT_METHOD } from "../common/constants";

const merchantId = process.env.MERCHANT_ID;
const salt_key = process.env.SALT_KEY;
const BASE_URL = process.env.BASE_URL;

const redirectMode = "REDIRECT";
const paymentInstrument = {
    "type": "PAY_PAGE"
};
const key_index = 1;
const PAY_ENDPOINT = '/pg/v1/pay';
const STATUS_ENDPOINT = '/pg/v1/status';

const redirectUrl = process.env.REDIRECT_URL;

const PAY_URL = BASE_URL + PAY_ENDPOINT;
const STATUS_URL = BASE_URL + STATUS_ENDPOINT;


// For Initate Payment subset
const encodeBase64 = (payload: any) => {
    return Buffer.from(JSON.stringify(payload)).toString("base64");
}

// For Initate Payment subset
const encryptSHA256 = (payload: any) => {
    return crypto
        .createHash("sha256")
        .update(payload)
        .digest("hex");
}


// For Initate Payment request to phonepe
export const initiatePayment = async (base64: string, xVerify: string) => {
    // Reference Link https://developer.phonepe.com/v1/reference/pay-api-1
    try {
        const resp = await axios({
            method: "post",
            url: PAY_URL,
            headers: {
                "Content-Type": "application/json",
                "X-VERIFY": xVerify
            },
            data: {
                "request": base64
            }
        })
        return resp.data;
    } catch (error) {
        console.error("Error in initating payment", error);
        return Promise.reject(error);
    }
}

// For Verifying Payment callback from phonepe server
export const decodeBase64 = (base64string: string) => {
    // Create a buffer from the string
    let bufferObj = Buffer.from(base64string, "base64");
    // Encode the Buffer as a utf8 string
    let decodedString = bufferObj.toString("utf8");
    let respJSON = JSON.parse(decodedString);
    return respJSON;
}

// For Verifying Payment callback from phonepe server
export const convertSha256 = (data: string) => {
    var hmac = crypto.createHmac('sha256', process.env.RP_SECRET || '');
    var sha = hmac.update(data);
    const generated_signature = sha.digest('hex');
    return generated_signature;
}

export const runPayAPI = async (data: PayInitBody, callbackUrl: string) => {
    try {
        let requestBody = {
            merchantId,
            ...data,
            redirectUrl,
            redirectMode,
            callbackUrl,
            paymentInstrument
        }
        const b64 = encodeBase64(requestBody);
        const sha256 = encryptSHA256(b64 + PAY_ENDPOINT + salt_key);
        const xVerify = sha256 + "###" + key_index;
        // To Initiate the payment
        const pay = await initiatePayment(b64, xVerify);

        return {
            status: true,
            url: pay.data.instrumentResponse.redirectInfo.url,
            payment_method: PAYMENT_METHOD.PHONEPE,
            order_id: pay.data.merchantTransactionId
        }
    } catch (error) {
        return Promise.reject({
            status: false,
            data: error
        })
    }
}

export const testCallbackData = () => {
    // Sample responce for call back data for UPI
    // Refer this link for more detail 
    // https://developer.phonepe.com/v1/reference/server-to-server-callback-5
    let data = {
        "response": "ewogICJzdWNjZXNzIjogdHJ1ZSwKICAiY29kZSI6ICJQQVlNRU5UX1NVQ0NFU1MiLAogICJtZXNzYWdlIjogIllvdXIgcmVxdWVzdCBoYXMgYmVlbiBzdWNjZXNzZnVsbHkgY29tcGxldGVkLiIsCiAgImRhdGEiOiB7CiAgICAibWVyY2hhbnRJZCI6ICJQR1RFU1RQQVlVQVQiLAogICAgIm1lcmNoYW50VHJhbnNhY3Rpb25JZCI6ICJNVDc4NTA1OTAwNjgxODgxMDQiLAogICAgInRyYW5zYWN0aW9uSWQiOiAiVDIxMTEyMjE0Mzc0NTYxOTAxNzAzNzkiLAogICAgImFtb3VudCI6IDEwMCwKICAgICJzdGF0ZSI6ICJDT01QTEVURUQiLAogICAgInJlc3BvbnNlQ29kZSI6ICJTVUNDRVNTIiwKICAgICJwYXltZW50SW5zdHJ1bWVudCI6IHsKICAgICAgInR5cGUiOiAiVVBJIiwKICAgICAgInV0ciI6ICIyMDYzNzg4NjYxMTIiCiAgICB9CiAgfQp9Cg=="
    }
    decodeBase64(data.response)
}

export const paymentStatusCheck = async (merchantTransactionId: string) => {
    // Reference Link https://developer.phonepe.com/v1/reference/check-status-api-1
    const sha256 = encryptSHA256(STATUS_ENDPOINT + "/" + merchantId + "/" + merchantTransactionId + salt_key);
    const xVerify = sha256 + "###" + key_index;
    try {
        const resp = await axios({
            method: "GET",
            url: STATUS_URL + "/" + merchantId + "/" + merchantTransactionId,
            headers: {
                "Content-Type": "application/json",
                "X-VERIFY": xVerify,
                "X-MERCHANT-ID": merchantId
            }
        });
        return resp.data as PaymentCallback;
    } catch (error) {
        console.error("Error", error);
        return Promise.reject(error);
    }
}
