import Razorpay from "razorpay";
import randomstring from "randomstring";

export var instance = process.env.RP_KEY ? new Razorpay({
    key_id: process.env.RP_KEY || '',
    key_secret: process.env.RP_SECRET || '',
}) : null;

export const createdOrder = async (amount: number, currency: number, callbackUrl: string) => {
    const amountPs = Math.round(Number(amount) * 100)

    if (!amountPs) {
        return Promise.reject({ message: "Invalid params" })
    }
    try {
        var options = {
            amount: amountPs,  // amount in the smallest currency unit
            currency: currency == 1 ? "INR" : "USD",
            receipt: `order_rcptid_${randomstring.generate(10)}`
        };
        const order = await instance!.orders.create(options);
        return Promise.resolve({
            amount: amountPs,
            currency: order.currency,
            id: order.id,
            keyId: process.env.RP_KEY || "",
            callbackUrl: callbackUrl
        });
    } catch (error) {
        console.error("Error on razorpay initialization : ", JSON.stringify(error));
        return Promise.reject({ message: "Error" })
    }
}