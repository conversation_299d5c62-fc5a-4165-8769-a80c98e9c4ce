import { handleResponse } from "../common";
const { S3 } = require('@aws-sdk/client-s3');

const s3AwsClient = new S3({
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY
    },
    region: process.env.AWS_REGION
});

const bucketName = 'metastar-streaming';

export const getCourseVideo = async (req: any, res: any) => {
    try {
        const path = req.query.id;
        const s3Options = {
            Bucket: bucketName,
            Key: path
        };

        const { ContentLength } = await s3AwsClient.headObject(s3Options);
        const videoSize = ContentLength;

        const FILE_PORTION_SIZE = 5000000; //bytes = 5MB
        const requestedRange = req.headers.range || '';


        if (!requestedRange) {
            res.status(400).send("Requires Range header");
            return;
        }


        const parts = requestedRange.replace(/bytes=/, "").split("-");
        const start = parseInt(parts[0], 10);
        const end = parts[1] ? parseInt(parts[1], 10) : Math.min(start + FILE_PORTION_SIZE - 1, videoSize - 1);
        const contentLength = end - start + 1;

        res.status(206);
        res.setHeader('Accept-Ranges', 'bytes');
        res.setHeader('Content-Range', `bytes ${start}-${end}/${videoSize}`);
        res.setHeader('Content-Length', contentLength);
        res.setHeader('Content-Type', "video/mp4");

        const streamRange = `bytes=${start}-${end}`;
        const s3Object = await s3AwsClient.getObject({
            ...s3Options,
            Range: streamRange
        });

        s3Object.Body.pipe(res);
    } catch (error) {
        return handleResponse(res, {
            status: false,
            message: `Streaming failed`
        }, 500);
    }
}