// import { PaymentRoot } from "../types/paypal";
import { PAYMENT_METHOD } from "../common/constants";
import { makePostCall } from "../common";

const PAYPAL = {
    BASEURl: process.env.PL_BASEURL || '',
    TOKEN: `/v1/oauth2/token`,
    CREATE_ORDER: `/v2/checkout/orders`,
}


const {
    PAYPAL_CLIENT_ID,
    PAYPAL_CLIENT_SECRET,
    BC_BASE_URL
} = process.env;

const getAccessToken = async () => {
    try {
        const api = await makePostCall(`${PAYPAL.BASEURl}/v1/oauth2/token`, "grant_type=client_credentials", {
            auth: {
                username: PAYPAL_CLIENT_ID || '',
                password: PAYPAL_CLIENT_SECRET || '',
            },
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
        })
        return api.access_token;
    } catch (error: any) {
        console.error("Error generating access token:", error.response.data);
        throw new Error("Failed to generate access token");
    }
}

export const createOrder = async (amount: string, currency_code: string) => {
    const orderData = {
        intent: "CAPTURE",
        purchase_units: [
            {
                amount: {
                    currency_code: currency_code,
                    value: amount,
                },
            },
        ],
        application_context: {
            brand_name: "Metastar",
            user_action: "PAY_NOW",
            return_url: BC_BASE_URL + "/api/paypal-callback",
            cancel_url: "https://your-site.com/cancel",
        },
    };

    try {
        const token = await getAccessToken();

        const api = await makePostCall(`${PAYPAL.BASEURl}/v2/checkout/orders`, orderData, {
            headers: {
                Authorization: `Bearer ${token}`,
                "Content-Type": "application/json",
            },
        })

        return {
            order_id: api.id,
            url: api.links.find((el: any) => el.rel == 'approve').href,
            payment_method: PAYMENT_METHOD.PAYPAL
        }
    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Error on creating order paypal`
        });
    }
};

// export const captureOrder = async (orderId: string) => {
//     try {
//         const token = await getAccessToken();
//         const api = await makePostCall(`${PAYPAL.BASEURl}/v2/checkout/orders/${orderId}/capture`, {}, {
//             headers: {
//                 Authorization: `Bearer ${token}`,
//                 "Content-Type": "application/json",
//             },
//         })
//         return api as PaymentRoot;
//     } catch (error) {
//         console.error("Error capturing order: ", error);
//         return Promise.reject({
//             status: false,
//             message: `Error capturing order`
//         });
//     }
// }