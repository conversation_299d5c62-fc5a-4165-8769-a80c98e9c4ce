import { BGMeetInvoiceEmail, MeetInvoiceEmail, MeetInvoiceEmailPGOffline, PHONE_PE_STATUS } from "../common/constants";
import { addWishListQuery, courseOrderQuery, deleteWishlistQuery, getCourseQuery, getMeetQuery, getOfferQuery, getPdtsQuery, getProductOfferQuery, getVideoQuery, meetOrderDetailPdfQuery, offerUsedCountQuery, orderInvoiceDetailByIdQuery, paidVidOrderQuery, userAddressByIdQuery, userDetailAddressAltQuery } from "../common/db_constants";
import { query } from "../db";
import { generatePDF } from "./pdf";

const productVariantsQuery = `
select 
	pi.id, oi.quantity, 
	oi.purchased_price total, pp.gst tax, 
	oi.price_per_quantity price,
	p.name || 
	coalesce(' - ' || string_agg(vo.value,', '),'') || 
	coalesce(' - ' || p.gst_code,'') title,
	pi.image_url image, 
	pp.price as original_price,
	od.offer_id,
	array_agg(
		jsonb_build_object(
    	coalesce(v.short_name, ''), coalesce(vo.value, '')
  		)
	) as variants
from order_items oi 
inner join product_item pi on pi.id = oi.product_item_id
inner join order_details od on od.id = oi.order_id
inner join products p on p.id = pi.product_id
inner join product_prices pp on pp.product_id = p.id and pp.currency = od.payment_currency 
left join product_configuration pc on pc.product_item_id = pi.id
left join variation_option vo on vo.id = pc.variation_option_id
left join variation v on v.id = vo.variation_id
where oi.order_id = $1
group by pi.id,oi.id,p.id,pp.gst,od.offer_id,pp.price;`

const purchasedCourseQuery = `
select 
	cpl.id, cd.title, 
	1 quantity, coalesce(cpl.purchased_price,cd.price) total,
	pp.gst tax, coalesce(cpl.purchased_price,cd.price) price,
	cd.image image, cpl.offer_id,pp.price as original_price
from course_purchase_logs cpl
inner join course_details cd on cd.id = cpl.course_id
inner join product_prices pp on pp.course_id = cd.id and pp.currency = cpl.payment_currency
where cpl.id = $1;`

const purchasedMeetsQuery = `
select 
	cpl.id, cd.name title,
	1 quantity, coalesce(cpl.purchased_price,am.price) total,
	pp.gst tax, coalesce(cpl.purchased_price,am.price),
	cd.image image, cpl.offer_id,pp.price as original_price
from artist_meeting_logs cpl
inner join artist_meet_detail cd on cd.artist_meet_id = cpl.artist_meet_id
inner join artist_meets am on am.id = cpl.artist_meet_id
inner join product_prices pp on pp.meet_id = am.id and pp.currency = cpl.payment_currency
where cpl.id = $1;
`;

const purchasedPaidVideosQuery = `
select 
	upv.id, pv.title,
	1 quantity, coalesce(pt.amount,pv.price) total,
	pp.gst tax, coalesce(pt.amount,pv.price),upv.offer_id,
	pv.image image,pp.price as original_price
from user_paid_video_entries upv
inner join payment_transactions pt on pt.id = upv.txn_id
inner join paid_videos pv on pv.id = upv.video_id
inner join product_prices pp on pp.video_id = pv.id and pp.currency = pt.payment_currency
where upv.id = $1`;

const getAddressByIdQuery = `select 
coalesce(ua.email,u.email) email,
coalesce(ua.phone,u.mobile_number) phone,
u.id user_id,
ua.first_name,
ua.last_name,
ua.address_line_one,
ua.address_line_two,
ua.address_line_three,
ua.city,
ua.state,
ua.pincode,
ua.country
from users u
left join user_address ua on ua.user_id = u.id
where ua.id = $1`;

// const usersQuery = `select * from users where id = $1`


const orderDetailByIdQuery = `select (SELECT COUNT(*) FROM order_details WHERE payment_status = 3) AS "orderNumber",od.created date,u.email email,
od.total_items total,p.price "subTotal",p.tax,
pm.name as "paymentMethod",
'Free Shipping' shipping,
p.id product_id,
pms.name payment_status,
os.name order_status,
od.address_id,
od.invoice_url,
pc.id currency_id,
pc.name currency_name,
pc.symbol currency_symbol,
p.is_cancellable,
od.refund_status
from order_details od
inner join order_items oi on oi.order_id = od.id
inner join product_item pi on pi.id = oi.product_item_id
inner join products p on p.id = pi.product_id
inner join users u on u.id = od.user_id
inner join payment_method pm on pm.id = od.payment_method
inner join payment_currency pc on pc.id = od.payment_currency
inner join payment_status pms on pms.id = od.payment_status
inner join order_status os on os.id = od.ordeR_status
where od.id = $1 and od.user_id = $2
group by od.id,u.id,p.id,pm.id,pms.id,os.id,pc.id;`

const courseOrderDetailQuery = `select 
cpl.id "orderNumber",cpl.created date,u.email,
pm.name "paymentMethod",
cd.id product_id,ps.name payment_status,
cpl.address_id,
cpl.invoice invoice_url,
case when (ps.id = any(array[1,2])) then 'PENDING'
when (ps.id = any(array[3])) then 'PURCHASED'
else 'FAILED' end order_status,
pc.id currency_id,
pc.name currency_name,
pc.symbol currency_symbol,
false is_cancellable,
null refund_status
from course_purchase_logs cpl
inner join users u on u.id = cpl.user_id
inner join payment_status ps on ps.id = cpl.payment_status
inner join payment_method pm on pm.id = cpl.payment_method
inner join payment_currency pc on pc.id = cpl.payment_currency
inner join course_details cd on cd.id = cpl.course_id
where cpl.id = $1 and cpl.user_id = $2;`

const updateCourseInvoiceQuery = `update course_purchase_logs
set invoice = $1,
updated = now()
where id = $2;`

const meetOrderDetailQuery = `select 
aml.id "orderNumber",aml.created date,u.email,
pm.name "paymentMethod",
amd.artist_meet_id product_id,ps.name payment_status,
aml.address_id,
aml.invoice invoice_url,
case when (ps.id = any(array[1,2])) then 'PENDING'
when (ps.id = any(array[3])) then 'PURCHASED'
else 'FAILED' end order_status,
pc.id currency_id,
pc.name currency_name,
pc.symbol currency_symbol,
false is_cancellable,
null refund_status
from artist_meeting_logs aml
inner join users u on u.id = aml.user_id
inner join payment_status ps on ps.id = aml.payment_status
inner join payment_method pm on pm.id = aml.payment_method
inner join payment_currency pc on pc.id = aml.payment_currency
inner join artist_meet_detail amd on amd.artist_meet_id = aml.artist_meet_id
where aml.id = $1 and aml.user_id = $2;`


const updateMeetInvoiceQuery = `update artist_meeting_logs
set invoice = $1,
updated = now()
where id = $2;`

const paidVideoDetailQuery = `select 
upv.id "orderNumber",upv.created date,u.email,
pm.name "paymentMethod",
pv.id product_id,ps.name payment_status,
upv.address_id,
upv.invoice invoice_url,
case when (ps.id = any(array[1,2])) then 'PENDING'
when (ps.id = any(array[3])) then 'PURCHASED'
else 'FAILED' end order_status,
pc.id currency_id,
pc.name currency_name,
pc.symbol currency_symbol,
false is_cancellable,
null refund_status
from user_paid_video_entries upv
inner join users u on u.id = upv.user_id
inner join payment_transactions pt on pt.id = upv.txn_id
inner join payment_status ps on ps.id = pt.payment_status
inner join paid_videos pv on pv.id = upv.video_id
inner join payment_method pm on pm.id = pt.payment_method
inner join payment_currency pc on pc.id = pt.payment_currency
where upv.id = $1 and upv.user_id = $2;`


const updatePaidVidInvoiceQuery = `update user_paid_video_entries
set invoice = $1,
updated = now()
where id = $2;`

const updateProductsInvoiceQuery = `update order_details
set invoice_url = $1,
updated = now()
where id = $2;`;

export const getPurchasedProducts = async (orderNumber: number, type: string) => {
    try {
        let products: any[] = [];
        if (type == "PRODUCT") {
            const pdtVariants = await query(productVariantsQuery, [orderNumber]);
            products = pdtVariants.rows
        } else if (type == 'COURSE') {
            const pdtVariants = await query(purchasedCourseQuery, [orderNumber]);
            products = pdtVariants.rows
        } else if (type == 'MEET') {
            const pdtVariants = await query(purchasedMeetsQuery, [orderNumber]);
            products = pdtVariants.rows
        } else if (type == 'PAID_VIDEO') {
            const pdtVariants = await query(purchasedPaidVideosQuery, [orderNumber]);
            products = pdtVariants.rows
        }

        const productsWithSubtotal = products.map((product) => {
            const priceWithoutTax = (product.total / (1 + (product.tax / 100))).toFixed(2);
            return { ...product, subtotal: priceWithoutTax };
        });

        return productsWithSubtotal
    } catch (error) {
        console.error("Error on getting purchased products : ", error)
        return Promise.reject({ status: false, message: "Error on getting purchased products" })
    }
}

export const getPurchaseAddress = async (type: string, uid: number, addressId?: number) => {
    try {
        // if (type == 'PRODUCT') {
        const addressRows = await query(getAddressByIdQuery, [addressId]);
        let address: any = addressRows.rowCount ? addressRows.rows[0] : null
        return address;
        // } else {
        //     const users = await query(usersQuery, [uid]);
        //     return {
        //         email: users.rows[0].email,
        //         phone: users.rows[0].mobile_number,
        //         first_name: users.rows[0].display_name,
        //         user_id: uid,
        //         last_name: null,
        //         address_line_one: null,
        //         address_line_two: null,
        //         address_line_three: null,
        //         city: null,
        //         state: null,
        //         pincode: null,
        //         country: null
        //     }
        // }
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on getting address" })
    }
}

export const getOrderDetail = async (order_item_id: number, uId: number, type: string) => {
    try {
        let products: any = null;
        if (type == "PRODUCT") {
            const pdtVariants = await query(orderDetailByIdQuery, [order_item_id, uId]);
            products = pdtVariants.rows[0]
        } else if (type == 'COURSE') {
            const pdtVariants = await query(courseOrderDetailQuery, [order_item_id, uId]);
            products = pdtVariants.rows[0]
        } else if (type == 'MEET') {
            const pdtVariants = await query(meetOrderDetailQuery, [order_item_id, uId]);
            products = pdtVariants.rows[0]
        } else if (type == 'PAID_VIDEO') {
            const pdtVariants = await query(paidVideoDetailQuery, [order_item_id, uId]);
            products = pdtVariants.rows[0]
        }

        if (!products) {
            return Promise.reject({ status: false, message: "Invalid Order Id" })
        }

        if (!products.invoice_url && products.payment_status == PHONE_PE_STATUS.PAYMENT_SUCCESS.name) {
            const invoice = await generateInvoice(order_item_id, uId, type, false)

            if (type == 'COURSE' && invoice) {
                await query(updateCourseInvoiceQuery, [invoice, order_item_id]);
            } else if (type == 'MEET' && invoice) {
                await query(updateMeetInvoiceQuery, [invoice, order_item_id]);
            } else if (type == 'PAID_VIDEO' && invoice) {
                await query(updatePaidVidInvoiceQuery, [invoice, order_item_id]);
            } else if (type == 'PRODUCT' && invoice) {
                await query(updateProductsInvoiceQuery, [invoice, order_item_id]);
            }

            products.invoice_url = invoice;
        }



        return products
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on getting order detail" })
    }
}

export const generateInvoice = async (id: number, uId: number, type: string, sendMail: boolean = true, meetLink?: string) => {
    try {
        let order: any;
        let subject;
        let body;
        // let invoice_id = await query(getTotalSuccessfulTransactionsForInvoiceNumber, []);
        if (type == 'COURSE') {
            const pdtVariants = await query(courseOrderQuery, [id]);
            // order = { id: invoice_id.rows[0].id, ...pdtVariants.rows[0] };
            order = pdtVariants.rows[0];
        } else if (type == 'MEET') {
            const pdtVariants = await query(meetOrderDetailPdfQuery, [id]);
            order = pdtVariants.rows[0];
            // order = { id: invoice_id.rows[0].id, ...pdtVariants.rows[0] };
        } else if (type == 'PAID_VIDEO') {
            const pdtVariants = await query(paidVidOrderQuery, [id]);
            order = pdtVariants.rows[0];
            // order = { id: invoice_id.rows[0].id, ...pdtVariants.rows[0] };
        } else if (type == 'PRODUCT') {
            const pdtVariants = await query(orderInvoiceDetailByIdQuery, [id]);
            order = pdtVariants.rows[0];
            // order = {  id: Number(invoice_id.rows[0].id) + 1, ...pdtVariants.rows[0] };
        }

        if (!order) {
            throw new Error(`Order not found for type: ${type}, id: ${id}`);
        }

        let customerDetail;
        if (order.address_id) {
            customerDetail = await query(userAddressByIdQuery, [order.address_id]);
        } else {
            customerDetail = await query(userDetailAddressAltQuery, [uId])
        }

        if (type == 'MEET') {
            subject = MeetInvoiceEmail.subject;
            const data = {
                customerName: customerDetail.rows[0].firstName + ' ' + customerDetail.rows[0].lastName,
                address: {
                    line1: customerDetail.rows[0].address.address_line_one,
                    line2: customerDetail.rows[0].address.address_line_two,
                    line3: customerDetail.rows[0].address.address_line_three,
                    city: customerDetail.rows[0].address.city,
                    state: customerDetail.rows[0].address.state,
                    pincode: customerDetail.rows[0].address.pincode
                },
            }

            body = MeetInvoiceEmail.body(data, "60 minutes", meetLink!, order.slot_date, order.slot_time);
            if(order.meetId == 9) {
                subject = BGMeetInvoiceEmail.subject;
                body = BGMeetInvoiceEmail.body(data, order.slot_date, order.slot_time);
            } 
             else if (order.meetId == 10) {
                subject = BGMeetInvoiceEmail.subject;
                body = BGMeetInvoiceEmail.body1(data);
            }
            else if (order.meetId == 11) {
                const isIndian = order.symbol === '₹';
                const timing = isIndian 
                    ? "Tuesdays & Thursdays, 6:30 – 8:00 AM IST" 
                    : "Wednesday and Fridays : 1:00 - 2:30 AM GMT";

                subject = MeetInvoiceEmailPGOffline.subject,
                body = MeetInvoiceEmailPGOffline.body(data.customerName, timing)
            }
        }

        const invoice = await generatePDF(order, customerDetail.rows[0], "Placed", sendMail, subject, body, type);

        return invoice
    } catch (error) {
        console.error("Error on generating invoice : ", error)
        return ''
    }
}

export const getProductsByType = async (id: number, type: string, userId: number) => {
    try {
        let products: any[] = [];
        if (type == "PRODUCT") {
            const pdtVariants = await query(getPdtsQuery, [id, userId]);
            products = pdtVariants.rows
        } else if (type == 'COURSE') {
            const pdtVariants = await query(getCourseQuery, [id, userId]);
            products = pdtVariants.rows
        } else if (type == 'MEET') {
            const pdtVariants = await query(getMeetQuery, [id, userId]);
            products = pdtVariants.rows
        } else if (type == 'PAID_VIDEO') {
            const pdtVariants = await query(getVideoQuery, [id, userId]);
            products = pdtVariants.rows
        }

        return products;
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on getting product by id and type" })
    }
}

export const updateWishlist = async (id: number, type: string, userId: number, wishlistId?: number) => {
    try {
        let wishlist;
        if (wishlistId) {
            wishlist = await query(deleteWishlistQuery, [wishlistId]);
        } else {
            wishlist = await query(addWishListQuery, [type == 'PRODUCT' ? id : null, type == 'MEET' ? id : null, type == 'COURSE' ? id : null, type == 'PAID_VIDEO' ? id : null, userId]);
        }
        return wishlist;
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on updating wishlist" })
    }
}

//Admin Function

const orderDetailByIdAdminQuery = `select od.id "orderNumber",od.created date,u.email email,
od.total_items total,p.price "subTotal",p.tax,
pm.name as "paymentMethod",
'Free Shipping' shipping,
p.id product_id,
pms.name payment_status,
os.name order_status,
od.address_id,
od.invoice_url,
od.user_id
from order_details od
inner join order_items oi on oi.order_id = od.id
inner join product_item pi on pi.id = oi.product_item_id
inner join products p on p.id = pi.product_id
inner join users u on u.id = od.user_id
inner join payment_method pm on pm.id = od.payment_method
inner join payment_status pms on pms.id = od.payment_status
inner join order_status os on os.id = od.ordeR_status
where od.id = $1
group by od.id,u.id,p.id,pm.id,pms.id,os.id;`

const courseOrderDetailAdminQuery = `select 
cpl.id "orderNumber",cpl.created date,u.email,
'PhonePe' "paymentMethod",
cd.id product_id,ps.name payment_status,
null address_id,
cpl.invoice invoice_url,
case when (ps.id = any(array[1,2])) then 'PENDING'
when (ps.id = any(array[3])) then 'PURCHASED'
else 'FAILED' end order_status,
cpl.user_id
from course_purchase_logs cpl
inner join users u on u.id = cpl.user_id
inner join payment_status ps on ps.id = cpl.payment_status
inner join course_details cd on cd.id = cpl.course_id
where cpl.id = $1;`

const meetOrderDetailAdminQuery = `select 
aml.id "orderNumber",aml.created date,u.email,
'PhonePe' "paymentMethod",
amd.artist_meet_id product_id,ps.name payment_status,
null address_id,
aml.invoice invoice_url,
case when (ps.id = any(array[1,2])) then 'PENDING'
when (ps.id = any(array[3])) then 'PURCHASED'
else 'FAILED' end order_status,
aml.user_id
from artist_meeting_logs aml
inner join users u on u.id = aml.user_id
inner join payment_status ps on ps.id = aml.payment_status
inner join artist_meet_detail amd on amd.artist_meet_id = aml.artist_meet_id
where aml.id = $1;`

const paidVideoDetailAdminQuery = `select 
upv.id "orderNumber",upv.created date,u.email,
'PhonePe' "paymentMethod",
pv.id product_id,ps.name payment_status,
null address_id,
upv.invoice invoice_url,
case when (ps.id = any(array[1,2])) then 'PENDING'
when (ps.id = any(array[3])) then 'PURCHASED'
else 'FAILED' end order_status,
upv.user_id
from user_paid_video_entries upv
inner join users u on u.id = upv.user_id
inner join payment_transactions pt on pt.id = upv.txn_id
inner join payment_status ps on ps.id = pt.payment_status
inner join paid_videos pv on pv.id = upv.video_id
where upv.id = $1;`


export const getOrderDetailAdmin = async (order_item_id: number, type: string) => {
    try {
        let products: any = null;
        if (type == "PRODUCT") {
            const pdtVariants = await query(orderDetailByIdAdminQuery, [order_item_id]);
            products = pdtVariants.rows[0]
        } else if (type == 'COURSE') {
            const pdtVariants = await query(courseOrderDetailAdminQuery, [order_item_id]);
            products = pdtVariants.rows[0]
        } else if (type == 'MEET') {
            const pdtVariants = await query(meetOrderDetailAdminQuery, [order_item_id]);
            products = pdtVariants.rows[0]
        } else if (type == 'PAID_VIDEO') {
            const pdtVariants = await query(paidVideoDetailAdminQuery, [order_item_id]);
            products = pdtVariants.rows[0]
        }

        if (!products) {
            return Promise.reject({ status: false, message: "Invalid Order Id" })
        }

        if (!products.invoice_url && products.payment_status == PHONE_PE_STATUS.PAYMENT_SUCCESS.name && products.user_id) {
            const invoice = await generateInvoice(order_item_id, products.user_id, type, false)

            if (type == 'COURSE' && invoice) {
                await query(updateCourseInvoiceQuery, [invoice, order_item_id]);
            } else if (type == 'MEET' && invoice) {
                await query(updateMeetInvoiceQuery, [invoice, order_item_id]);
            } else if (type == 'PAID_VIDEO' && invoice) {
                await query(updatePaidVidInvoiceQuery, [invoice, order_item_id]);
            }

            products.invoice_url = invoice;
        }



        return products
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on getting order detail" })
    }
}

export const getOfferData = async (code: string, meet_id?: number, course_id?: number, video_id?: number, product_id?: number) => {
    try {
        const data = await query(getOfferQuery, [code, true]);

        if (!data.rowCount && (meet_id || course_id || video_id || product_id)) {
            const data = await query(getOfferQuery, [code, false]);
            if (!data.rowCount) {
                return Promise.reject({ status: false, message: "Coupon not found" })
            }
            const pdtCpnF = await query(getProductOfferQuery, [data.rows[0].id, meet_id, course_id, video_id, product_id]);
            if (!pdtCpnF.rowCount) {
                return Promise.reject({ status: false, message: "Coupon not enabled for this product" })
            }
            return data.rows
        }

        return data.rowCount ? data.rows : []
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on getting offer detail" })
    }
}

export const validateOfferData = async (discountData: any, userId: number) => {
    try {
        const countData = await query(offerUsedCountQuery, [userId, discountData.id]);

        if (!discountData.unlimited) {
            if (countData.rowCount) {
                if (countData.rows[0].total_count >= discountData.total_limit) {
                    return Promise.reject({
                        status: false,
                        message: `Invalid Coupon`
                    })
                }
            }
        }

        if (discountData.user_limited) {
            if (countData.rows[0].user_count >= discountData.user_limit) {
                return Promise.reject({
                    status: false,
                    message: `Invalid Coupon`
                })
            }
        }

        return true;
    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Error on coupon validate`
        })
    }
}