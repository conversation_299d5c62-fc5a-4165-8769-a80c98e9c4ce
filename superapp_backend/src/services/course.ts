import { PHONE_PE_STATUS } from "../common/constants";
import { query } from "../db";
import { paymentStatusCheck } from "./pg";
import { generateInvoice } from "./orders";
import { getCourseDetailQuery, insertUserCourseQuery, updatePurchaseStatusQuery } from "../common/db_constants";
import logger from "../logger";

const coursePurchasesQuery = `select 
id,merchant_transaction_id,user_id,course_id,
(select validity from course_details cd where cd.id = course_id) validity
from course_purchase_logs 
where created < now() - interval '10 mins' 
and payment_status in (1,2) 
and merchant_transaction_id is not null limit 1;`

export const processCoursePurchases = async () => {
    try {
        const timeoutOrdersRows = await query(coursePurchasesQuery, []);
        const timeoutOrders = timeoutOrdersRows.rows;
        if (timeoutOrders.length) {
            for (let i = 0; i < timeoutOrders.length; i++) {
                const paymentStatus = await paymentStatusCheck(timeoutOrders[i].merchant_transaction_id);

                if (paymentStatus.code == PHONE_PE_STATUS.PAYMENT_SUCCESS.name) {
                    const invoice = await generateInvoice(timeoutOrders[i].id, timeoutOrders[i].user_id, 'COURSE')

                    let orderDetail = await query(updatePurchaseStatusQuery, [PHONE_PE_STATUS.PAYMENT_SUCCESS.id, timeoutOrders[i].merchant_transaction_id, invoice]);

                    if (!orderDetail.rowCount) {
                        logger.error("Course Purchase Id not found & updated");
                        return;
                    }

                    const course = await query(getCourseDetailQuery, [timeoutOrders[i].course_id, timeoutOrders[i].user_id]);

                    if (!course.rowCount) {
                        logger.error("Course Id not found");
                        return;
                    }

                    const userCourse = await query(insertUserCourseQuery, [timeoutOrders[i].course_id, timeoutOrders[i].user_id, timeoutOrders[i].validity]);

                    if (!userCourse.rowCount) {
                        logger.error("User course not created");
                        return;
                    }

                    return
                } else if (paymentStatus.code == PHONE_PE_STATUS.TIMED_OUT.name || paymentStatus.code == PHONE_PE_STATUS.PAYMENT_ERROR.name || paymentStatus.code == PHONE_PE_STATUS.PAYMENT_DECLINED.name) {
                    let orderDetail = await query(updatePurchaseStatusQuery, [PHONE_PE_STATUS.PAYMENT_ERROR.id, timeoutOrders[i].merchant_transaction_id, null]);
                    logger.log("Upadted Payment Count : ", orderDetail.rowCount);
                    return;
                }
            }
            return;
        }
    } catch (error) {
        logger.error(`Error on updating payment status : ${error}`);
        return
    }
}