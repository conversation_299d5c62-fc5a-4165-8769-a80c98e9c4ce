import { query } from "../db";
import excelJS from "exceljs";
import { sendEmail } from "./mailer";
import logger from "../logger";
import { formatRegex, invalidArray } from "../common/constants";
import { handleResponse } from "../common";

let emailString = process.env.REPORT_TO_MAIL as unknown as string;
let email: string[] = emailString.split(",");

export const karokeReportQuery = `
    select 
	kr.id songid, 
 	coalesce(us.email,us.mobile_number) user_name,
	ks.name song_name,
	ar.name artist_name,
	kr.url song_url,
    TO_CHAR(kr.created at time zone 'asia/kolkata','Mon DD YYYY') song_created
from karaoke_recordings kr
inner join karaoke_songs ks on ks.id = kr.song_id
inner join users us on us.id = kr.user_id
inner join artist ar on ar.id = ks.artist_id
WHERE (kr.created between ($1 at time zone 'asia/kolkata') and (($2 at time zone 'asia/kolkata') + interval '1 day' - interval '1 second') OR $2 IS NUll)
AND (ar.id = $3 or $3 is null)
ORDER BY kr.created desc
    `;

const userReportQuery = `select u.id,coalesce(email,mobile_number) user_name,u.created date_created,email,mobile_number,c.name platform,lm.name login_method from users u
inner join login_methods lm on lm.id = u.login_method
inner join clients c on c.id = u.client_id
where (u.created between ($1 at time zone 'asia/kolkata') and (($2 at time zone 'asia/kolkata') + interval '1 day' - interval '1 second') OR $1 IS NUll)
AND (lm.id = $3 or $3 is null) AND (c.id = $4 or $4 is null)
order by u.created desc`

const reportQuery = `select 
od.id,
row_number() OVER() rn,
coalesce(u.display_name,ua.first_name || ' ' || ua.last_name) customer_name,
coalesce(u.email,ua.email) customer_email,
coalesce(u.mobile_number,ua.phone) customer_phone,
od.id billing_cust_name,
ua.first_name || ' ' || ua.last_name shpng_customer_name,
null shpng_company,
ua.phone shpng_phone,
ua.address_line_one,
ua.address_line_two,
ua.address_line_three,
ua.city,
ua.pincode,
ua.state,
ua.country,
p.name product_name,
'Online' payment_mode,
'Awaiting' accept_status,
'Order Placed' order_status,
CONCAT(STRING_AGG(pi.sku || ',' || oi.quantity, ';'), ';') sku,
null total_items
from order_details od
inner join users u on u.id = od.user_id
inner join user_address ua on ua.id = od.address_id
inner join order_status os on os.id = od.order_status
inner join order_items oi on oi.order_id = od.id
inner join product_item pi on pi.id = oi.product_item_id
inner join products p on p.id = pi.product_id
where od.is_reported = false and od.payment_status = 3
group by u.id,ua.id,od.id,os.id`;


const reportDwnldQuery = `select 
od.id,
row_number() OVER() rn,
coalesce(u.display_name,ua.first_name || ' ' || ua.last_name) customer_name,
coalesce(u.email,ua.email) customer_email,
coalesce(u.mobile_number,ua.phone) customer_phone,
od.id billing_cust_name,
ua.first_name || ' ' || ua.last_name shpng_customer_name,
null shpng_company,
ua.phone shpng_phone,
ua.address_line_one,
ua.address_line_two,
ua.address_line_three,
ua.city,
ua.pincode,
ua.state,
ua.country,
p.name product_name,
'Online' payment_mode,
'Awaiting' accept_status,
'Order Placed' order_status,
CONCAT(STRING_AGG(pi.sku || ',' || oi.quantity, ';'), ';') sku,
null total_items,
od.created
from order_details od
inner join users u on u.id = od.user_id
inner join user_address ua on ua.id = od.address_id
inner join order_status os on os.id = od.order_status
inner join order_items oi on oi.order_id = od.id
inner join product_item pi on pi.id = oi.product_item_id
inner join products p on p.id = pi.product_id
where (od.payment_status = 3) and (od.created between ($1 at time zone 'asia/kolkata') and (($2 at time zone 'asia/kolkata') + interval '1 day' - interval '1 second') OR $2 IS NUll)
AND (p.artist_id = $3 or $3 is null)
group by u.id,ua.id,od.id,os.id,p.id
order by od.created desc`;

const updateReportedQuery = `update order_details
set is_reported = true,
updated = now()
where id = any($1);`;

const BILLING_DETAILS = {
    company: 'METASTAR MEDIA PRIVATE LIMITED',
    phone: '8082627587',
    address1: '302, KUMAR PLAZA, KALINA KURLA ROAD',
    address2: 'NEAR KALINA MASJID, 3RD FLOOR, OFFICE, SANTACRUZ EAST',
    address3: '',
    landmark: '',
    city: 'Mumbai',
    picode: '400029',
    state: 'Maharashtra',
    country: 'India'
}

const coursePurchaseReportQuery = `select 
    cpl.id,
	row_number() OVER() rn,
	coalesce(u.display_name,u.email,u.mobile_number) customer_name,
	u.email customer_email,
	u.mobile_number customer_phone,
	cpl.id billing_cust_name,
	null shpng_customer_name,
	null shpng_company,
	null shpng_phone,
	null address_line_one,
	null address_line_two,
	null address_line_three,
	null city,
	null pincode,
	null state,
	null country,
    cd.title product_name,
	'Online' payment_mode,
	'Awaiting' accept_status,
	'Order Placed' order_status,
	cd.sku sku,
	null total_items,
    cpl.created
    from course_purchase_logs cpl
    inner join payment_status ps on ps.id = cpl.payment_status
    inner join course_details cd on cd.id = cpl.course_id
	INNER JOIN users u on u.id = cpl.user_id
    left join user_address ua on ua.id = cpl.address_id
	where cpl.payment_status = 3 and (cpl.created between ($1 at time zone 'asia/kolkata') and (($2 at time zone 'asia/kolkata') + interval '1 day' - interval '1 second') OR $2 IS NUll)
    AND (cd.artist_id = $3 or $3 is null) 
    `

const meetPurchaseQuery = `select 
	aml.id,
	row_number() OVER() rn,
	coalesce(u.display_name,ua.first_name || ' ' || ua.last_name) customer_name,
    coalesce(u.email,ua.email) customer_email,
    coalesce(u.mobile_number,ua.phone) customer_phone,
    aml.id billing_cust_name,
    ua.first_name || ' ' || ua.last_name shpng_customer_name,
    null shpng_company,
    ua.phone shpng_phone,
    ua.address_line_one,
    ua.address_line_two,
    ua.address_line_three,
    ua.city,
    ua.pincode,
    ua.state,
    ua.country,
    'Meet ' || amd.name product_name,
	'Online' payment_mode,
	'Awaiting' accept_status,
	'Order Placed' order_status,
	amd.sku sku,
	null total_items,
    aml.created
    from artist_meeting_logs aml
    inner join payment_status ps on ps.id = aml.payment_status
    left join user_address ua on ua.id = aml.address_id
    inner join artist_meet_detail amd on amd.artist_meet_id = aml.artist_meet_id
 	inner join artist_meets am on am.id = amd.artist_meet_id
	INNER JOIN users u on u.id = aml.user_id
	where (aml.payment_status = 3) and (aml.created between ($1 at time zone 'asia/kolkata') and (($2 at time zone 'asia/kolkata') + interval '1 day' - interval '1 second') OR $2 IS NUll)
    AND (am.artist_id = $3 or $3 is null)
    order by aml.created desc
`

const videoPurchaseQuery = `select 
	upv.id,
	row_number() OVER() rn,
	coalesce(u.display_name,ua.first_name || ' ' || ua.last_name) customer_name,
    coalesce(u.email,ua.email) customer_email,
    coalesce(u.mobile_number,ua.phone) customer_phone,
    upv.id billing_cust_name,
    ua.first_name || ' ' || ua.last_name shpng_customer_name,
    null shpng_company,
    ua.phone shpng_phone,
    ua.address_line_one,
    ua.address_line_two,
    ua.address_line_three,
    ua.city,
    ua.pincode,
    ua.state,
    ua.country,
    pv.title product_name,
	'Online' payment_mode,
	'Awaiting' accept_status,
	'Order Placed' order_status,
	pv.sku sku,
	null total_items,
    upv.created
    from user_paid_video_entries upv
    inner join payment_transactions pt on pt.id = upv.txn_id
    inner join payment_status ps on ps.id = pt.payment_status
    inner join paid_videos pv on pv.id = upv.video_id
	INNER JOIN users u on u.id = upv.user_id
    left join user_address ua on ua.id = upv.address_id
	where pt.payment_status = 3 and (upv.created between ($1 at time zone 'asia/kolkata') and (($2 at time zone 'asia/kolkata') + interval '1 day' - interval '1 second') OR $2 IS NUll)
    order by upv.created desc
`

const createReportExcel = async (report: any[]) => {
    try {
        let reportData = report.map(el => {
            return {
                ...el,
                b_company: BILLING_DETAILS.company,
                b_phone: BILLING_DETAILS.phone,
                b_address1: BILLING_DETAILS.address1,
                b_address2: BILLING_DETAILS.address2,
                b_address3: BILLING_DETAILS.address3,
                b_landmark: BILLING_DETAILS.landmark,
                b_city: BILLING_DETAILS.city,
                b_pincode: BILLING_DETAILS.picode,
                b_state: BILLING_DETAILS.state,
                b_counrty: BILLING_DETAILS.country,
                gstin: '',
                s_landmark: '',
                s_required: 'Yes',
                rush_order: 'No',
                invoice: 'No',
                offer_code: ''
            }
        })

        const workbook = new excelJS.Workbook();  // Create a new workbook
        const worksheet = workbook.addWorksheet("My Users"); // New Worksheet
        const path = "./src/assets";  // Path to download excel
        // Column for data in excel. key must match data key
        worksheet.columns = [
            { header: "Product Name", key: "product_name", width: 10 },
            { header: "Customer Name", key: "customer_name", width: 10 },
            { header: "Customer Email", key: "customer_email", width: 10 },
            { header: "Customer Phone", key: "customer_phone", width: 10 },
            { header: "Billing Customer Name", key: "billing_cust_name", width: 10 },
            { header: "Billing Company", key: "b_company", width: 10 },
            { header: "Billing Phone", key: "b_phone", width: 10 },
            { header: "Billing Address Line 1", key: "b_address1", width: 10 },
            { header: "Billing Address Line 2", key: "b_address2", width: 10 },
            { header: "Billing Address Line 3", key: "b_address3", width: 10 },
            { header: "Billing Landmark", key: "b_landmark", width: 10 },
            { header: "Billing City", key: "b_city", width: 10 },
            { header: "Billing Pincode", key: "b_pincode", width: 10 },
            { header: "Billing State", key: "b_state", width: 10 },
            { header: "Billing Country", key: "b_counrty", width: 10 },
            { header: "GSTIN", key: "gstin", width: 10 },
            { header: "Shipping Customer Name", key: "shpng_customer_name", width: 10 },
            { header: "Shipping Company", key: "shpng_company", width: 10 },
            { header: "Shipping Phone", key: "shpng_phone", width: 10 },
            { header: "Shipping Address Line 1", key: "address_line_one", width: 10 },
            { header: "Shipping Address Line 2", key: "address_line_two", width: 10 },
            { header: "Shipping Address Line 3", key: "address_line_three", width: 10 },
            { header: "Shipping Landmark", key: "s_landmark", width: 10 },
            { header: "Shipping City", key: "city", width: 10 },
            { header: "Shipping Pincode", key: "pincode", width: 10 },
            { header: "Shipping State", key: "state", width: 10 },
            { header: "Shipping Country", key: "country", width: 10 },
            { header: "Shipping Required", key: "s_required", width: 10 },
            { header: "Rush Order", key: "rush_order", width: 10 },
            { header: "Payment Mode", key: "payment_mode", width: 10 },
            { header: "Generate Invoice", key: "invoice", width: 10 },
            { header: "Accept Status", key: "accept_status", width: 10 },
            { header: "Order Status", key: "order_status", width: 10 },
            { header: "Offer Code", key: "offer_code", width: 10 },
            { header: "Product SKU", key: "sku", width: 10 },
            { header: "Total", key: "total_items", width: 10 }
        ];
        // Looping through User data
        let counter = 1;
        reportData.forEach((user) => {
            user.rn = counter;
            worksheet.addRow(user); // Add data in worksheet
            counter++;
        });
        // Making first line in excel bold
        worksheet.getRow(1).eachCell((cell) => {
            cell.font = { bold: true };
        });

        const fPath = `${path}/orders.xlsx`

        await workbook.xlsx.writeFile(fPath)

        return fPath;
    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Error on creating Report Excel`
        })
    }
}

const createUserReportExcel = async (report: any[]) => {
    try {
        const workbook = new excelJS.Workbook();  // Create a new workbook
        const worksheet = workbook.addWorksheet("My Users"); // New Worksheet
        const path = "./src/assets";  // Path to download excel
        // Column for data in excel. key must match data key
        worksheet.columns = [
            { header: "User Id", key: "id", width: 10 },
            { header: "Username", key: "user_name", width: 10 },
            { header: "Date Created", key: "date_created", width: 10 },
            { header: "Email", key: "email", width: 10 },
            { header: "Mobile", key: "mobile_number", width: 10 },
            { header: "Platform", key: "platform", width: 10 },
            { header: "Login Method", key: "login_method", width: 10 },
        ];
        // Looping through User data
        let counter = 1;
        report.forEach((user) => {
            user.rn = counter;
            worksheet.addRow(user); // Add data in worksheet
            counter++;
        });
        // Making first line in excel bold
        worksheet.getRow(1).eachCell((cell) => {
            cell.font = { bold: true };
        });

        const fPath = `${path}/users.xlsx`

        await workbook.xlsx.writeFile(fPath)

        return fPath;
    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Error on creating Report Excel`
        })
    }
}

const createKarokeReportExcel = async (report: any[]) => {
    try {
        const workbook = new excelJS.Workbook();  // Create a new workbook
        const worksheet = workbook.addWorksheet("My Users"); // New Worksheet
        const path = "./src/assets";  // Path to download excel
        // Column for data in excel. key must match data key
        worksheet.columns = [
            { header: "Song Id", key: "songid", width: 10 },
            { header: "Username", key: "user_name", width: 10 },
            { header: "Song Name", key: "song_name", width: 10 },
            { header: "Artist Name", key: "artist_name", width: 10 },
            { header: "Song Url", key: "song_url", width: 10 },
            { header: "Song Created", key: "song_created", width: 10 }
        ];
        // Looping through User data
        let counter = 1;
        report.forEach((user) => {
            user.rn = counter;
            worksheet.addRow(user); // Add data in worksheet
            counter++;
        });
        // Making first line in excel bold
        worksheet.getRow(1).eachCell((cell) => {
            cell.font = { bold: true };
        });

        const fPath = `${path}/karoke.xlsx`

        await workbook.xlsx.writeFile(fPath)

        return fPath;
    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Error on creating Report Excel`
        })
    }
}

const createPdtWiseReportExcel = async (report: any[]) => {
    try {
        const workbook = new excelJS.Workbook();  // Create a new workbook
        const worksheet = workbook.addWorksheet("My Users"); // New Worksheet
        const path = "./src/assets";  // Path to download excel
        // Column for data in excel. key must match data key
        worksheet.columns = [
            { header: "Product Id", key: "id", width: 10 },
            { header: "Product Name", key: "product_name", width: 10 },
            { header: "Items Sold", key: "total_items_sold", width: 10 },
            { header: "SKU", key: "sku", width: 10 },
            { header: "Sub Total", key: "sub_total", width: 10 },
            { header: "Tax", key: "tax", width: 10 },
            { header: "Total", key: "total", width: 10 }
        ];
        // Looping through User data
        let counter = 1;
        report.forEach((user) => {
            user.rn = counter;
            worksheet.addRow(user); // Add data in worksheet
            counter++;
        });
        // Making first line in excel bold
        worksheet.getRow(1).eachCell((cell) => {
            cell.font = { bold: true };
        });

        const fPath = `${path}/ProductSales.xlsx`

        await workbook.xlsx.writeFile(fPath)

        return fPath;
    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Error on creating Report Excel`
        })
    }
}

export const getOrderReport = async (req: any, res: any) => {
    try {
        const report = await query(reportQuery, []);

        if (!report.rowCount) {
            res.send('Not Data Found');
        }

        const path = await createReportExcel(report.rows);

        await query(updateReportedQuery, [report.rows.map(el => { return el.id })]);

        res.download(path);
    } catch (error) {
        res.send({
            status: "error",
            message: "Something went wrong",
            error
        });
    }
}

export const getUserFilter = async (req: any, res: any) => {
    let response = {};
    try {
        const loginTypes = await query('select * from login_methods', [])
        const clients = await query('select * from clients', [])

        response = {
            status: true,
            message: `Success`,
            data: {
                loginTypes: loginTypes.rows,
                clients: clients.rows
            }
        }
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request Failed`
        }
        return handleResponse(res, response, 500);
    }
}

export const getUserReport = async (req: any, res: any) => {
    let response = {};
    let { fromDate, toDate, clientId, loginType } = req.query;


    if ((fromDate && toDate) && (!formatRegex.test(fromDate) || !formatRegex.test(toDate))) {
        response = {
            status: false,
            message: `Invalid Date Format`
        }
        return handleResponse(res, response, 400);
    }
    else if ((fromDate && !toDate) || (toDate && !fromDate)) {
        response = {
            status: false,
            message: `Invalid Date Provided`
        }
        return handleResponse(res, response, 400);
    }
    try {
        const report = await query(userReportQuery, [!invalidArray.includes(fromDate) ? fromDate : null, !invalidArray.includes(toDate) ? toDate : null, loginType ? loginType : null, clientId ? clientId : null]);

        if (!report.rowCount) {
            res.send('Not Data Found');
        }

        const path = await createUserReportExcel(report.rows);

        res.download(path);
    } catch (error) {
        res.send({
            status: "error",
            message: "Something went wrong",
            error
        });
    }
}

export const getKaraokeReport = async (req: any, res: any) => {
    let response: any = {}
    let { fromDate, toDate, artistId } = req.query;

    if ((fromDate && toDate) && (!formatRegex.test(fromDate) || !formatRegex.test(toDate))) {
        response = {
            status: false,
            message: `Invalid Date Format`
        }
        return handleResponse(res, response, 400);
    }
    else if ((fromDate && !toDate) || (toDate && !fromDate)) {
        response = {
            status: false,
            message: `Invalid Date Provided`
        }
        return handleResponse(res, response, 400);
    }

    try {
        const report = await query(karokeReportQuery, [!invalidArray.includes(fromDate) ? fromDate : null, !invalidArray.includes(toDate) ? toDate : null, !invalidArray.includes(artistId) ? artistId : null]);

        if (!report.rowCount) {
            res.send('Not Data Found');
        }

        const path = await createKarokeReportExcel(report.rows);

        res.download(path);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}

export const getOrderReportDwnl = async (req: any, res: any) => {
    let response: any = {}
    let { fromDate, toDate, artistId } = req.query;

    if ((fromDate && toDate) && (!formatRegex.test(fromDate) || !formatRegex.test(toDate))) {
        response = {
            status: false,
            message: `Invalid Date Format`
        }
        return handleResponse(res, response, 400);
    }
    else if ((fromDate && !toDate) || (toDate && !fromDate)) {
        response = {
            status: false,
            message: `Invalid Date Provided`
        }
        return handleResponse(res, response, 400);
    }

    try {
        const orderReport = await query(reportDwnldQuery, [!invalidArray.includes(fromDate) ? fromDate : null, !invalidArray.includes(toDate) ? toDate : null, !invalidArray.includes(artistId) ? artistId : null]);

        const courseReport = await query(coursePurchaseReportQuery, [!invalidArray.includes(fromDate) ? fromDate : null, !invalidArray.includes(toDate) ? toDate : null, !invalidArray.includes(artistId) ? artistId : null]);

        const meetReport = await query(meetPurchaseQuery, [!invalidArray.includes(fromDate) ? fromDate : null, !invalidArray.includes(toDate) ? toDate : null, !invalidArray.includes(artistId) ? artistId : null]);

        const videoReport = await query(videoPurchaseQuery, [!invalidArray.includes(fromDate) ? fromDate : null, !invalidArray.includes(toDate) ? toDate : null]);

        if (!orderReport.rowCount && !courseReport.rowCount && !meetReport.rowCount && !videoReport.rowCount) {
            response = {
                status: false,
                message: `No Data Found`
            }
            return handleResponse(res, response, 400);
        }

        const report = [...orderReport.rows, ...courseReport.rows, ...meetReport.rows, ...videoReport.rows].sort((a, b) => a.date - b.date);

        const path = await createReportExcel(report);

        res.download(path);
    } catch (error) {
        res.send({
            status: "error",
            message: "Something went wrong",
            error
        });
    }
}


const reportQueryV2 = `select 
od.id,
row_number() OVER() rn,
coalesce(u.display_name,ua.first_name || ' ' || ua.last_name) customer_name,
coalesce(u.email,ua.email) customer_email,
coalesce(u.mobile_number,ua.phone) customer_phone,
od.id billing_cust_name,
ua.first_name || ' ' || ua.last_name shpng_customer_name,
null shpng_company,
ua.phone shpng_phone,
ua.address_line_one,
ua.address_line_two,
ua.address_line_three,
ua.city,
ua.pincode,
ua.state,
ua.country,
p.name product_name,
'Online' payment_mode,
'Awaiting' accept_status,
'Order Placed' order_status,
CONCAT(STRING_AGG(pi.sku || ',' || oi.quantity, ';'), ';') sku,
null total_items
from order_details od
inner join users u on u.id = od.user_id
inner join user_address ua on ua.id = od.address_id
inner join order_status os on os.id = od.order_status
inner join order_items oi on oi.order_id = od.id
inner join product_item pi on pi.id = oi.product_item_id
inner join products p on p.id = pi.product_id
where od.payment_status = 3 and od.created between timezone ('UTC',$1) and  timezone ('UTC',$2) 
group by u.id,ua.id,od.id,os.id,p.name`;

const pdtWIseReportQuery = `select 
pi.id,
pi.title product_name,
count(distinct od.id) total_items_sold,
CONCAT(STRING_AGG(pi.sku || ',' || oi.quantity, ';'), ';') sku,
sum(oi.purchased_price) total,
p.tax,
sum(oi.purchased_price) - ((sum(oi.purchased_price) * p.tax)/100)::numeric(12,2) sub_total
from order_details od
inner join users u on u.id = od.user_id
inner join user_address ua on ua.id = od.address_id
inner join order_status os on os.id = od.order_status
inner join order_items oi on oi.order_id = od.id
inner join product_item pi on pi.id = oi.product_item_id
inner join products p on p.id = pi.product_id
where od.payment_status = 3 and od.created between timezone ('UTC',$1) and  timezone ('UTC',$2) 
group by pi.id,p.tax`

const coursePdtWiseQuery = `select 
    cd.id,
	cd.title product_name,
	count(distinct cpl.id) total_items_sold,
	CONCAT(STRING_AGG(cd.sku || ',' || 1, ';'), ';') sku,
	sum(cd.price) total,
	cd.gst tax,
	sum(cd.price) - ((sum(cd.price) * cd.gst)/100)::numeric(12,2) sub_total
    from course_purchase_logs cpl
    inner join payment_status ps on ps.id = cpl.payment_status
    inner join course_details cd on cd.id = cpl.course_id
	INNER JOIN users u on u.id = cpl.user_id
	where cpl.payment_status = 3 and cpl.created between timezone ('UTC',$1) and  timezone ('UTC',$2) 
group by cd.id,cd.gst`

const meetPdtWiseQuery = `select 
	amd.id,
	amd.name product_name,
	count(distinct aml.id) total_items_sold,
	CONCAT(STRING_AGG(amd.sku || ',' || 1, ';'), ';') sku,
	sum(am.price) total,
	am.gst tax,
	sum(am.price) - ((sum(am.price) * am.gst)/100)::numeric(12,2) sub_total
    from artist_meeting_logs aml
    inner join payment_status ps on ps.id = aml.payment_status
    inner join artist_meet_detail amd on amd.artist_meet_id = aml.artist_meet_id
 	inner join artist_meets am on am.id = amd.artist_meet_id
	INNER JOIN users u on u.id = aml.user_id
	where aml.payment_status = 3 and aml.created between timezone ('UTC',$1) and  timezone ('UTC',$2)
group by amd.id,am.gst`

const videoPdtWiseQuery = `select 
	pv.id,
	pv.title product_name,
	count(distinct upv.id) total_items_sold,
	CONCAT(STRING_AGG(pv.sku || ',' || 1, ';'), ';') sku,
	sum(pv.price) total,
	pv.gst tax,
	sum(pv.price) - ((sum(pv.price) * pv.gst)/100)::numeric(12,2) sub_total
    from user_paid_video_entries upv
    inner join payment_transactions pt on pt.id = upv.txn_id
    inner join payment_status ps on ps.id = pt.payment_status
    inner join paid_videos pv on pv.id = upv.video_id
	INNER JOIN users u on u.id = upv.user_id
	where pt.payment_status = 3 and upv.created between timezone ('UTC',$1) and  timezone ('UTC',$2)
group by pv.id,pv.gst`


export const processReport = async (startDate: any, endDate: any) => {
    try {
        const orderReport = await query(reportQueryV2, [startDate, endDate]);

        const courseReport = await query(coursePurchaseReportQuery, [startDate, endDate, null]);

        const meetReport = await query(meetPurchaseQuery, [startDate, endDate, null]);

        const videoReport = await query(videoPurchaseQuery, [startDate, endDate]);

        const report = [...orderReport.rows, ...courseReport.rows, ...meetReport.rows, ...videoReport.rows];

        const sub = 'Purchase Report';
        let body = '';
        let attachments: any[] = [];
        if (report.length) {
            const path = await createReportExcel(report);
            attachments.push({
                filename: `Report${startDate}_${endDate}.xlsx`,
                path: path
            });
        } else {
            body = 'No Sales'
        }

        if (email) {
            await sendEmail(email, sub, body, undefined, attachments)
        } else {
            logger.error("Admin Email not found")
        }

        return;
    } catch (error) {
        logger.error("Error on process daily report")
        return;
    }
}

export const processPdtReport = async (startDate: any, endDate: any) => {
    try {
        const orderReport = await query(pdtWIseReportQuery, [startDate, endDate]);

        const courseReport = await query(coursePdtWiseQuery, [startDate, endDate]);

        const meetReport = await query(meetPdtWiseQuery, [startDate, endDate]);

        const videoReport = await query(videoPdtWiseQuery, [startDate, endDate]);

        const report = [...orderReport.rows, ...courseReport.rows, ...meetReport.rows, ...videoReport.rows];

        const sub = 'Purchase Report';
        let body = '';
        let attachments: any[] = [];
        if (report.length) {
            const path = await createPdtWiseReportExcel(report);
            attachments.push({
                filename: `ProductReport${startDate}_${endDate}.xlsx`,
                path: path
            });
        } else {
            body = 'No Sales'
        }

        if (email) {
            await sendEmail(email, sub, body, undefined, attachments)
        } else {
            logger.error("Admin Email not found")
        }

        return;
    } catch (error) {
        logger.error("Error on process daily report")
        return;
    }
}