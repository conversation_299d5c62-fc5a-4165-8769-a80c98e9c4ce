import { query } from "../db";
import { makeGetCall, makePostCall } from "../common";
import { MeetResp, RegisterResp, ResisterRoot, ScheduleMeetingBody } from "../types/sale_assist";
import { paymentStatusCheck } from "./pg";
import { PHONE_PE_STATUS } from "../common/constants";
import logger from "../logger";
import { getArtistMeetByArtIdQuery, getCldlyCheckoutByIDquery, updateCalendlyStatus, updateMeetScheduledQuery, updatePayStatusQuery } from "../common/db_constants";
import { generateInvoice } from "./orders";
import { cancelEvent } from "./calendlyV1";

const BASE_URL = `https://platform.saleassist.ai/source`;
const CAPTURE = `/capture`;
const apiKey = process.env.SA_KEY;
const apiSecret = process.env.SA_SECRET;
const referrer = `https://asas.com/21b9eb89-bae0-41a9-a2a2-e6316bcda6bf?`
const SCHEDULE = `/schedule_meeting_v2`

export const registerUser = async (sourceId: string, payLoad: ResisterRoot) => {
    try {
        const url = BASE_URL + '/' + sourceId + CAPTURE;
        const headers =
        {
            Accept: '',
            api_key: apiKey,
            api_secret: apiSecret,
            Referrer: referrer
        }

        const api: RegisterResp = await makePostCall(url, payLoad, { headers });
        return api
    } catch (error) {
        return Promise.reject({
            status: false,
            message: 'Error on calling register',
            error
        })
    }
}

export const getSlotsApi = async (formId: string, sourceId: string) => {
    try {
        const SLOTS = `/get_available_slots/${formId}/schedule?timezone=Asia/Calcutta`
        const url = BASE_URL + '/' + sourceId + SLOTS;
        const headers =
        {
            Accept: '',
            api_key: apiKey,
            api_secret: apiSecret,
            Referrer: referrer
        }

        const api: any = await makeGetCall(url, null, headers);
        return api
    } catch (error) {
        return Promise.reject({
            status: false,
            message: 'Error on calling getSlots',
            error
        })
    }
}

export const scheduleMeeting = async (payload: ScheduleMeetingBody, sourceId: string) => {
    const url = BASE_URL + '/' + sourceId + SCHEDULE;
    try {
        const headers =
        {
            Accept: '',
            api_key: apiKey,
            api_secret: apiSecret,
            Referrer: referrer
        }

        const api: MeetResp = await makePostCall(url, payload, { headers });
        return api
    } catch (error) {
        return Promise.reject({
            status: false,
            message: 'Error on calling scheduleMeeting',
            error
        })
    }
}

const meetOrdersQuery = `select id,merchant_transaction_id,slot_date,slot_time,form_id,artist_meet_id,user_id from artist_meeting_logs where (created < now() - interval '10 mins') and (payment_status in (1,2)) and (merchant_transaction_id is not null) and (payment_method = 1) limit 1;`

export const processMeetOrders = async () => {
    try {
        const timeoutOrdersRows = await query(meetOrdersQuery, []);
        const timeoutOrders = timeoutOrdersRows.rows;
        if (timeoutOrders.length) {
            for (let i = 0; i < timeoutOrders.length; i++) {
                const paymentStatus = await paymentStatusCheck(timeoutOrders[i].merchant_transaction_id);

                if (paymentStatus.code == PHONE_PE_STATUS.PAYMENT_SUCCESS.name) {
                    let orderDetail = await query(updatePayStatusQuery, [PHONE_PE_STATUS.PAYMENT_SUCCESS.id, timeoutOrders[i].merchant_transaction_id]);

                    if (!orderDetail.rowCount) {
                        logger.error("Transaction Id not found");
                        return;
                    }

                    const aritstMeets = await query(getArtistMeetByArtIdQuery, [timeoutOrders[i].artist_meet_id]);

                    if (!aritstMeets.rowCount) {
                        logger.error("Transaction Id not found");
                        return;
                    }

                    // const userMeet = await query(getUserByFormIdQuery, [timeoutOrders[i].form_id]);

                    // if (!userMeet.rowCount) {
                    //     logger.error("User Meet Id not found");
                    //     return;
                    // }


                    // const meetPaylod: ScheduleMeetingBody = {
                    //     meta: [],
                    //     timezone: "Asia/Calcutta",
                    //     date: timeoutOrders[i].slot_date,
                    //     time: timeoutOrders[i].slot_time,
                    //     invite_friends: [],
                    //     form_id: timeoutOrders[i].form_id,
                    //     session_id: userMeet.rows[0].session_id,
                    //     people_id: userMeet.rows[0].people_id,
                    //     unique_field_value: userMeet.rows[0].unique_field_value,
                    //     source_referrer: `https://asas.com/21b9eb89-bae0-41a9-a2a2-e6316bcda6bf?`
                    // }

                    const invoice = await generateInvoice(timeoutOrders[i].id, timeoutOrders[i].user_id, 'MEET')

                    // const meet = await scheduleMeeting(meetPaylod, aritstMeets.rows[0].source_id);

                    // if (meet) {
                    await query(updateMeetScheduledQuery, [timeoutOrders[i].id, invoice]);
                    // }

                    const calendlyData = await query(getCldlyCheckoutByIDquery, [timeoutOrders[i].id]);

                    if (!calendlyData.rowCount) {
                        logger.error(
                            `Event Id is Invali : ${timeoutOrders[i].artist_meet_id}`
                        );
                        return
                    }

                    await query(updateCalendlyStatus, ['PAYMENT_SUCCESS', calendlyData.rows[0].email, calendlyData.rows[0].source_id, calendlyData.rows[0].invite_id, true])
                } else if (paymentStatus.code == PHONE_PE_STATUS.TIMED_OUT.name || paymentStatus.code == PHONE_PE_STATUS.PAYMENT_ERROR.name || paymentStatus.code == PHONE_PE_STATUS.PAYMENT_DECLINED.name) {
                    await query(updatePayStatusQuery, [PHONE_PE_STATUS.PAYMENT_ERROR.id, timeoutOrders[i].merchant_transaction_id]);

                    const calendlyData = await query(getCldlyCheckoutByIDquery, [timeoutOrders[i].id]);

                    if (!calendlyData.rowCount) {
                        logger.error(
                            `Event Id is Invali : ${timeoutOrders[i].artist_meet_id}`
                        );
                        return
                    }

                    await cancelEvent(calendlyData.rows[0].invite_id)

                    await query(updateCalendlyStatus, ['PAYMENT_FAILED', calendlyData.rows[0].email, calendlyData.rows[0].source_id, calendlyData.rows[0].invite_id, false])

                    logger.log("Upadted Payment failed meet purchase", timeoutOrders[i].merchant_transaction_id);
                }
            }
            return;
        }
    } catch (error) {
        logger.error(`Error on updating payment status : ${error}`);
        return
    }
}