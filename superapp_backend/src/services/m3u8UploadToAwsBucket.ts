import { Request, Response } from 'express';
import { google } from 'googleapis';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { convertToHLS, ConversionOptions } from '../common/m3u8Converter';
import { uploadMultipleFiles, FileUpload, UploadResult } from '../common/uploadM3u8ToAws';
import logger from '../logger/index';
import {
  uploadArtistVideo,
} from "../uploadProduct/uploadMerch";

interface GoogleDriveFolderRequest {
    folderId: string; // Google Drive folder ID
    description?: string;
    is_active?: boolean;
    artist_id?: number;
    old_video?: string;
    type: string;
    conversionOptions?: ConversionOptions; // Optional HLS conversion settings
    folderName?: string; // AWS S3 folder name
    bucketName?: string; // AWS S3 bucket Name 
}

interface VideoProcessingResponse {
    success: boolean;
    message: string;
    data?: {
        processedVideos: ProcessedVideoInfo[];
        totalVideos: number;
        successfulUploads: number;
        failedProcessing: string[];
    };
    error?: string;
}

interface ProcessedVideoInfo {
    fileName: string;
    playlistUrl: string;
    segmentUrls: string[];
    totalSegments: number;
    uploadResults: UploadResult[];
    dbResult?: any;
}

interface VideoFileInfo {
    id: string;
    name: string;
    size: string;
    mimeType: string;
}

export const processAndUploadFolderVideos = async (
    req: Request,
    res: Response
): Promise<void> => {
    const tempDir = path.join(process.cwd(), 'temp');
    const processedVideos: ProcessedVideoInfo[] = [];
    const failedProcessing: string[] = [];

    try {
        const {
            folderId,
            conversionOptions,
            description,
            is_active,
            artist_id,
            old_video,
            type,
            folderName = 'videos/hls',
            bucketName = 'metastar-superapp'
        }: GoogleDriveFolderRequest = req.body;

        if (!folderId) {
            res.status(400).json({
                success: false,
                message: 'Google Drive folder ID is required',
                error: 'Missing folderId in request body'
            } as VideoProcessingResponse);
            return;
        }

        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }

        logger.info(`Starting to list video files from Google Drive folder: ${folderId}`);
        const videoFiles = await listVideoFilesInFolder(folderId);

        if (videoFiles.length === 0) {
            res.status(404).json({
                success: false,
                message: 'No video files found in the specified folder',
                error: 'Empty folder or no video files'
            } as VideoProcessingResponse);
            return;
        }

        logger.info(`Found ${videoFiles.length} video files to process`);

        // Process each video file
        for (const videoFile of videoFiles) {
            try {
                logger.info(`Processing video: ${videoFile.name} (${videoFile.id})`);
                
                const processedVideo = await processVideoFile(
                    videoFile,
                    tempDir,
                    folderName,
                    bucketName,
                    type,
                    conversionOptions,
                    {
                        description,
                        is_active,
                        artist_id,
                        old_video
                    }
                );

                processedVideos.push(processedVideo);
                logger.info(`Successfully processed video: ${videoFile.name}`);

            } catch (error) {
                logger.error(`Failed to process video ${videoFile.name}:`, error);
                failedProcessing.push(`${videoFile.name}: ${error instanceof Error ? error.message : String(error)}`);
            }
        }

        const successfulUploads = processedVideos.length;
        const totalVideos = videoFiles.length;

        const response: VideoProcessingResponse = {
            success: successfulUploads > 0,
            message: `Processed ${successfulUploads}/${totalVideos} videos successfully`,
            data: {
                processedVideos,
                totalVideos,
                successfulUploads,
                failedProcessing
            }
        };

        if (successfulUploads === 0) {
            res.status(500).json({
                ...response,
                success: false,
                message: 'Failed to process any videos'
            });
        } else if (failedProcessing.length > 0) {
            res.status(207).json(response); // 207 Multi-Status for partial success
        } else {
            res.status(200).json(response);
        }

    } catch (error) {
        logger.error('Error in video folder processing pipeline:', error);
        
        const errorResponse: VideoProcessingResponse = {
            success: false,
            message: 'Video folder processing failed',
            error: error instanceof Error ? error.message : 'Unknown error occurred'
        };

        res.status(500).json(errorResponse);
    } finally {
        // Cleanup temp directory
        try {
            if (fs.existsSync(tempDir)) {
                const tempContents = fs.readdirSync(tempDir);
                for (const item of tempContents) {
                    const itemPath = path.join(tempDir, item);
                    const stat = fs.statSync(itemPath);
                    if (stat.isDirectory()) {
                        fs.rmSync(itemPath, { recursive: true, force: true });
                    } else {
                        fs.unlinkSync(itemPath);
                    }
                }
                logger.info('Cleaned up temporary directory');
            }
        } catch (cleanupError) {
            logger.warn('Error during cleanup:', cleanupError);
        }
    }
};

async function listVideoFilesInFolder(folderId: string): Promise<VideoFileInfo[]> {
    const auth = new google.auth.GoogleAuth({
        keyFile: process.env.GOOGLE_APPLICATION_CREDENTIALS,
        scopes: ['https://www.googleapis.com/auth/drive.readonly']
    });

    const drive = google.drive({ version: 'v3', auth });
    const videoFiles: VideoFileInfo[] = [];

    try {
        const videoMimeTypes = [
            'video/mp4',
            'video/avi',
            'video/mov',
            'video/wmv',
            'video/flv',
            'video/webm',
            'video/mkv',
            'video/m4v'
        ];

        let pageToken: string | undefined;
        
        do {
            const response = await drive.files.list({
                q: `'${folderId}' in parents and trashed=false and (${videoMimeTypes.map(type => `mimeType='${type}'`).join(' or ')})`,
                fields: 'nextPageToken, files(id, name, size, mimeType)',
                pageSize: 100,
                pageToken
            });

            if (response.data.files) {
                for (const file of response.data.files) {
                    if (file.id && file.name && file.size && file.mimeType) {
                        videoFiles.push({
                            id: file.id,
                            name: file.name,
                            size: file.size,
                            mimeType: file.mimeType
                        });
                    }
                }
            }

            pageToken = response.data.nextPageToken || undefined;
        } while (pageToken);

        logger.info(`Found ${videoFiles.length} video files in folder`);
        return videoFiles;

    } catch (error) {
        logger.error('Error listing files in Google Drive folder:', error);
        throw new Error(`Failed to list files in folder: ${error}`);
    }
}

async function processVideoFile(
    videoFile: VideoFileInfo,
    tempDir: string,
    folderName: string = 'videos/hls',
    bucketName: string = 'metastar-superapp',
    type: string,
    conversionOptions?: ConversionOptions,
    dbParams?: {
        video_id?: string;
        description?: string;
        is_active?: boolean;
        artist_id?: number;
        old_video?: string;
    }
): Promise<ProcessedVideoInfo> {
    let downloadedVideoPath = '';
    let hlsOutputDir = '';

    try {
        // Download video
        logger.info(`Downloading video: ${videoFile.name} (${videoFile.size} bytes)`);
        const downloadResult = await downloadVideoFromGoogleDrive(videoFile.id, videoFile.name, tempDir);
        downloadedVideoPath = downloadResult.filePath;

        // Convert to HLS
        logger.info(`Converting ${videoFile.name} to HLS...`);
        const uniqueId = uuidv4();
        hlsOutputDir = path.join(tempDir, `hls_${uniqueId}_${videoFile.id}`);

        const defaultConversionOptions: ConversionOptions = {
            outputDir: hlsOutputDir,
            segmentDuration: 10,
            videoBitrate: '1500k',
            audioBitrate: '128k',
            resolution: '1920x1080',
            playlistName: 'output.m3u8',
            segmentPrefix: 'output_%03d.ts',
            ...conversionOptions // Merge with user options
        };

        const conversionResult = await convertToHLS(downloadedVideoPath, defaultConversionOptions);

        if (!conversionResult.success) {
            throw new Error(`HLS conversion failed: ${conversionResult.error}`);
        }

        logger.info(`HLS conversion successful for ${videoFile.name}. Segments: ${conversionResult.segmentCount}`);

        // Prepare files for upload with unique folder structure
        const videoFolderName = `${folderName}/${path.parse(videoFile.name).name}`;
        const filesToUpload = await prepareHLSFilesForUpload(hlsOutputDir, videoFolderName);

        logger.info(`Prepared ${filesToUpload.length} files for upload for ${videoFile.name}`);

        // Upload to AWS S3
        logger.info(`Starting upload to AWS S3 for ${videoFile.name}...`);
        const uploadResults = await uploadMultipleFiles(bucketName, filesToUpload);

        const failedUploads = uploadResults.filter(result => result.error);
        if (failedUploads.length > 0) {
            logger.warn(`${failedUploads.length} files failed to upload for ${videoFile.name}:`, failedUploads);
        }

        const successfulUploads = uploadResults.filter(result => result.url);
        const playlistUpload = successfulUploads.find(result => 
            result.fileName.endsWith('.m3u8')
        );

        if (!playlistUpload) {
            throw new Error(`Failed to upload playlist file for ${videoFile.name}`);
        }

        const playlistUploadToUpload = new URL(playlistUpload.url!).pathname.slice(1);

        // Upload to database if type is specified
        let dbResult = null;
        if (type === "ArtistVideo" && dbParams) {
            try {
                dbResult = await uploadArtistVideo({
                    video: playlistUploadToUpload,
                    is_active: dbParams.is_active !== undefined ? dbParams.is_active : true,
                    artist_id: dbParams.artist_id || 0,
                    old_video: dbParams.old_video || ''
                });

                if (!dbResult.status) {
                    logger.warn(`Database upload failed for ${videoFile.name}:`, dbResult);
                }
            } catch (dbError) {
                logger.error(`Error uploading metadata to DB for ${videoFile.name}:`, dbError);
                dbResult = { error: dbError instanceof Error ? dbError.message : String(dbError) };
            }
        }

        const segmentUrls = successfulUploads
            .filter(result => result.fileName.endsWith('.ts'))
            .map(result => result.url!);

        return {
            fileName: videoFile.name,
            playlistUrl: playlistUpload.url!,
            segmentUrls,
            totalSegments: conversionResult.segmentCount || 0,
            uploadResults,
            dbResult
        };

    } catch (error) {
        logger.error(`Error processing video ${videoFile.name}:`, error);
        throw error;
    } finally {
        // Clean up files for this video
        try {
            if (downloadedVideoPath && fs.existsSync(downloadedVideoPath)) {
                fs.unlinkSync(downloadedVideoPath);
                logger.info(`Cleaned up downloaded video: ${downloadedVideoPath}`);
            }
            
            if (hlsOutputDir && fs.existsSync(hlsOutputDir)) {
                fs.rmSync(hlsOutputDir, { recursive: true, force: true });
                logger.info(`Cleaned up HLS output directory: ${hlsOutputDir}`);
            }
        } catch (cleanupError) {
            logger.warn(`Error during cleanup for ${videoFile.name}:`, cleanupError);
        }
    }
}

async function downloadVideoFromGoogleDrive(
    fileId: string,
    fileName: string,
    outputDir: string = './temp'
): Promise<{ filePath: string; fileName: string }> {
    const auth = new google.auth.GoogleAuth({
        keyFile: process.env.GOOGLE_APPLICATION_CREDENTIALS,
        scopes: ['https://www.googleapis.com/auth/drive.readonly']
    });

    const drive = google.drive({ version: 'v3', auth });

    try {
        const fileMetadata = await drive.files.get({
            fileId: fileId,
            fields: 'name, size, mimeType'
        });

        const originalFileName = fileMetadata.data.name || 'video';
        const fileSize = fileMetadata.data.size;
        const mimeType = fileMetadata.data.mimeType;

        logger.info(`Downloading file: ${originalFileName} (${fileSize} bytes, ${mimeType})`);

        const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9._-]/g, '_');
        const filePath = path.join(outputDir, `${sanitizedFileName}`);

        // Download file
        const response = await drive.files.get({
            fileId: fileId,
            alt: 'media'
        }, {
            responseType: 'stream'
        });

        const writeStream = fs.createWriteStream(filePath);
        
        await new Promise<void>((resolve, reject) => {
            response.data.pipe(writeStream);
            
            writeStream.on('finish', () => {
                logger.info(`File downloaded successfully: ${filePath}`);
                resolve();
            });
            
            writeStream.on('error', (error) => {
                logger.error('Error writing file:', error);
                reject(error);
            });

            response.data.on('error', (error) => {
                logger.error('Error downloading file:', error);
                reject(error);
            });
        });

        return { filePath, fileName: sanitizedFileName };

    } catch (error) {
        logger.error('Error downloading from Google Drive:', error);
        throw new Error(`Failed to download video from Google Drive: ${error}`);
    }
}

async function prepareHLSFilesForUpload(
    hlsOutputDir: string,
    s3FolderName: string
): Promise<FileUpload[]> {
    const files: FileUpload[] = [];

    try {
        const dirContents = fs.readdirSync(hlsOutputDir);
        
        for (const file of dirContents) {
            const filePath = path.join(hlsOutputDir, file);
            const stat = fs.statSync(filePath);
            
            if (stat.isFile() && (file.endsWith('.m3u8') || file.endsWith('.ts'))) {
                files.push({
                    fileName: file,
                    filePath: filePath,
                    folderName: s3FolderName
                });
            }
        }

        logger.info(`Found ${files.length} HLS files to upload`);
        return files;

    } catch (error) {
        logger.error('Error preparing HLS files for upload:', error);
        throw new Error(`Failed to prepare HLS files for upload: ${error}`);
    }
}