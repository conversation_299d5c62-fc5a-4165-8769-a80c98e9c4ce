import { Request, Response } from 'express';
import { google } from 'googleapis';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { convertToHLS, ConversionOptions } from '../common/m3u8Converter';
import { uploadMultipleFiles, FileUpload, UploadResult } from '../common/uploadM3u8ToAws';
import logger from '../logger/index';
import {
  uploadArtistVideo,
} from "../uploadProduct/uploadMerch";
interface GoogleDriveVideoRequest {
    fileId: string; // Google Drive file ID
    video_id:string;
    description:string;
    is_active:boolean;
    artist_id:number;
    old_video:string;
    type: string;
    fileName?: string; // Optional custom filename
    conversionOptions?: ConversionOptions; // Optional HLS conversion settings
    folderName?: string; // AWS S3 folder name
    bucketName?: string; // AWS S3 bucket Name 
}

interface VideoProcessingResponse {
    success: boolean;
    message: string;
    data?: {
        playlistUrl: string;
        segmentUrls: string[];
        totalSegments: number;
        uploadResults: UploadResult[];
    };
    error?: string;
}

export const processAndUploadVideo = async (
    req: Request,
    res: Response
): Promise<void> => {
    const tempDir = path.join(process.cwd(), 'temp');
    let downloadedVideoPath = '/Gdrive-videos/';
    let hlsOutputDir = '/hls-outputs/';

    try {
        const {
            fileId,
            fileName,
            conversionOptions,
            video_id,
            description,
            is_active,
            artist_id,
            old_video,
            type,
            folderName = 'videos/hls',
            bucketName='metastar-superapp'
        }: GoogleDriveVideoRequest = req.body;

        if (!fileId) {
            res.status(400).json({
                success: false,
                message: 'Google Drive file ID is required',
                error: 'Missing fileId in request body'
            } as VideoProcessingResponse);
            return;
        }

        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }

        logger.info(`Starting download from Google Drive: ${fileId}`);
        const downloadResult = await downloadVideoFromGoogleDrive(fileId, fileName, tempDir);
        downloadedVideoPath = downloadResult.filePath;


        logger.info('Starting HLS conversion...');
        const uniqueId = uuidv4();
        hlsOutputDir = path.join(tempDir, `hls_${uniqueId}`);

        const defaultConversionOptions: ConversionOptions = {
            outputDir: hlsOutputDir,
            segmentDuration: 10,
            videoBitrate: '1500k',
            audioBitrate: '128k',
            resolution: '1920x1080',
            playlistName: 'output.m3u8',
            segmentPrefix: 'output_%03d.ts',
            ...conversionOptions // Merge with user options
        };

        const conversionResult = await convertToHLS(downloadedVideoPath, defaultConversionOptions);

        if (!conversionResult.success) {
            throw new Error(`HLS conversion failed: ${conversionResult.error}`);
        }

        logger.info(`HLS conversion successful. Segments: ${conversionResult.segmentCount}`);

        const filesToUpload = await prepareHLSFilesForUpload(hlsOutputDir, folderName);

        logger.info(`Prepared ${filesToUpload.length} files for upload`);

        logger.info('Starting upload to AWS S3...');
        const uploadResults = await uploadMultipleFiles(bucketName, filesToUpload);

        const failedUploads = uploadResults.filter(result => result.error);
        if (failedUploads.length > 0) {
            logger.warn(`${failedUploads.length} files failed to upload:`, failedUploads);
        }

        const successfulUploads = uploadResults.filter(result => result.url);
        const playlistUpload = successfulUploads.find(result => 
            result.fileName.endsWith('.m3u8')
        );

        if (!playlistUpload) {
            throw new Error('Failed to upload playlist file');
        }

        const playlistUploadToUpload =new URL(playlistUpload.url!).pathname.slice(1);

        try {

            let dbResponse;

            if(type === "ArtistVideo"){
                dbResponse = await uploadArtistVideo({
                   video_id,
                   video: playlistUploadToUpload,
                   description,
                   is_active,
                   artist_id,
                   old_video
               });
           
               const response = {
                   success: true,
                   status: 200,
                   message: 'Video processed and uploaded successfully',
                   dbResult: dbResponse, 
               };
           
               if(dbResponse.status){
                   logger.info('Video metadata uploaded to DB successfully');
                   res.status(200).json(response);
               }else{
                   logger.info('error no dbresponse status false');
                   res.status(500).json({
                    success: false,
                    message:'error no dbresponse status false',
                    result: dbResponse, 
                   }); 
               }
            }else{
                 logger.info('error no "type"');
                res.status(500).json(
                    {
                    success: false,
                    message: 'error no "type"',
                    result: dbResponse, 
                    }); 
            }


} catch (error) {
    logger.error('Error uploading metadata to DB:', error);
    res.status(500).json({
        success: false,
        message: 'Failed to upload video metadata',
        error: error instanceof Error ? error.message : String(error)
    });
}

    } catch (error) {
        logger.error('Error in video processing pipeline:', error);
        
        const errorResponse: VideoProcessingResponse = {
            success: false,
            message: 'Video processing failed',
            error: error instanceof Error ? error.message : 'Unknown error occurred'
        };

        res.status(500).json(errorResponse);
    } finally {
        // Delete temporary files
        try {
            if (downloadedVideoPath && fs.existsSync(downloadedVideoPath)) {
                fs.unlinkSync(downloadedVideoPath);
                logger.info(`Cleaned up downloaded video: ${downloadedVideoPath}`);
            }
            
            if (hlsOutputDir && fs.existsSync(hlsOutputDir)) {
                fs.rmSync(hlsOutputDir, { recursive: true, force: true });
                logger.info(`Cleaned up HLS output directory: ${hlsOutputDir}`);
            }
        } catch (cleanupError) {
            logger.warn('Error during cleanup:', cleanupError);
        }
    }
};

async function downloadVideoFromGoogleDrive(
    fileId: string,
    customFileName?: string,
    outputDir: string = './temp'
): Promise<{ filePath: string; fileName: string }> {
    const auth = new google.auth.GoogleAuth({
        keyFile: process.env.GOOGLE_APPLICATION_CREDENTIALS,
        scopes: ['https://www.googleapis.com/auth/drive.readonly']
    });

    const drive = google.drive({ version: 'v3', auth });

    try {
        const fileMetadata = await drive.files.get({
            fileId: fileId,
            fields: 'name, size, mimeType'
        });

        const originalFileName = fileMetadata.data.name || 'video';
        const fileSize = fileMetadata.data.size;
        const mimeType = fileMetadata.data.mimeType;

        logger.info(`Downloading file: ${originalFileName} (${fileSize} bytes, ${mimeType})`);

        const fileName = customFileName || originalFileName;
        const filePath = path.join(outputDir, fileName);

        // download file
        const response = await drive.files.get({
            fileId: fileId,
            alt: 'media'
        }, {
            responseType: 'stream'
        });

        const writeStream = fs.createWriteStream(filePath);
        
        await new Promise<void>((resolve, reject) => {
            response.data.pipe(writeStream);
            
            writeStream.on('finish', () => {
                logger.info(`File downloaded successfully: ${filePath}`);
                resolve();
            });
            
            writeStream.on('error', (error) => {
                logger.error('Error writing file:', error);
                reject(error);
            });

            response.data.on('error', (error) => {
                logger.error('Error downloading file:', error);
                reject(error);
            });
        });

        return { filePath, fileName };

    } catch (error) {
        logger.error('Error downloading from Google Drive:', error);
        throw new Error(`Failed to download video from Google Drive: ${error}`);
    }
}

async function prepareHLSFilesForUpload(
    hlsOutputDir: string,
    s3FolderName: string
): Promise<FileUpload[]> {
    const files: FileUpload[] = [];

    try {
        const dirContents = fs.readdirSync(hlsOutputDir);
        
        for (const file of dirContents) {
            const filePath = path.join(hlsOutputDir, file);
            const stat = fs.statSync(filePath);
            
            if (stat.isFile() && (file.endsWith('.m3u8') || file.endsWith('.ts'))) {
                files.push({
                    fileName: file,
                    filePath: filePath,
                    folderName: s3FolderName
                });
            }
        }

        logger.info(`Found ${files.length} HLS files to upload`);
        return files;

    } catch (error) {
        logger.error('Error preparing HLS files for upload:', error);
        throw new Error(`Failed to prepare HLS files for upload: ${error}`);
    }
}
