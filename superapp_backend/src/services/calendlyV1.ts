import axios from "axios";
import { PHONE_PE_STATUS } from "../common/constants";
import { updatePayStatusQuery, getCldlyCheckoutByIDquery, updateCalendlyStatus, getTimedoutCalendly, updatedTimeoutCalendly, oldPendingMeetsPaymentsQuery } from "../common/db_constants";
import { query } from "../db";
import logger from "../logger";

const CALENDLY_TOKEN = process.env.CALENDLY_TOKEN;
const CALENDLY_BASE_URL = process.env.CALENDLY_URL;

export const calendlyAPI = axios.create({
    baseURL: CALENDLY_BASE_URL,
    headers: {
        Authorization: `Bearer ${CALENDLY_TOKEN}`,
        "Content-Type": "application/json",
    },
});

export const getEventAvailableSlots = async (
    eventURI: string,
    startTime: string,
    endTime: string
) => {
    try {
        const response = await calendlyAPI.get("/event_type_available_times", {
            params: {
                event_type: eventURI,
                start_time: startTime,
                end_time: endTime,
            },
        });
        return response.data;
    } catch (error: any) {
        console.error("Available slot error calendly")
        console.error(JSON.stringify(error))
        throw error;
    }
}

export const cancelEvent = async (
    inviteUUID: string,
    reason = 'Admin Cancellation'
) => {
    try {
        const data = {
            reason: reason
        };
        const response = await calendlyAPI.post(`/scheduled_events/${inviteUUID}/cancellation`, data);
        return response.data;
    } catch (error) {
        console.error("Cancel Error Calendly")
        console.error(JSON.stringify(error))
        throw error;
    }
}

export const timeOutCalendlyPendings = async () => {
    try {
        const orders = await query(oldPendingMeetsPaymentsQuery, []);

        if (!orders.rowCount) {
            return;
        }

        for (let index = 0; index < orders.rows.length; index++) {
            const element = orders.rows[index];

            await query(updatePayStatusQuery, [PHONE_PE_STATUS.PAYMENT_ERROR.id, element.merchant_transaction_id]);

            const calendlyData = await query(getCldlyCheckoutByIDquery, [element.id]);

            if (!calendlyData.rowCount) {
                logger.error(
                    `Event Id is Invali : ${element.artist_meet_id}`
                );
                break
            }

            await cancelEvent(calendlyData.rows[0].invite_id, "No Payment Received")

            await query(updateCalendlyStatus, ['PAYMENT_FAILED', calendlyData.rows[0].email, calendlyData.rows[0].source_id, calendlyData.rows[0].invite_id, false])

            logger.log("Upadted Payment failed meet purchase", element.merchant_transaction_id);
        }

        return
    } catch (error) {
        logger.error(`Error on cancelling calendly pending reqs : ${error}`);
        return
    }
}

export const timeOutCalendlyV2 = async () => {
    try {
        const eventsData = await query(getTimedoutCalendly, []);
        if (!eventsData.rowCount) {
            return null;
        }

        const data = eventsData.rows;

        for (let index = 0; index < data.length; index++) {
            const event = data[index];
            await cancelEvent(event.invite_id, "No Payment Received")
            await query(updatedTimeoutCalendly, [event.id, 'PAYMENT_TIMEOUT', false])
        }
        return null;
    } catch (error) {
        console.error("Error in Cancelling Calendly Pending Events");
        return null;
    }
}
