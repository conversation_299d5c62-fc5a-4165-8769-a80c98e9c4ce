import { PHONE_PE_STATUS } from "../common/constants";
import { query } from "../db";
import { paymentStatusCheck } from "./pg";
import { generateInvoice } from "./orders";
import { updatePaymentTxnQuery, updateVideoPurchasedQuery } from "../common/db_constants";
import logger from "../logger";

const paidVideoPurchasesQuery = `select 
pt.id,pt.merchant_transaction_id,pt.user_id,upve.id entry_id,
case when validity is not null then (CURRENT_TIMESTAMP + (validity || ' days')::interval) end
from payment_transactions pt
inner join user_paid_video_entries upve on upve.txn_id = pt.id
where pt.created < now() - interval '10 mins' 
and pt.payment_status in (1,2) 
and pt.merchant_transaction_id is not null limit 1;`

export const processPaidVideoPurchases = async () => {
    try {
        const timeoutOrdersRows = await query(paidVideoPurchasesQuery, []);
        const timeoutOrders = timeoutOrdersRows.rows;
        if (timeoutOrders.length) {
            for (let i = 0; i < timeoutOrders.length; i++) {
                const paymentStatus = await paymentStatusCheck(timeoutOrders[i].merchant_transaction_id);

                if (paymentStatus.code == PHONE_PE_STATUS.PAYMENT_SUCCESS.name) {
                    let orderDetail = await query(updatePaymentTxnQuery, [PHONE_PE_STATUS.PAYMENT_SUCCESS.id, timeoutOrders[i].merchant_transaction_id]);

                    if (!orderDetail.rowCount) {
                        logger.error("Merchat Txn Id not found & updated");
                        return;
                    }

                    const invoice = await generateInvoice(timeoutOrders[i].id, timeoutOrders[i].user_id, 'PAID_VIDEO')

                    const course = await query(updateVideoPurchasedQuery, [timeoutOrders[i].id, invoice]);

                    if (!course.rowCount) {
                        logger.error("Video Entry not found");
                        return;
                    }

                    return;
                } else if (paymentStatus.code == PHONE_PE_STATUS.TIMED_OUT.name || paymentStatus.code == PHONE_PE_STATUS.PAYMENT_ERROR.name || paymentStatus.code == PHONE_PE_STATUS.PAYMENT_DECLINED.name) {
                    await query(updatePaymentTxnQuery, [PHONE_PE_STATUS.PAYMENT_ERROR.id, timeoutOrders[i].merchant_transaction_id]);
                    logger.error(`Upadted Payment failed video purchase : ${timeoutOrders[i].merchant_transaction_id}`);
                    return;
                }
            }
            return;
        }
    } catch (error) {
        logger.error(`Error on updating payment status : ${error}`);
        return
    }
}