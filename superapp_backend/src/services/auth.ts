import { insertuserProfilequery } from "../controller/merch";
import { OTP_TYPES } from "../common/constants";
import {
  add_otp,
  checkUserEmailQuery,
  checkUserMobQuery,
  checkUserNameQuery,
  getOtpQuery,
  getUserbyemail,
  getUserbyUID,
  insertUser<PERSON>uery,
  otp_attempt,
  otp_verified,
  updatePasswordQuery,
  upsertUserQuery,
  userByCredQuery,
  verify_email_query,
  verify_mobile_query,
} from "../common/db_constants";
import { query } from "../db";
import { USER } from "../types/db_types";

export const insertUser = async (data: USER) => {
  try {
    const user = await query(insertUserQuery, [
      data.password_hash,
      data.mobile_number,
      data.email,
      data.client_id,
      data.login_method,
    ]);
    if (!user.rowCount) {
      return Promise.reject({
        status: false,
        message: "User not created",
      });
    }

    const user_profile = await query(insertuserProfilequery, [
      null,
      data.email,
      null,
      data.mobile_number,
      user.rows[0].id,
      null,
    ]);

    if (!user_profile.rowCount) {
      return Promise.reject({
        status: false,
        message: "User Profile not created",
      });
    }

    return Promise.resolve({
      status: true,
      message: "User created",
      data: user.rows[0] as USER,
    });
  } catch (error) {
    return Promise.reject({
      status: false,
      message: `Couldn't insert user into db`,
      error: error,
    });
  }
};

export const checkUsername = async (username: string) => {
  try {
    const data = await query(checkUserNameQuery, [username]);
    if (data.rowCount) {
      return true;
    }
    return false;
  } catch (error) {
    return Promise.reject({
      status: false,
      message: `Couldn't validate username`,
      error: error,
    });
  }
};

export const checkUserEmail = async (email: string) => {
  try {
    const data = await query(checkUserEmailQuery, [email]);
    if (data.rowCount) {
      return true;
    }
    return false;
  } catch (error) {
    return Promise.reject({
      status: false,
      message: `Couldn't validate email id`,
      error: error,
    });
  }
};

export const checkUserMobile = async (mobile: string) => {
  try {
    const data = await query(checkUserMobQuery, [mobile]);
    if (data.rowCount) {
      return true;
    }
    return false;
  } catch (error) {
    return Promise.reject({
      status: false,
      message: `Couldn't validate mobile number`,
      error: error,
    });
  }
};

export const getOtp = async (uid: number, otpType: string) => {
  try {
    const otp = await query(getOtpQuery, [uid, otpType]);
    if (!otp.rowCount) {
      return null;
    }
    return otp.rows[0];
  } catch (error) {
    return Promise.reject({
      status: false,
      message: `couldn't find otp`,
      error,
    });
  }
};

export const saveOtp = async (email: string, action: string) => {
  try {
    const otpNumber = Math.floor(Math.random() * (9999 - 1000 + 1)) + 1000;
    const otp = process.env.NODE_ENV === "development" ? "1111" : otpNumber;
    await query(add_otp, [email, otp, action]);
    return otp;
  } catch (error) {
    return Promise.reject({
      status: false,
      message: "Couldn't send otp",
      rawError: error,
    });
  }
};

export const updateOtpAttempt = async (
  email: string,
  action: string,
  otp: number,
  resent: number
) => {
  try {
    const update = await query(otp_attempt, [email, action, otp, resent]);
    if (update.rows.length == 0) {
      return Promise.reject({
        message: "Otp not verified.",
      });
    }
    return update.rows;
  } catch (error) {
    return Promise.reject({
      status: false,
      message: "Otp not verified",
      rawError: error,
    });
  }
};

export const updateOtpVerified = async (email: string, action: string) => {
  try {
    const update = await query(otp_verified, [email, action]);
    if (update.rows.length == 0) {
      return Promise.reject({
        message: "Otp not verified.",
      });
    }

    if (action == OTP_TYPES.MAIL) {
      await query(verify_email_query, [email]);
    } else if (action == OTP_TYPES.MOBILE) {
      await query(verify_mobile_query, [email]);
    }

    return update.rows;
  } catch (error) {
    return Promise.reject({
      status: false,
      message: "Otp not verified",
      rawError: error,
    });
  }
};

export const findUser = async (
  userType: number,
  userName?: string,
  email?: string,
  mobile?: string | null
) => {
  try {
    const user = await query(userByCredQuery, [
      email,
      mobile,
      userName,
      userType,
    ]);
    return user.rows[0];
  } catch (error) {
    return Promise.reject({
      status: false,
      message: "Couldn't find the user",
      rawError: error,
    });
  }
};

export const updateNewPassword = async (pass: string, destination: string) => {
  try {
    const user = await query(updatePasswordQuery, [pass, destination]);
    return user.rowCount;
  } catch (error) {
    return Promise.reject({
      status: false,
      message: "Couldn't find the user",
      rawError: error,
    });
  }
};

// export const upsertUser = async (data: USER) => {
//   try {

//     const user = await query(upsertUserQuery, [
//       data.password_hash,
//       data.mobile_number,
//       data.email,
//       data.client_id,
//       data.login_method,
//       data.email_verified,
//       data.display_name,
//       data.display_pic,
//     ]);

//     if (!user.rowCount) {
//       return Promise.reject({
//         status: false,
//         message: "User not created",
//       });
//     }

//     const user_profile = await query(insertuserProfilequery, [
//       null,
//       data.email,
//       null,
//       data.mobile_number,
//       user.rows[0].id,
//       null,
//     ]);

//     if (!user_profile.rowCount) {
//       return Promise.reject({
//         status: false,
//         message: "User Profile not created",
//       });
//     }
//     return Promise.resolve({
//       status: true,
//       message: "User created",
//       data: user.rows[0] as USER,
//     });
//   } catch (error) {
//     return Promise.reject({
//       status: false,
//       message: `Couldn't Upsert user into db`,
//       error: error,
//     });
//   }
// };
export const upsertUser = async (data: USER) => {
  try {
    let availableUser;
    if (!data.email) {
      availableUser = await query(getUserbyUID, [data.uid]);
    } else {
      availableUser = await query(getUserbyemail, [data.email]);
    }

    if (availableUser.rowCount) {
      return Promise.resolve({
        status: true,
        message: "User created",
        data: availableUser.rows[0] as USER,
      });
    } else {
      const user = await query(upsertUserQuery, [
        data.password_hash,
        data.mobile_number,
        data.email,
        data.client_id,
        data.login_method,
        data.email_verified,
        data.display_name,
        data.display_pic,
        data.uid,
      ]);

      if (!user.rowCount) {
        return Promise.reject({
          status: false,
          message: "User not created",
        });
      }

      const user_profile = await query(insertuserProfilequery, [
        data.display_name,
        data.email,
        data.display_pic,
        data.mobile_number,
        user.rows[0].id,
        null,
      ]);

      if (!user_profile.rowCount) {
        return Promise.reject({
          status: false,
          message: "User Profile not created",
        });
      }
      return Promise.resolve({
        status: true,
        message: "User created",
        data: user.rows[0] as USER,
      });
    }
  } catch (error) {
    return Promise.reject({
      status: false,
      message: `Couldn't Upsert user into db`,
      error: error,
    });
  }
};
