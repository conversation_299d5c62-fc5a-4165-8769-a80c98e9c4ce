import logger from "../logger";
import nodemailer from "nodemailer";

export const sendEmail = async (emailId: string[], subject: string, body?: string, cc?: string,attachments?: any) => {
    let transporter;
    let mailOptions = {};
    if (process.env.APP_ENV === 'PROD') {
        transporter = nodemailer.createTransport({
            host: process.env.PROD_MAILER_HOST,
            port: Number(process.env.PROD_MAILER_PORT),
            secure: true,
            auth: {
                user: process.env.PROD_MAILER_USER,
                pass: process.env.PROD_MAILER_PASSWORD
            },
            debug: true
        });
        mailOptions = { from: process.env.PROD_MAILER_FROM, to: emailId, subject: subject, html: body, cc: cc, attachments: attachments};
    } else {
        transporter = nodemailer.createTransport({
            host: process.env.TEST_MAILER_HOST,
            port: Number(process.env.TEST_MAILER_PORT),
            secure: true,
            auth: {
                user: process.env.TEST_MAILER_USER,
                pass: process.env.TEST_MAILER_PASSWORD
            },
            debug: true
        });
        mailOptions = { from: process.env.TEST_MAILER_FROM, to: emailId, subject: subject, html: body, cc: cc, attachments: attachments};
    }
    try {
        await transporter.sendMail(mailOptions);
        return "success"
    } catch (error) {
        logger.error(error);
    }
    return "empty";
}
