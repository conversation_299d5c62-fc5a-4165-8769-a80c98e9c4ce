import { addHoldingsQuery, addTreasureHistory, getCoinQuery, getTreasureProductsQuery, getTreasuresQuery, getUserTreasureQuery, updateHoldingQuery, updateHoldingStatusQuery, updateHuntAvilabilityQuery, updateTreasureProductQuery, userClaimedCoinsQuery, userTreasureHoldingQuery } from "../common/db_constants";
import { query } from "../db";

export const getTreasuresDb = async (artistId?: number) => {
    try {
        let coins = await query(getTreasuresQuery, [artistId ? artistId : null]);
        return coins.rows;
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on getting treasure details" })
    }
}

export const getCoinDb = async (userId: number, huntId: number, coinId: number) => {
    try {
        let coins = await query(getCoinQuery, [huntId]);
        if (!coins.rowCount) {
            return Promise.reject({ status: false, message: "Coin detail not found" })
        }


        const holdings = await query(userClaimedCoinsQuery, [huntId, userId]);
        const coin = coins.rows.find(el => Number(el.coin_id) == Number(coinId));
        if(!coin){
            return Promise.reject({ status: false, message: "Coin id not found" })
        }
        
        const response = {
            claim_id: holdings.rowCount ? holdings.rows[0].id : null,
            ...coin,
            is_over: coin.is_limited_quantity ? coin.available == 0 : false,
            coins_collected: holdings.rowCount,
            claimed: holdings.rowCount && holdings.rows.find(_el => Number(_el.coin_id) === Number(coinId)) != null ? true : false,
            productClaimed: holdings.rowCount && holdings.rows[0].product_item_id ? true : false
        }

        return response;
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on getting coin detail" })
    }
}

export const getAvailableHolding = async (userId: number, huntId: number) => {
    try {
        const userTreasure = await query(userTreasureHoldingQuery, [userId, huntId]);
        return userTreasure.rows;
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on getting coin holding" })
    }
}

export const addholding = async (userId: number, treasureId: number, qty: number) => {
    try {
        const userTreasure = await query(addHoldingsQuery, [userId, treasureId, qty]);
        return userTreasure.rows;
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on adding coin holding" })
    }
}

export const updateHolding = async (qty: number, holdingId: number) => {
    try {
        const userTreasure = await query(updateHoldingQuery, [qty, holdingId]);
        return userTreasure.rows;
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on updating coin holding" })
    }
}

export const updateHoldingStatus = async (holdingId: number) => {
    try {
        const userTreasure = await query(updateHoldingStatusQuery, ['ELIGIBLE', holdingId]);
        return userTreasure.rows;
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on updating holding status" })
    }
}

export const updateHuntAvilability = async (huntId: number) => {
    try {
        const userTreasure = await query(updateHuntAvilabilityQuery, [huntId]);
        return userTreasure.rows;
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on updating hunt availability" })
    }
}

export const getUserCollectedTreasure = async (holdingId: number, userId: number) => {
    try {
        const userTreasure = await query(getUserTreasureQuery, [holdingId, userId]);
        return userTreasure.rows;
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on getting holding status" })
    }
}

export const updateProduct = async (holdingId: number, productItemId: number, addressId: number) => {
    try {
        const userTreasure = await query(updateTreasureProductQuery, [productItemId, addressId, holdingId]);
        return userTreasure.rows;
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on updating product" })
    }
}

export const addClaimHistory = async (userId: number, coinId: number, holdingId: number, qty: number) => {
    try {
        const userTreasure = await query(addTreasureHistory, [userId, coinId, holdingId, qty]);
        return userTreasure.rows;
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on adding coin history" });
    }
}

export const getTrsrProducts = async (artistId: number) => {
    try {
        const pdts = await query(getTreasureProductsQuery, [artistId]);
        return pdts.rows
    } catch (error) {
        return Promise.reject({ status: false, message: "Error on getting products" });
    }
}