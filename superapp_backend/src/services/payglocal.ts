import { query } from "../db";
import { ORDER_STATUS, PAYMENT_METHOD, PHONE_PE_STATUS } from "../common/constants";
import { deleteCartItemsByArtistIdQuery, getArtistMeetByArtIdQuery, getCldlyCheckoutByIDquery, getCourseDetailQuery, getPaygCourseOrders, getPaygMeetOrders, getPaygOrders, getPaygVideoOrders, insertUserCourseQuery, orderInvoiceQuery, updateCalendlyStatus, updateMeetScheduledQuery, updatePaymentTxnQuery, updatePayStatusQuery, updatePurchaseStatusQuery, updateVideoPurchasedQuery, userAddressinvoiceQuery } from "../common/db_constants";
import logger from "../logger";
import { generatePDF } from "./pdf";
import { updateOrderStatus, updatePaymentFailed, updatePaymentStatus } from "./merch";
import { generateInvoice } from "./orders";

import fs from 'fs';
import axios from 'axios';
import { PayGLocalTxnData } from "types";
import { cancelEvent } from "./calendlyV1";
const { generateJWEAndJWS, generateJWS } = require('payglocal-js-client');

const merchantId = process.env.PAYGLOCAL_MERCHANT_ID;
const privateKeyId = process.env.PAYGLOCAL_PRIVATE_KEY_ID;
const publicKeyId = process.env.PAYGLOCAL_PUBLIC_KEY_ID;
const callBackURL = process.env.PAYGLOCAL_CALLBACK_URL;

const baseURL = process.env.PAYGLOCAL_BASE_URL;
const paymentInitiatEndpoint = process.env.PAYGLOCAL_PAYMENT_INIT_ENDPOINT;

const publicKeyPath = process.env.PAYGLOCAL_PUBLIC_KEY_PATH;
const privateKeyPath = process.env.PAYGLOCAL_PRIVATE_KEY_PATH;


const publicKey = fs.readFileSync(publicKeyPath!, { encoding: "utf8" });
const privateKey = fs.readFileSync(privateKeyPath!, { encoding: "utf8" });

export const initPayment = async (txId: string, paymentData: PayGLocalTxnData) => {
    try {
        const collectPayload: any = {
            "merchantTxnId": txId,
            "paymentData": paymentData,
            "merchantCallbackURL": callBackURL
        };

        const data = {
            payload: collectPayload,
            publicKey,
            privateKey,
            merchantId: merchantId!,
            privateKeyId: privateKeyId!,
            publicKeyId: publicKeyId!
        };

        const token: any = await generateJWEAndJWS(data);

        const res = await axios.post(baseURL! + paymentInitiatEndpoint, token.jweToken, {
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'text/plain',
                'x-gl-token-external': token.jwsToken
            }
        })

        const result = res.data;

        return {
            order_id: result.gid,
            url: result.data.redirectUrl,
            payment_method: PAYMENT_METHOD.PAYGLOCAL,
            // "payURL": result.data.redirectUrl,
            // "txID": result.data.merchantTxnId,
            // "statusURL": result.data.statusUrl,
            // "gid": result.gid,
            // "message": result.message,
            // "status": result.status,
            // "errors": result.errors,
            // "reasonCode": result.reasonCode,
            // id: result.data.merchantTxnId
        }


    } catch (error) {
        console.error(`Error on init payment payglocal : `, JSON.stringify(error));
        return Promise.reject({
            status: false,
            message: `Error on init payment`,
            error: JSON.stringify(error)
        })
    }

}

const statusCheckPayGlocal = async (gId: string) => {
    try {
        const statusPayload = `/gl/v1/payments/${gId}/status`;
        const payload = await generateJWS({
            payload: statusPayload,
            privateKey,
            privateKeyId,
            merchantId
        });

        // await axios.getAdapter((baseURL+ sta))
        const statusRes = await axios.request({
            method: 'get',
            url: baseURL + statusPayload,
            headers: {
                'x-gl-token-external': payload
            }
        });

        const result = statusRes.data;
        return {
            "amount": result.data.Amount,
            "currency": result.data.Currency,
            "status": result.data.status,
            "txId": result.data.merchantTxnId,
            "gid": result.data.gid
        }
    } catch (error: any) {
        if (error.status == 420 || error.response.data.message == 'Transaction not found') {
            return {
                "amount": null,
                "currency": null,
                "status": 'REQUEST_ERROR',
                "txId": '',
                "gid": error.response.data.gid
            }
        }
        console.error("Error on payglocal status check : ", JSON.stringify(error));
        return Promise.reject({
            status: false,
            message: 'Error on status check'
        })
    }
}

export const validateMerchOrders = async () => {
    try {
        const orders = await query(getPaygOrders, []);

        if (!orders.rowCount) {
            return;
        }

        for (let index = 0; index < orders.rows.length; index++) {
            const element = orders.rows[index];
            const data = await statusCheckPayGlocal(element.payment_ref);

            if (data.status == 'SENT_FOR_CAPTURE') {
                let orderDetail = await query(orderInvoiceQuery, [element.payment_ref]);
                // const invoice_id = await query(getTotalSuccessfulTransactionsForInvoiceNumber, []);
                // orderDetail.rows[0] = { id: Number(invoice_id.rows[0].id) + 1, ...orderDetail.rows[0] };

                if (!orderDetail.rowCount) {
                    logger.error("Transaction Id not found");
                    return;
                } else {
                    const customerDetail = await query(userAddressinvoiceQuery, [element.payment_ref])
                    const invoice = await generatePDF(orderDetail.rows[0], customerDetail.rows[0], 'Placed');

                    await updatePaymentStatus(element.payment_ref, PHONE_PE_STATUS.PAYMENT_SUCCESS.id, invoice, ORDER_STATUS.PLACED);
                    await updateOrderStatus(element.payment_ref);
                    await query(deleteCartItemsByArtistIdQuery, [element.payment_ref]);
                }
            } else if (["ABANDONED", "ISSUER_DECLINE", "CUSTOMER_CANCELLED", "AUTHENTICATION_TIMEOUT", "GENERAL_DECLINE", "REQUEST_ERROR", "SYSTEM_ERROR"].includes(data.status)) {
                await updatePaymentStatus(element.payment_ref, PHONE_PE_STATUS.PAYMENT_ERROR.id, '', ORDER_STATUS.PAY_FAILED);
                await updatePaymentFailed(element.payment_ref);
            }
        }

        return
    } catch (error) {
        logger.error(`Error on updating payglocal payment status : ${error}`);
        return
    }
}

export const validateCourseOrders = async () => {
    try {
        const orders = await query(getPaygCourseOrders, []);

        if (!orders.rowCount) {
            return;
        }

        for (let index = 0; index < orders.rows.length; index++) {
            const element = orders.rows[index];
            const data = await statusCheckPayGlocal(element.merchant_transaction_id);

            if (data.status == 'SENT_FOR_CAPTURE') {
                const invoice = await generateInvoice(element.id, element.user_id, 'COURSE')

                let orderDetail = await query(updatePurchaseStatusQuery, [PHONE_PE_STATUS.PAYMENT_SUCCESS.id, element.merchant_transaction_id, invoice]);

                if (!orderDetail.rowCount) {
                    break;
                }

                const course = await query(getCourseDetailQuery, [element.course_id, element.user_id]);

                if (!course.rowCount) {
                    break;
                }

                await query(insertUserCourseQuery, [element.course_id, element.user_id, element.validity]);
            } else if (["ABANDONED", "ISSUER_DECLINE", "CUSTOMER_CANCELLED", "AUTHENTICATION_TIMEOUT", "GENERAL_DECLINE", "REQUEST_ERROR", "SYSTEM_ERROR"].includes(data.status)) {
                await query(updatePurchaseStatusQuery, [PHONE_PE_STATUS.PAYMENT_ERROR.id, element.merchantTransactionId, null]);
            }
        }

        return
    } catch (error) {
        logger.error(`Error on updating payglocal payment status : ${error}`);
        return
    }
}

export const validateMeetOrders = async () => {
    try {
        const orders = await query(getPaygMeetOrders, []);

        if (!orders.rowCount) {
            return;
        }

        for (let index = 0; index < orders.rows.length; index++) {
            const element = orders.rows[index];
            const data = await statusCheckPayGlocal(element.payment_ref);

            if (data.status == 'SENT_FOR_CAPTURE') {
                let orderDetail = await query(updatePayStatusQuery, [PHONE_PE_STATUS.PAYMENT_SUCCESS.id, element.merchant_transaction_id]);

                if (!orderDetail.rowCount) {
                    logger.error("Transaction Id not found");
                    return;
                }

                const aritstMeets = await query(getArtistMeetByArtIdQuery, [element.artist_meet_id]);

                if (!aritstMeets.rowCount) {
                    logger.error("Transaction Id not found");
                    return;
                }

                const invoice = await generateInvoice(element.id, element.user_id, 'MEET')

                await query(updateMeetScheduledQuery, [element.id, invoice]);


                const calendlyData = await query(getCldlyCheckoutByIDquery, [element.id]);

                if (!calendlyData.rowCount) {
                    logger.error(
                        `Event Id is Invali : ${element.artist_meet_id}`
                    );
                    return
                }

                await query(updateCalendlyStatus, ['PAYMENT_SUCCESS', calendlyData.rows[0].email, calendlyData.rows[0].source_id, calendlyData.rows[0].invite_id, true])

            } else if (["ABANDONED", "ISSUER_DECLINE", "CUSTOMER_CANCELLED", "AUTHENTICATION_TIMEOUT", "GENERAL_DECLINE", "REQUEST_ERROR", "SYSTEM_ERROR"].includes(data.status)) {
                await query(updatePayStatusQuery, [PHONE_PE_STATUS.PAYMENT_ERROR.id, element.merchant_transaction_id]);

                const calendlyData = await query(getCldlyCheckoutByIDquery, [element.id]);

                if (!calendlyData.rowCount) {
                    logger.error(
                        `Event Id is Invali : ${element.artist_meet_id}`
                    );
                    return
                }

                await cancelEvent(calendlyData.rows[0].invite_id)

                await query(updateCalendlyStatus, ['PAYMENT_FAILED', calendlyData.rows[0].email, calendlyData.rows[0].source_id, calendlyData.rows[0].invite_id, false])

                logger.log("Upadted Payment failed meet purchase", element.merchant_transaction_id);
            }
        }

        return
    } catch (error) {
        logger.error(`Error on updating payglocal payment status : ${error}`);
        return
    }
}

export const validatePaidVideoOrders = async () => {
    try {
        const orders = await query(getPaygVideoOrders, []);

        if (!orders.rowCount) {
            return;
        }

        for (let index = 0; index < orders.rows.length; index++) {
            const element = orders.rows[index];
            const data = await statusCheckPayGlocal(element.merchant_transaction_id);

            if (data.status == 'SENT_FOR_CAPTURE') {
                let orderDetail = await query(updatePaymentTxnQuery, [
                    PHONE_PE_STATUS.PAYMENT_SUCCESS.id,
                    element.merchantTransactionId,
                ]);

                if (!orderDetail.rowCount) {
                    break;
                }

                const invoice = await generateInvoice(
                    element.id,
                    element.user_id,
                    "PAID_VIDEO"
                );

                const course = await query(updateVideoPurchasedQuery, [
                    element.id,
                    invoice,
                ]);

                if (!course.rowCount) {
                    break;
                }
            } else if (["ABANDONED", "ISSUER_DECLINE", "CUSTOMER_CANCELLED", "AUTHENTICATION_TIMEOUT", "GENERAL_DECLINE", "REQUEST_ERROR", "SYSTEM_ERROR"].includes(data.status)) {
                await query(updatePaymentTxnQuery, [
                    PHONE_PE_STATUS.PAYMENT_ERROR.id,
                    element.merchantTransactionId,
                ]);
            }
        }

        return
    } catch (error) {
        logger.error(`Error on updating payglocal payment status : ${error}`);
        return
    }
}