import PdfPrinter from 'pdfmake';
import fs from 'fs';
import { CustomerDetails, OD_Products, OrderDetailsPDF } from '../types';
import { checkFileExistsSync, removeFile } from './file';
import logger from '../logger';
import { uploadFile } from './aws';
import path from 'path';
import util from 'util';
import stream from 'stream';
import { PAYPAL_PLATFORM_FEES, REGEX } from '../common/constants';
import { sendEmail } from './mailer';
import { OrderDATA, ProductsEntity, mailAcceptText, mailDeliveredText } from '../admin/mail_accept_text';
const finished = util.promisify(stream.finished);

const fonts = {
    "Roboto": {
        "normal": 'src/fonts/Roboto/Roboto-Regular.ttf',
        "bold": 'src/fonts/Roboto/Roboto-Bold.ttf'
    }
};
const printer = new PdfPrinter(fonts);


// Static Input
let companyDetail = {
    name: 'Metastar Media Pvt Ltd',
    gstNo: '27AAPCM7025F1ZA',
    address_line_one: "302 Kumar Plaza",
    address_line_two: "Kalina Kurla Road",
    address_line_three: "Sanatacruz East",
    city: "Mumbai",
    state: "Maharastra",
    country: "India",
    pincode: 400029
};

const getOrderDataDef = (orderDetail: any) => {
    let itemList: { text: string; border: boolean[]; margin: number[]; alignment: string; fontSize?: number; fillColor?: string; textTransform?: string; }[][] = [];
    orderDetail.products.forEach((el: any) => {
        let items = [
            {
                text: `${el.name}`,
                border: [false, false, false, true],
                margin: [0, 5, 0, 5],
                alignment: 'left',
                fontSize: 11
            },
            {
                text: `${el.qty}`,
                margin: [0, 5, 0, 5],
                alignment: 'center',
                border: [false, false, false, true],
                fontSize: 11
            },
            {
                text: `${el.symbol} ${Number(el.price).toFixed(2)}`,
                margin: [0, 5, 0, 5],
                alignment: 'center',
                border: [false, false, false, true],
                fontSize: 11
            },
        ]
        
        if (orderDetail.discounted > 0) {
            items.push({
                text: el.discounted > 0 ? `${el.symbol} ${el.discounted.toFixed(2)}` : ``,
                margin: [0, 5, 0, 5],
                alignment: 'center',
                border: [false, false, false, true],
                fontSize: 11
            })
        }

        if (el.symbol == '₹') {
            items.push({
                text: `${el.symbol} ${el.gst}`,
                margin: [0, 5, 0, 5],
                alignment: 'center',
                border: [false, false, false, true],
                fontSize: 11
            })
        }

        if (orderDetail.paymentMethod === 'Paypal') {
            const gatewayFee = ((el.price - el.discounted) * PAYPAL_PLATFORM_FEES).toFixed(2);
            items.push({
                text: `${el.symbol} ${gatewayFee}`,
                margin: [0, 5, 0, 5],
                alignment: 'center',
                border: [false, false, false, true],
                fontSize: 11
            });
        }

        items.push({
            border: [false, false, false, true],
            text: `${el.symbol} ${Number(el.total).toFixed(2)}`,
            alignment: 'right',
            margin: [0, 5, 0, 5],
            fontSize: 12
        });

        itemList.push(items);
    })
    return itemList;
}

const getTableDefinition = (symbol: string, discount?: number, paymentMethod?: string) => {
    let items: { text: string; fillColor: string; border: boolean[]; margin: number[]; alignment?: string; textTransform: string; fontSize?: number; }[] = [
        {
            text: 'Product',
            fillColor: '#eaf2f5',
            border: [false, true, false, true],
            margin: [0, 5, 0, 5],
            textTransform: 'uppercase',
        },
        {
            text: 'Qty',
            fillColor: '#eaf2f5',
            border: [false, true, false, true],
            margin: [0, 5, 0, 5],
            alignment: 'center',
            textTransform: 'uppercase',
        },
        {
            text: 'Price',
            fillColor: '#eaf2f5',
            border: [false, true, false, true],
            margin: [0, 5, 0, 5],
            alignment: 'center',
            textTransform: 'uppercase',
        }
    ]

    if (discount! > 0) {
        items.push({
            text: 'Discount',
            fillColor: '#eaf2f5',
            border: [false, true, false, true],
            margin: [0, 5, 0, 5],
            alignment: 'center',
            textTransform: 'uppercase',
        })
    }

    if (symbol == '₹') {
        items.push({
            text: 'GST',
            fillColor: '#eaf2f5',
            border: [false, true, false, true],
            margin: [0, 5, 0, 5],
            alignment: 'center',
            textTransform: 'uppercase',
        })
    }

    if (paymentMethod === 'Paypal') {
        items.push({
            text: 'Payment Gateway Fee',
            fillColor: '#eaf2f5',
            border: [false, true, false, true],
            margin: [0, 5, 0, 5],
            alignment: 'center',
            textTransform: 'uppercase',
            fontSize: 10
        })
    }

    items.push({
        text: 'TOTAL',
        border: [false, true, false, true],
        alignment: 'right',
        fillColor: '#eaf2f5',
        margin: [0, 5, 0, 5],
        textTransform: 'uppercase',
    })

    return items;
}

const getTableWidth = (symbol: string, discount?: number, paymentMethod?: string) => {
    if (discount! > 0 && paymentMethod === 'Paypal') {
        return ['*', 40, 60, '*', '*', 80]
    } else if (paymentMethod === 'Paypal') {
        return ['*', 40, 60, '*', 80]
    } else if (symbol == '₹' && discount! > 0) {
        return ['*', 40, 60, '*', '*', 80]
    } else if (symbol == '₹') {
        return ['*', 40, 60, '*', 80]
    } else if (symbol == '$' && discount! > 0) {
        return ['*', 40, 60, '*', 80]
    } else {
        return ['*', 40, 60, '*']
    }
}

const getTableFooter = (code: string, symbol: string, total: number, paymentMethod?: string) => {
    
    if (code) {
        return {
            def: [
                {
                    text: 'Coupon Applied:',
                    color: '#aaaaab',
                    bold: true,
                    fontSize: 12,
                    alignment: 'left',
                    border: [false, true, false, true],
                    margin: [0, 10, 0, 10],
                    verticalAlignment: 'middle'
                },
                {
                    text: `${code}`,
                    bold: true,
                    fontSize: 12,
                    alignment: 'left',
                    border: [false, true, false, true],
                    margin: [0, 10, 0, 10],
                    verticalAlignment: 'middle'
                },
                {
                    text: 'Total:',
                    bold: true,
                    fontSize: 12,
                    alignment: 'right',
                    border: [false, true, false, true],
                    margin: [0, 10, 0, 10],
                    verticalAlignment: 'middle'
                },
                {
                    text: `${symbol} ${Number(total).toFixed(2)}`,
                    bold: true,
                    fontSize: 20,
                    alignment: 'right',
                    border: [false, true, false, true],
                    margin: [0, 5, 0, 5],
                    verticalAlignment: 'middle'
                },
            ],
            width: ['auto', 'auto', '*', 'auto']
        }
    }

    return {
        width: ['*', 'auto'],
        def: [
            {
                text: 'Total:',
                bold: true,
                fontSize: 12,
                alignment: 'right',
                border: [false, true, false, true],
                margin: [0, 10, 0, 10],
                verticalAlignment: 'middle'
            },
            {
                text: `${symbol} ${Number(total).toFixed(2)}`,
                bold: true,
                fontSize: 20,
                alignment: 'right',
                border: [false, true, false, true],
                margin: [0, 5, 0, 5],
                verticalAlignment: 'middle'
            },
        ]
    }
}

const getPdfDef = (orderDetail: any, customerDetail: any, productDefinition: any) => {
    return {
        content: [
            {
                columns: [
                    {
                        image: 'src/assets/logo.png',
                        width: 150,
                    },
                    [
                        {
                            text: 'INVOICE',
                            color: '#333333',
                            width: '*',
                            fontSize: 28,
                            bold: true,
                            alignment: 'right',
                            margin: [0, 0, 0, 15],
                        },
                        {
                            stack: [
                                {
                                    columns: [
                                        {
                                            text: 'Invoice No : ',
                                            color: '#aaaaab',
                                            bold: true,
                                            width: '*',
                                            fontSize: 12,
                                            alignment: 'right',
                                        },
                                        {
                                            text: `${orderDetail.id}`,
                                            bold: false,
                                            color: '#333333',
                                            fontSize: 12,
                                            alignment: 'right',
                                            width: 100,
                                        },
                                    ],
                                },
                                {
                                    columns: [
                                        {
                                            text: 'Date : ',
                                            color: '#aaaaab',
                                            bold: true,
                                            width: '*',
                                            fontSize: 12,
                                            alignment: 'right',
                                        },
                                        {
                                            text: `${orderDetail.date}`,
                                            bold: true,

                                            color: '#333333',
                                            fontSize: 12,
                                            alignment: 'right',
                                            width: 100,
                                        },
                                    ],
                                },
                                {
                                    columns: [
                                        {
                                            text: 'Payment Method : ',
                                            color: '#aaaaab',
                                            bold: true,
                                            fontSize: 12,
                                            alignment: 'right',
                                            width: '*',
                                        },
                                        {
                                            text: `${orderDetail.paymentMethod}`,
                                            bold: true,
                                            fontSize: 14,
                                            alignment: 'right',
                                            color: 'green',
                                            width: 100,
                                        },
                                    ],
                                },
                            ],
                        },
                    ],
                ],
            },
            {
                columns: [
                    {
                        text: 'From',
                        color: '#aaaaab',
                        bold: true,
                        fontSize: 14,
                        alignment: 'left',
                        margin: [0, 20, 0, 5],
                    },
                    {
                        text: 'To',
                        color: '#aaaaab',
                        bold: true,
                        fontSize: 14,
                        alignment: 'left',
                        margin: [0, 20, 0, 5],
                    },
                ],
            },
            {
                columns: [
                    {
                        text: `${companyDetail.name} \n ${companyDetail.gstNo}`,
                        bold: true,
                        color: '#333333',
                        alignment: 'left',
                    },
                    {
                        text: `${customerDetail.firstName} ${customerDetail.lastName} \n ${customerDetail.email} ${customerDetail.mobile_number ? `\n ${customerDetail.mobile_number}` : ''}`,
                        bold: true,
                        color: '#333333',
                        alignment: 'left',
                    },
                ],
            },
            {
                columns: [
                    {
                        text: 'Address',
                        color: '#aaaaab',
                        bold: true,
                        margin: [0, 7, 0, 3],
                    },
                    {
                        text: customerDetail.address.address_line_one ? 'Address' : '',
                        color: '#aaaaab',
                        bold: true,
                        margin: [0, 7, 0, 3],
                    },
                ],
            },
            {
                columns: [
                    {
                        text: `${companyDetail.address_line_one}, ${companyDetail.address_line_two}, \n ${companyDetail.address_line_three}, ${companyDetail.city} \n   ${companyDetail.state} - ${companyDetail.pincode}`,
                        style: 'invoiceBillingAddress',
                    },
                    {
                        text: customerDetail.address.address_line_one ? `${customerDetail.address.address_line_one}, ${customerDetail.address.address_line_two}, \n ${customerDetail.address.address_line_three}, ${customerDetail.address.city} \n   ${customerDetail.address.state} - ${customerDetail.address.pincode}` : '',
                        style: 'invoiceBillingAddress',
                    },
                ],
            },
            '\n\n',

            {
                layout: {
                    defaultBorder: false,
                    hLineWidth: function (i: any, node: any) {
                        return 1;
                    },
                    vLineWidth: function (i: any, node: any) {
                        return 1;
                    },
                    hLineColor: function (i: number, node: any) {
                        if (i === 1 || i === 0) {
                            return '#bfdde8';
                        }
                        return '#eaeaea';
                    },
                    vLineColor: function (i: any, node: any) {
                        return '#eaeaea';
                    },
                    hLineStyle: function (i: any, node: any) {
                        return null;
                    },
                    paddingLeft: function (i: any, node: any) {
                        return 10;
                    },
                    paddingRight: function (i: any, node: any) {
                        return 10;
                    },
                    paddingTop: function (i: any, node: any) {
                        return 2;
                    },
                    paddingBottom: function (i: any, node: any) {
                        return 2;
                    },
                    fillColor: function (rowIndex: any, node: any, columnIndex: any) {
                        return '#fff';
                    },
                },
                table: {
                    headerRows: 1,
                    widths: getTableWidth(orderDetail.symbol, orderDetail.discounted ? orderDetail.discounted : 0, orderDetail.paymentMethod),
                    body: [
                        getTableDefinition(orderDetail.symbol, orderDetail.discounted ? orderDetail.discounted : 0, orderDetail.paymentMethod),
                        ...productDefinition,
                    ],
                },
            },
            '\n',
            '\n\n',
            {
                layout: {
                    defaultBorder: false,
                    hLineWidth: function (i: any, node: any) {
                        return 1;
                    },
                    vLineWidth: function (i: any, node: any) {
                        return 1;
                    },
                    hLineColor: function (i: any, node: any) {
                        return '#eaeaea';
                    },
                    vLineColor: function (i: any, node: any) {
                        return '#eaeaea';
                    },
                    hLineStyle: function (i: any, node: any) {
                        return null;
                    },
                    paddingLeft: function (i: any, node: any) {
                        return 10;
                    },
                    paddingRight: function (i: any, node: any) {
                        return 10;
                    },
                    paddingTop: function (i: any, node: any) {
                        return 3;
                    },
                    paddingBottom: function (i: any, node: any) {
                        return 3;
                    },
                    fillColor: function (rowIndex: any, node: any, columnIndex: any) {
                        return '#fff';
                    },
                },
                table: {
                    headerRows: 1,
                    widths: getTableFooter(orderDetail.coupon_code, orderDetail.symbol, orderDetail.total, orderDetail.paymentMethod).width,
                    body: [
                        getTableFooter(orderDetail.coupon_code, orderDetail.symbol, orderDetail.total, orderDetail.paymentMethod).def
                    ],
                },
            },
            '\n\n',
            {
                text: 'NOTES',
                style: 'notesTitle',
            },
            {
                text: 'Thank you for purchase! \n This is software generated invoice, signature not required' + `\n${orderDetail.symbol == '$' ? `SUPPLY MEANT FOR EXPORT UNDER LETTER OF UNDERTAKING WITHOUT PAYMENT OF IGST` : ''}`,
                style: 'notesText',
            },
        ],
        styles: {
            notesTitle: {
                fontSize: 10,
                bold: true,
                margin: [0, 50, 0, 3],
            },
            notesText: {
                fontSize: 10,
            },
        },
        defaultStyle: {
            columnGap: 20,
            //font: 'Quicksand',
        },
    };
}

export const getProductArr = (data: OD_Products[]) => {
    const result: any[] = data.map(el => {
        return {
            name: el.name,
            imageUrl: el.image,
            quantity: el.qty,
            price: el.price,
            symbol: el.symbol,
            size: el.size
        }
    })
    return result as ProductsEntity[];
}


export const generatePDF = async (
    orderDetail: OrderDetailsPDF, customerDetail: CustomerDetails,
    status: string, sendMail: boolean = true, subject?: any, body?: any,
    type?: String
) => {
    try {
        const orderDataDef = getOrderDataDef(orderDetail);

        const docDefinition = getPdfDef(orderDetail, customerDetail, orderDataDef);
        const fileName = `invoice_${Date.now()}_${orderDetail.id}.pdf`;

        // @ts-ignore
        const pdfDoc = printer.createPdfKitDocument(docDefinition);
        const writeStream = fs.createWriteStream(`output/${fileName}`);
        pdfDoc.pipe(writeStream);
        pdfDoc.end();
        await finished(writeStream);

        const localPath = path.join(__dirname, `../../output/${fileName}`);

        if (!checkFileExistsSync(localPath)) {
            logger.error('File Not found');
            return "";
        }
        const upload = await uploadFile(fileName, localPath, 'invoices');



        if (customerDetail.email && REGEX.EMAIL.test(customerDetail.email) && sendMail) {
            let sub = subject ? subject : 'Purchase Invoice';
            let data: OrderDATA = {
                customerName: customerDetail.firstName + ' ' + customerDetail.lastName,
                total: orderDetail.total,
                address: {
                    line1: customerDetail.address.address_line_one,
                    line2: customerDetail.address.address_line_two,
                    line3: customerDetail.address.address_line_three,
                    city: customerDetail.address.city,
                    state: customerDetail.address.state,
                    pincode: customerDetail.address.pincode
                },
                products: getProductArr(orderDetail.products)
            }
            let html = body;
            let subText = `<p>We&rsquo;re getting your order ready to be shipped. We will notify you when it has been sent. The ideal time
                required to ship your order is 2-5 working days.</p>`;

            if (type) {
                if (type == 'MEET' || type == 'PAID_VIDEO' || type == 'COURSE') {
                    subText = `<p>Thank you for your purchase! We're excited to confirm your subscription to ${data.products[0].name}.</p>`
                }
            }
            data.subText = subText;
            if (status == 'Placed' && !subject) {
                sub = 'Your Order is Confirmed!'
                html = mailAcceptText(data)
            } else if (status == 'Delivered' && !subject) {
                sub = 'Your Order has been Delivered!'
                html = mailDeliveredText(data)
            }
            const localeFileContent = fs.readFileSync(localPath);
            const attachments: any[] = [
                {
                    filename: 'Invoice.pdf',
                    content: localeFileContent,
                    encoding: 'base64',
                    contentType: 'application/pdf',
                }
            ];
            await sendEmail([customerDetail.email], sub, html, undefined, attachments)
        }

        removeFile(localPath);
        return upload;
    } catch (error) {
        logger.error(`Error on generating odf invoce : ${error}`);
        return ''
    }
}


// // Dynamic Input based on customer order
// const orderDetail = {
//     id: 12345,
//     date: 'Dec 15, 2023',
//     paymentMethod: 'UPI',
//     products: [
//         {
//             name: "Hoddie",
//             price: "999",
//             qty: 2,
//             total: 1998,
//             symbol: '₹'
//         },
//         {
//             name: "T-Shirt",
//             price: "959",
//             qty: 2,
//             total: 1918,
//             symbol: '₹'
//         }
//     ],
//     symbol: '₹',
//     total: 3916
// };

// // Dynamic Input based on customer order
// const customerDetail = {
//     firstName: 'Veerapandi',
//     lastName: 'S',
//     email: '<EMAIL>',
//     phone: '+91 99887 76655',
//     address: {
//         address_line_one: "55",
//         address_line_two: "Electric Sector Street",
//         address_line_three: "Havana",
//         city: "Chennai",
//         state: "Tamil Nadu",
//         country: "India",
//         pincode: 600048
//     }
// }

// generatePDF(orderDetail, customerDetail);