import { OrderBody } from "../types/db_types";
import { query } from "../db"
import { CURRENCIES, ORDER_STATUS, PHONE_PE_STATUS } from "../common/constants";
import { deleteCartItemsByArtistIdQuery, orderInvoiceQuery, transactionBegin, transactionCommit, transactionRollback, userAddressinvoiceQuery } from "../common/db_constants";
import logger from "../logger";
import { paymentStatusCheck } from "./pg";
import { generatePDF } from "./pdf";

const getUserCartQuery = `select * from shopping_cart where user_id = $1 and artist_id = $2;`

const upsertUserCartQuery = `insert into shopping_cart(user_id,artist_id)
values($1,$2)
on conflict on constraint unq_cart do nothing
returning *`;

const addToCartQuery = `insert into shopping_cart_items(cart_id,product_item_id,quantity,payment_currency)
values($1,$2,$3,$4)
on conflict on constraint unq_cart_itm do update set
quantity = shopping_cart_items.quantity::integer + $3::integer,
payment_currency = coalesce(shopping_cart_items.payment_currency,$4),
updated = now()
returning *`;

const deleteCartItmQuery = `delete from shopping_cart_items where cart_id = $1 and  product_item_id = $2;`;

const userCartItemsQuery = `select pi.id as product_item_id,array[pi.image_url] images,pi.title "name",pi.price price_per_quantity,sci.quantity cart_quantity,p.tax from shopping_cart_items sci
inner join shopping_cart sc on sc.id = sci.cart_id
inner join product_item pi on pi.id = sci.product_item_id
INNER JOIN products p 
  ON (
    (pi.product_type != 2 AND p.id = pi.product_id)
    OR
    (pi.product_type = 2 AND p.id = pi.course_product_id)
  )
where (sc.user_id = $1) and (sc.artist_id = $2 or $2 is null)
order by sci.created asc`;

const createOrderDetailQuery = `insert into order_details(user_id,total_items,payment_ref,payment_status,payment_method,address_id,order_status,purchased_price,payment_currency,offer_id,base_price)
values ($1,$2,$3,2,$8,$4,1,$5, $6, $7, $9)
returning id;`;

const addOrderItemsQuery = `insert into order_items(order_id,product_item_id,quantity,purchased_price,price_per_quantity,base_price)
values ($1,$2,$3,$4,$5,$6)
returning *`;

const addOrderHistoryQuery = `insert into order_status_history(order_item_id,status_id)
values ($1,$2)
returning *`;

const getCartItemsByArtistIdQuery = `
SELECT 
  sci.product_item_id AS "productItemId",
  sci.product_item_id,
  COALESCE(ARRAY[pi.image_url], ARRAY[]::text[]) AS images,
  COALESCE(pi.title, 'Product Not Found') AS "name",
  sci.quantity AS cart_quantity,
  COALESCE((pp.price * sci.quantity), 0) AS price,
  COALESCE(pp.price, 0) AS price_per_qty,
  COALESCE(pp.gst, 0) AS tax,
  COALESCE(pcr.name, 'Unknown Currency') AS currency,
  pcr.id AS currency_id,
  sci.quantity,
  sc.artist_id AS artist_id,
  COALESCE(pcr.symbol, '') AS currency_symbol,
  pi.product_type,
  CASE 
    WHEN pi.product_type = 2 THEN cd.course_level
    ELSE NULL
  END AS course_level,
  COALESCE(
    array_agg(
      jsonb_build_object(
        COALESCE(v.short_name, ''), 
        COALESCE(vo.value, '')
      )
    ) FILTER (WHERE v.short_name IS NOT NULL OR vo.value IS NOT NULL),
    ARRAY[]::jsonb[]
  ) AS variants,
  (pcp.id IS NOT NULL) AS discount_enabled,
  CASE 
    WHEN cc.id IS NOT NULL THEN
      jsonb_build_object(
        'unlimited', cc.unlimited,
        'total_limit', cc.total_limit,
        'user_limited', cc.user_limited,
        'user_limit', cc.user_limit,
        'code', cc.code,
        'all_products', cc.all_products,
        'discount', cc.discount,
        'id', cc.id
      )
    ELSE NULL
  END AS coupon

FROM shopping_cart_items sci
INNER JOIN shopping_cart sc ON sc.id = sci.cart_id
LEFT JOIN product_item pi ON pi.id = sci.product_item_id
LEFT JOIN product_prices pp ON
  pp.currency = sci.payment_currency AND
  (
    (pi.product_type = 1 AND pp.product_id = pi.product_id) OR
    (pi.product_type = 2 AND pp.course_id = pi.product_id) OR
    (pi.product_type = 3 AND pp.video_id = pi.product_id)
  )
LEFT JOIN payment_currency pcr ON pcr.id = pp.currency
LEFT JOIN product_coupons pcp ON pcp.product_id = pi.product_id
LEFT JOIN coupon_codes cc ON cc.id = pcp.coupon_id
LEFT JOIN product_configuration pc ON pc.product_item_id = pi.id
LEFT JOIN variation_option vo ON vo.id = pc.variation_option_id
LEFT JOIN variation v ON v.id = vo.variation_id
LEFT JOIN course_details cd ON cd.id = pi.product_id AND pi.product_type = 2
WHERE sc.user_id = $1 
  AND (sc.artist_id = $2 OR $2 IS NULL) 
  AND (sci.payment_currency = $3 OR $3 IS NULL)
GROUP BY 
  sci.product_item_id, 
  pi.id,
  pi.image_url, 
  pi.title, 
  sci.quantity, 
  pp.id,
  pp.price, 
  pp.gst, 
  pcr.name, 
  pcr.id, 
  pcr.symbol,
  pcp.id, 
  cc.id, 
  cc.unlimited,
  cc.total_limit,
  cc.user_limited,
  cc.user_limit,
  cc.code,
  cc.all_products,
  cc.discount,
  sc.artist_id,
  pi.product_type,
  cd.course_level
ORDER BY sci.product_item_id;
`;

const updateOrderStatusQuery = `update order_status_history
set status_id = (case when sq.is_virtual = false then $2 else $3 end)::integer,
updated = now()
from (
	select oi.id,p.is_virtual from order_items oi
	inner join product_item pi on pi.id = oi.product_item_id
	INNER JOIN products p 
      ON (
        (pi.product_type != 2 AND p.id = pi.product_id)
        OR
        (pi.product_type = 2 AND p.id = pi.course_product_id)
      )
	inner join order_details od on od.id = oi.order_id
	where payment_ref = $1
) sq
where order_item_id = sq.id`;

const updatePayFailedQuery = `update order_status_history
set status_id = $2,
updated = now()
from (
	select oi.id from order_items oi
	inner join product_item pi on pi.id = oi.product_item_id
	INNER JOIN products p 
        ON (
          (pi.product_type != 2 AND p.id = pi.product_id)
          OR
          (pi.product_type = 2 AND p.id = pi.course_product_id)
        )
	inner join order_details od on od.id = oi.order_id
	where payment_ref = $1
) sq
where order_item_id = sq.id`;

const updatePaymentStatusQuery = `update order_details
set payment_status = $2,
invoice_url = $3,
order_status = $4,
updated = now()
where payment_ref = $1 and payment_status != $2
returning *;`;

export const upsertUserCart = async (userId: number, artistId: number) => {
    try {
        let userCart = await query(getUserCartQuery, [userId, artistId]);
        if (userCart.rowCount) {
            return userCart.rows[0];
        }

        userCart = await query(upsertUserCartQuery, [userId, artistId]);
        return userCart.rows[0];
    } catch (error) {
        return Promise.reject({
            status: false,
            message: "Error in upserting cart",
            rawError: error
        })
    }
}

export const updateCartItem = async (cartId: number, productItemId: number, qty: number, currency?: number) => {
    try {
        let userCart;
        if (qty == 0) {
            userCart = await query(deleteCartItmQuery, [cartId, productItemId]);
        } else {
            userCart = await query(addToCartQuery, [cartId, productItemId, Number(qty), currency ? currency : CURRENCIES.INR]);
        }


        return userCart.rows;
    } catch (error) {
        return Promise.reject({
            status: false,
            message: "Error in updating cart items",
            rawError: error
        })
    }
}

export const getCartItems = async (userId: number, artistId?: any) => {
    try {
        const cartItems = await query(userCartItemsQuery, [userId, artistId]);
        return cartItems.rows
    } catch (error) {
        return Promise.reject({
            status: false,
            message: "Error in getting cart items",
            rawError: error
        })
    }
}

export const createOrderDb = async (data: OrderBody[], userId: number, pRef: string, addressId: number, totalAmount: number, currency: number, paymentMethod: number, baseAmount: number, offerId?: number) => {
    try {

        const orderDetail = await query(createOrderDetailQuery, [userId, data.length, pRef, addressId, totalAmount, currency, offerId, paymentMethod, baseAmount]);

        if (!orderDetail.rowCount) {
            return Promise.reject({
                status: false,
                message: "Error in creating order detail",
            })
        }

        for (let i = 0; i < data.length; i++) {
            const orderItem = await query(addOrderItemsQuery, [orderDetail.rows[0].id, data[i].productItemId, data[i].quantity, data[i].price, data[i].price_per_qty, data[i].original_price]);
            if (!orderItem.rowCount) {
                return Promise.reject({
                    status: false,
                    message: "Error in creating order item",
                })
            }
            const orderHistory = await query(addOrderHistoryQuery, [orderItem.rows[0].id, ORDER_STATUS.CREATED]);
            if (!orderHistory.rowCount) {
                return Promise.reject({
                    status: false,
                    message: "Error in creating order item",
                })
            }
        }

        return Promise.resolve({
            status: true,
            message: "Order Created",
            data: orderDetail.rows[0].id
        })
    } catch (error) {
        return Promise.reject({
            status: false,
            message: "Error in getting cart items",
            rawError: error
        })
    }
}

export const getCartItemsByArtistId = async (userId: number, artistId: number | null, currency?: number) => {
    try {
        const cartItems = await query(getCartItemsByArtistIdQuery, [userId, artistId, currency ? currency : null]);
        return cartItems.rows;
    } catch (error) {
        logger.error("Error in getting cart items : ", error, "user id - : ", userId);
        return Promise.reject({
            status: false,
            message: "Error in getting cart items",
        })
    }
}

export const updateOrderStatus = async (mTxnId: string) => {
    try {
        const data = await query(updateOrderStatusQuery, [mTxnId, ORDER_STATUS.PLACED, ORDER_STATUS.DELIVERED]);
        return data.rowCount;
    } catch (error) {
        console.error("Error on updating status : ", error, "mTxnId : ", mTxnId);
        return;
    }
}

export const updatePaymentFailed = async (mTxnId: string) => {
    try {
        const data = await query(updatePayFailedQuery, [mTxnId, ORDER_STATUS.PAY_FAILED]);
        return data.rowCount;
    } catch (error) {
        console.error("Error on updating status : ", error, "mTxnId : ", mTxnId);
        return;
    }
}

export const updatePaymentStatus = async (mTxnId: string, pStatus: number, invoice: string, oStatus: number) => {
    try {
        const data = await query(updatePaymentStatusQuery, [mTxnId, pStatus, invoice, oStatus]);
        return data.rowCount;
    } catch (error) {
        console.error("Error on updating status : ", error, "mTxnId : ", mTxnId);
        return;
    }
}

// const getOrdersQuery = `select id,payment_ref from order_details where created between now() - interval '20 mins' and now() and payment_status = 2 and payment_ref is not null;`;

const timedOutOrdersQuery = `select id,payment_ref from order_details where (created < now() - interval '10 mins') and (payment_method = 1) and (payment_status = 2) and (payment_ref is not null) limit 1;`

export const processOrders = async () => {
    try {
        // const ordersRows = await query(getOrdersQuery, []);
        // const orders = ordersRows.rows;
        // if (orders.length) {
        //     for (let i = 0; i < orders.length; i++) {
        //         const paymentStatus = await paymentStatusCheck(orders[i].payment_ref);

        //         if (paymentStatus.code == PHONE_PE_STATUS.PAYMENT_SUCCESS.name) {
        //             let orderDetail = await query(orderInvoiceQuery, [orders[i].payment_ref]);

        //             if (!orderDetail.rowCount) {
        //                 logger.error("Transaction Id not found");
        //                 return;
        //             } else {
        //                 const customerDetail = await query(userAddressinvoiceQuery, [orders[i].payment_ref])
        //                 const invoice = await generatePDF(orderDetail.rows[0], customerDetail.rows[0]);


        //                 await query(transactionBegin, []);
        //                 await updatePaymentStatus(orders[i].payment_ref, PHONE_PE_STATUS.PAYMENT_SUCCESS.id, invoice, ORDER_STATUS.PLACED);
        //                 await updateOrderStatus(orders[i].payment_ref);
        //                 await query(transactionCommit, []);
        //             }
        //         }
        //     }
        // }

        const timeoutOrdersRows = await query(timedOutOrdersQuery, []);
        const timeoutOrders = timeoutOrdersRows.rows;
        if (timeoutOrders.length) {
            await query(transactionBegin, []);

            for (let i = 0; i < timeoutOrders.length; i++) {
                const paymentStatus = await paymentStatusCheck(timeoutOrders[i].payment_ref);

                if (paymentStatus.code == PHONE_PE_STATUS.PAYMENT_SUCCESS.name) {
                    let orderDetail = await query(orderInvoiceQuery, [timeoutOrders[i].payment_ref]);
                    // const invoice_id = await query(getTotalSuccessfulTransactionsForInvoiceNumber, []);
                    // orderDetail.rows[0] = { id: Number(invoice_id.rows[0].id) + 1, ...orderDetail.rows[0] };

                    if (!orderDetail.rowCount) {
                        logger.error("Transaction Id not found");
                        return;
                    } else {
                        const customerDetail = await query(userAddressinvoiceQuery, [timeoutOrders[i].payment_ref])
                        const invoice = await generatePDF(orderDetail.rows[0], customerDetail.rows[0], 'Placed');

                        await updatePaymentStatus(timeoutOrders[i].payment_ref, PHONE_PE_STATUS.PAYMENT_SUCCESS.id, invoice, ORDER_STATUS.PLACED);
                        await updateOrderStatus(timeoutOrders[i].payment_ref);
                        await query(deleteCartItemsByArtistIdQuery, [timeoutOrders[i].payment_ref]);
                    }
                } else if (paymentStatus.code == PHONE_PE_STATUS.TIMED_OUT.name || paymentStatus.code == PHONE_PE_STATUS.PAYMENT_ERROR.name || paymentStatus.code == PHONE_PE_STATUS.PAYMENT_DECLINED.name) {
                    const pymnt = await updatePaymentStatus(timeoutOrders[i].payment_ref, PHONE_PE_STATUS.PAYMENT_ERROR.id, '', ORDER_STATUS.PAY_FAILED);
                    await updatePaymentFailed(timeoutOrders[i].payment_ref);
                    console.error("Upadted Payment Count : ", pymnt);

                }
            }

            await query(transactionCommit, []);
        }
    } catch (error) {
        await query(transactionRollback, []);
        logger.error(`Error on updating payment status : ${error}`);
        return
    }
}

const updateStatusQuery = 'update order_details set order_status = $1,updated = now() where id = $2 returning *;'

export const getOrdersQuery = 'select * from order_details where id = any($1);'

export const updateOrderStatusDB = async (data: any[]) => {
    try {
        await query(transactionBegin, []);

        for (let i = 0; i < data.length; i++) {
            const update = await query(updateStatusQuery, [data[i].statusId, data[i].id]);
            if (!update.rowCount) {
                data
                await query(transactionRollback, []);
                return Promise.reject({
                    status: false,
                    message: 'Error on updating order status'
                });
            }
        }

        await query(transactionCommit, []);

        return Promise.resolve({
            status: true,
            message: 'Success'
        })
    } catch (error) {
        await query(transactionRollback, []);
        return Promise.reject({
            status: false,
            message: 'Error on updating order status'
        });
    }
}