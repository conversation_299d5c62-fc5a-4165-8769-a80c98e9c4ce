import { makePostCall } from "../common";

export const sendMobileOtp = async (mobile: string, otp: number) => {
    try {
        const url = `https://control.msg91.com/api/v5/flow/`
        const payload = {
            "template_id": "67601b8ad6fc0523a5258792",
            "short_url": "0",
            "recipients": [
                {
                    "mobiles": mobile,
                    "OTP": otp
                }
            ]
        };
        const config = {
            headers: {
                "authkey": "408293AxZMFfLzf36536464aP1"
            }
        }
        const apiRes = await makePostCall(url, payload, config);

        return apiRes;
    } catch (error) {
        
    }   
}