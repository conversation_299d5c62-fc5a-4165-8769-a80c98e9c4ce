import { createJ<PERSON>TToken } from "../common";
import { getArtistAlertBody, getArtistReqAlertSubject, getUserAcknowldgementSub, getUserRequestSavedBody, AckEmail, BGAckEmail } from "../common/constants";
import { getRequestByArtistIdQuery, getRequestQuery, saveRequestQuery, updateRequestQuery } from "../common/db_constants";
import { query } from "../db";
import { sendEmail } from "./mailer";

export const MEET_REQ_STATUS = {
    REQUESTED: 'REQUESTED',
    REJECTED: 'REJECTED',
    ACCEPTED: 'ACCEPTED'
}

export const saveMeetRequest = async (reqData: any, id: number, userId: number, meetStatus : string = MEET_REQ_STATUS.REQUESTED) => {
    try {
        const data = await query(saveRequestQuery, [userId, id, reqData, meetStatus]);
        if (!data.rowCount) {
            return {
                status: false,
                message: "Error in saving data"
            }
        }
        return {
            status: true,
            message: "DB Entry Done",
            id: data.rows[0].id
        };

    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Couldn't Save Entry`,
            error: error
        })
    }
}

export const getMeetRequestById = async (id: string) => {
    try {
        const data = await query(getRequestQuery, [id]);
        if (!data.rowCount) {
            return {
                status: false,
                message: "Error in saving data"
            }
        }
        return {
            status: true,
            message: "DB Entry Done",
            data: data.rows[0]
        };
    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Couldn't Save Entry`,
            error: error
        })
    }
}

export const getMeetRequestByArtistDetailId = async (ids: string[], user_id: number, status: string[]) => {
    try {
        const data = await query(getRequestByArtistIdQuery, [ids, user_id, status]);
        return {
            status: true,
            message: "DB Entry Done",
            data: data.rows
        };
    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Couldn't Save Entry`,
            error: error
        })
    }
}

export const updateRequestStatus = async (id: string, status: string, feedback?: string) => {
    try {
        const data = await query(updateRequestQuery, [status, id, feedback]);
        if (!data.rowCount) {
            return {
                status: false,
                message: "Updated"
            }
        }
        return {
            status: true,
            message: "DB Entry Done",
            id: data.rows[0].id,
            user_id: data.rows[0].user_id
        };
    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Couldn't update Entry`,
            error: error
        })
    }
}

export const sendMailToArtist = async (
    meetName: string, reqId: string, artistEmail: string[],
    data: Object, meetId: number,
    meetType: number
) => {
    try {
        const tokenPayLoad = {
            is_artist: true,
            reqId
        };

        const token = createJWTToken(tokenPayLoad);

        if (meetId === 9) {
            meetName = 'Individual Collaboration';
        }
        if (meetId === 10) {
            meetName = 'Group Collaboration';
        }


        const html = getArtistAlertBody(process.env.BC_BASE_URL || '', reqId, meetName, token, data, meetType);

        const subject = getArtistReqAlertSubject();

        const email = await sendEmail(artistEmail, subject, html);

        if (email !== "success") {
            console.error("Error sending artist request alert mail:", email);
        }

        return;
    } catch (error) {
        console.error("Exception in sendMailToArtist:", error);
        return Promise.reject({
            status: false,
            message: `Couldn't send artist alert mail`,
            error: error
        });
    }
}

export const sendUserAcknoledgement = async (userEmail: string, userName: string, artistName : string,meetName: string) => {
    try {
        const html = getUserRequestSavedBody(userName, artistName,meetName);

        const email = await sendEmail([userEmail], getUserAcknowldgementSub, html);

        if (email != "success") {
            console.error("Error on sending meet request user acknowldgement", email);
        }

        return;
    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Couldn't send meet request user acknowldgement`,
            error: error
        })
    }
}

export const sendUserRequestStatusUpdate = async (
    toEmail: string, status: string, userName: string,
    artistName: string, linkUrl: string, id: number, meetName: string,
    feedBack?: string,

) => {
    try {


        let html = AckEmail.rejectedBody(userName, artistName, feedBack!);
        let subject = AckEmail.rejectedSubject;
        if (id == 9 || id == 10) {
            html = BGAckEmail.rejectBody(userName, artistName, meetName);
            subject = BGAckEmail.rejectedSubject;
        }
        if (status == MEET_REQ_STATUS.ACCEPTED) {
            html = AckEmail.acceptedBody(userName, linkUrl, artistName);
            subject = AckEmail.acceptedSubject;
            if (id == 9) {
                subject = BGAckEmail.acceptSubject;
                html = BGAckEmail.acceptBody(userName, artistName, meetName, linkUrl)
            }
            if (id == 10) {
                subject = BGAckEmail.acceptSubject;
                html = BGAckEmail.acceptBody2(userName, artistName, meetName, linkUrl)
            }
        }

        const email = await sendEmail([toEmail], subject, html);

        if (email != "success") {
            console.error("Error on sending meet request user status update mail", email);
        }
        return;
    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Couldn't send meet request user status update mail`,
            error: error
        })
    }
}