import AWS from 'aws-sdk';
import fs from 'fs';
import logger from '../logger';
import mime from 'mime-types';
import axios from 'axios'

export const uploadFile = async (fileName: string, filePath: string, folderName: string) => {
    const BUCKET_NAME = 'metastar-superapp';

    if (!fs.existsSync(filePath)) {
        logger.error(`File Not Found in the given path ${filePath}`)
    }

    const fileContent = fs.readFileSync(filePath)
    const ContentType = mime.contentType(fileName) || '';

    const s3 = new AWS.S3({
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY
    });

    const params = {
        Bucket: BUCKET_NAME,
        Key: `${folderName}/${fileName}`,
        Body: fileContent,
        ACL: 'bucket-owner-full-control',
        ContentType: ContentType
    }

    try {
        await s3.putObject(params).promise();
        const url = `https://d2b4rv4q8lb0q0.cloudfront.net/${folderName}/${fileName}`
        return url;
    } catch (error) {
        logger.error("Error on uploding file on s3 : ", error);
        return Promise.reject({
            status: false,
            message: "Error in uploading file",
            rawError: error
        });
    }
}
export const CLOUDFRONT_DOMAIN = 'assets.artisteverse.com';
const keyPairId = process.env.AWS_CLOUDFLARE_KEY_PAIRID;
const privateKeyPath = process.env.AWS_CLOUDFLARE_PRIVATE_KEY;
if (!keyPairId || !privateKeyPath) {
    throw 'ENV not available for AWS'
}
const privateKey = fs.readFileSync(privateKeyPath, "utf8");

const cloudfront = new AWS.CloudFront.Signer(keyPairId, privateKey);

export const generateSignedCookies = (path: string, expiresInSeconds: number) => {
    try {        
        const encodedPath = path.split('/')
            .map(component => encodeURIComponent(component))
            .join('/');
        
        const resourceUrl = `https://${CLOUDFRONT_DOMAIN}/${encodedPath}`;
        
        const policy = JSON.stringify({
            Statement: [
                {
                    Resource: resourceUrl,
                    Condition: {
                        DateLessThan: {
                            "AWS:EpochTime": Math.floor((Date.now() + expiresInSeconds * 1000) / 1000)
                        }
                    }
                }
            ]
        });
        const cookies = cloudfront.getSignedCookie({ policy });
        
        return cookies;
    } catch (error) {
        console.error({ error })
        return null;
    }
}


export const generateSignedUrl = (resourcePath: string, expiresInSeconds: number): string | null => {
    try {
        // Construct the full CloudFront URL for the resource
        const resourceUrl = `https://${CLOUDFRONT_DOMAIN}/${encodeURIComponent(resourcePath).replace(/^\//, '').replace(/%2F/g, '/')}`; // Ensure no leading slash
        
        // Generate the signed URL
        const signedUrl = cloudfront.getSignedUrl({
            url: resourceUrl,
            expires: Math.floor((Date.now() + expiresInSeconds * 1000) / 1000), // Expiration time in Unix epoch seconds
        });

        return signedUrl;
    } catch (error) {
        logger.error("Error generating CloudFront signed URL:", error); // Use logger for errors
        return null;
    }
};



export async function getSignedPlaylistWithSegments(
  signedM3u8Url: string,
  expiresInSeconds = 1800
): Promise<string > {
  try {
    // 1. Fetch the original playlist
    const { data: m3u8Content } = await axios.get<string>(signedM3u8Url);

    // 2. Get the base path of the segments
    const url = new URL(signedM3u8Url);
    const basePath = url.pathname.substring(0, url.pathname.lastIndexOf('/') + 1); // e.g., /videos/abc/

    // 3. Rewrite the segment lines with signed URLs
    const lines = m3u8Content.split('\n');
    const signedLines = lines.map((line: string): string => {
      const trimmed = line.trim();
      if (
        trimmed &&
        !trimmed.startsWith('#') &&
        (trimmed.endsWith('.ts') || trimmed.endsWith('.m3u8'))
      ) {
            const safeBasePath = decodeURIComponent(basePath);
            const rawPath = `${safeBasePath}${trimmed}`.replace(/^\//, '');
            const encodedPath = encodeURI(rawPath);

            const signedSegmentUrl = generateSignedUrl(rawPath, expiresInSeconds);
            if (!signedSegmentUrl) {
                throw new Error(`Failed to generate signed URL for segment: ${encodedPath}`);
            }
            return signedSegmentUrl;
      }
      return line;
    });

    return signedLines.join('\n');
  } catch (error) {
    console.error('Error in getSignedPlaylistWithSegments:', error);
    throw new Error('Failed to fetch or rewrite playlist');
  }
}
