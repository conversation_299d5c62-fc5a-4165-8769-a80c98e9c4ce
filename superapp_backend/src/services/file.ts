import download from 'download';
// import { Storage } from '@google-cloud/storage';
import fs from 'fs';

// const storage = new Storage();

import path from 'path';
import logger from '../logger';
import { promisify } from 'util';

const statAsync = promisify(fs.stat);


export const checkDir = (dir: string) => {
    try {
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
    } catch (error) {
        logger.error("Error In Check Dir", error);
        throw "Error in checking DIR"
    }
}

export const checkFileExistsSync = (filePath: string) => {
    return fs.existsSync(filePath);
}

export const removeFile = (filePath: string) => {
    if (!checkFileExistsSync(filePath)) {
        return null;
    }
    fs.unlink(filePath, (err) => {
        if (err) {
            logger.error('Error deleting the file:', err);
            return;
        }
    });

    return
}

const getFileNameFromUrl = (urlString: string) => {
    const parsedUrl = new URL(urlString);
    return path.basename(parsedUrl.pathname);
}

export const downloadFile = async (url: string, destination: string) => {
    try {
        const fileName = getFileNameFromUrl(url);
        const filePath = destination + fileName;
        if (checkFileExistsSync(filePath)) {
            return filePath;
        };
        await download(url, destination);
        return filePath;
    } catch (error) {
        return Promise.reject({
            status: false,
            message: "Error in downloading file",
            rawError: error
        })
    }
}

// export const uploadFileGCP = async (bucketName: string, filename: string, destination: string) => {
//     try {
//         // Uploads a file to the bucket
//         const result = await storage.bucket(bucketName).upload(filename, {
//             destination: destination,
//         });

//         return result;
//     } catch (error) {
//         return Promise.reject({
//             status: false,
//             message: "Error in uploading file",
//             rawError: error
//         })
//     }
// }

export const getFileSize = async (filename: string) => {
    try {
        const stats = await statAsync(filename);
        return stats.size; // Size of the file in bytes
    } catch (error) {
        console.error('Error getting file size:', error);
        throw error;
    }
}