import ffmpeg from 'fluent-ffmpeg';
import logger from '../logger';
import { query } from '../db';
import { getArtistTracksQuery, getTrackQuery, saveRecordQuery } from '../common/karaoke_constants';
import { downloadFile } from './file';

export const baseURL = 'https://storage.googleapis.com/planetbickram/';

export const bucketName = 'planetbickram';

export const karaokeOutDir = 'karaoke/output/'

export const karaokeTrackDir = 'src/assets/';

export const karaokeLocalOutDir = 'output/';


export const mixAudio = async (file1: string, file2: string, output: string) => {

    return new Promise((resolve, reject) => {
        // Use FFmpeg to mix the audio files
        ffmpeg()
            .input(file1)
            .input(file2)
            .complexFilter([
               // Reduce volume of file1 to 0.5 (50%)
               { filter: 'volume', options: '0.5', inputs: '[0:a]', outputs: 'file1_volume' },
               // Increase volume of file2 to 1.5 (150%)
               { filter: 'volume', options: '1.5', inputs: '[1:a]', outputs: 'file2_volume' },
               // Mix the adjusted volumes
               { filter: 'amix', options: { inputs: 2, duration: 'shortest' }, inputs: ['file1_volume', 'file2_volume'] }
            ])
            .saveToFile(output)
            .on('error', function (err) {
                logger.error('An error occurred: ' + err.message);
                return reject({
                    status: false,
                    message: "Error in Mixing Audio",
                    rawError: err
                })
            })
            .on('end', function () {
                return resolve({
                    status: true,
                    message: "Mixed Successfully",
                    output
                })
            });
    });
}


export const getTrackFile = async (id: number) => {
    try {
        const data = await query(getTrackQuery, [id]);
        if (!data.rows) {
            return {
                status: false,
                message: "No Track Found",
                path: ""
            }
        }

        const localPath = await downloadFile(data.rows[0].track_url, karaokeTrackDir);
        return {
            status: true,
            message: "Track Received",
            path: localPath
        };

    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Couldn't get track DB`,
            error: error
        })
    }

}

export const saveRecording = async (song_id: number, user_id: number, url: string) => {
    try {
        const data = await query(saveRecordQuery, [song_id, user_id, url]);
        if (!data.rows) {
            return {
                status: false,
                message: "Error in saving data"
            }
        }
        return {
            status: true,
            message: "DB Entry Done",
            id: data.rows[0].id
        };

    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Couldn't Save Entry`,
            error: error
        })
    }

}

export const getArtistTracks = async (artist_id: number) => {
    try {
        const data = await query(getArtistTracksQuery, [artist_id]);
        if (!data.rows) {
            return {
                status: false,
                message: "No Track Found"
            }
        }

        return {
            status: true,
            message: "Tracks Found",
            data: data.rows
        }
    } catch (error) {
        return Promise.reject({
            status: false,
            message: `Couldn't get track DB For Artist`,
            error: error
        })
    }
}