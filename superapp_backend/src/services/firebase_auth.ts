import { handleResponse } from "../common";
import admin from "firebase-admin";
import serviceAccount from "../assets/artisteverse-c7b44-firebase-adminsdk-fbsvc-c1fe56aa25.json";

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as any),
  });
}

export const firestoreDB = admin.firestore();


export const verifyFirebaseToken = async (req: any, res: any, next: any) => {
  let response = {
    status: false,
    message: `Request failed`,
    error: "",
  };
  try {
    const token = req.body.token;
    if (!token) {
      response.error = "No Token Provided";
      handleResponse(res, response, 401);
    }
    const decodedToken = await admin.auth().verifyIdToken(token);
    let data = {
      uid: decodedToken.uid,
      name: decodedToken.name,
      phoneNumber: decodedToken.phone_number,
      email: decodedToken.email,
      profilePic: decodedToken.picture,
      isFacebook: decodedToken.firebase.sign_in_provider === "facebook.com",
      isTwitter: decodedToken.firebase.sign_in_provider === "twitter.com",
    };
    if (
      decodedToken.firebase.sign_in_provider === "facebook.com" ||
      decodedToken.firebase.sign_in_provider === "twitter.com"
    ) {
      let resp = await getUserDetailsByUuid(decodedToken.uid);
      data.email = resp.providerData[0].email;
    }
    req.user = data;
    next();
    return;
  } catch (error) {
    console.error("Firebase token verification failed:", error);
    response.error = "Server Error";
    return handleResponse(res, response, 401);
  }
};

export const getUserDetailsByUuid = async (uid: string) => {
  try {
    const userRecord = await admin.auth().getUser(uid);
    return userRecord;
  } catch (error) {
    console.error("Error fetching user data:", error);
    throw error;
  }
};
