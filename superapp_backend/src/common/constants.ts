export const REGEX = {
  EMAIL: /^[\w\.-]+@[\w\.-]+\.\w+$/,
  MOBILE: new RegExp("^([0|+[0-9]{1,5})?([7-9][0-9]{9})$"),
};

export const OTP_CONFIG = {
  MAX_ATTEMPT: 5,
  MAX_RESEND: 3,
};

export const OTP_TYPES = {
  MAIL: "MAIL",
  MOBILE: "MOBILE",
  MAIL_FP: "MAIL_FP",
  MOBILE_FP: "MOBILE_FP",
};

export const LOGIN_METHODS = {
  GOOGLE: 1,
  EMAIL: 2,
  MOBILE: 3,
  FACEBOOK: 4,
  TWITTER: 5,
};

export const ORDER_STATUS = {
  PLACED: 2,
  PAY_INIT: 10,
  PAY_FAILED: 11,
  CREATED: 1,
  DELIVERED: 6,
  CANCELLED: 8,
};

export const REFUND_STATUS = {
  INIT: "INITIATED",
};

export const ORDER_STATUS_CSV = [
  {
    name: "Processing",
    id: 4,
  },
  {
    name: "Delivered",
    id: 6,
  },
  {
    name: "Shipped",
    id: 5,
  },
];

export const PHONE_PE_STATUS = {
  PAYMENT_INIT: {
    name: "PAYMENT_INIT",
    id: 2,
  },
  PAYMENT_SUCCESS: {
    name: "PAYMENT_SUCCESS",
    id: 3,
  },
  PAYMENT_ERROR: {
    name: "PAYMENT_ERROR",
    id: 4,
  },
  PAYMENT_DECLINED: {
    name: "PAYMENT_DECLINED",
    id: 5,
  },
  TIMED_OUT: {
    name: "TIMED_OUT",
    id: 6,
  },
  PAYMENT_PENDING: {
    name: "PAYMENT_PENDING",
    id: 1,
  },
};

export const DEFAULT_PAGE_SIZE = 40;

export const getSignupVerificationBody = (otp: number): string => {
  const body = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>Welcome to Artisteverse</title>
    </head>
    <body>
        <h4>Welcome aboard, User!</h4>
        <p>
            Verification code: <strong>${otp}</strong>
        </p>
        <p>
            Enter this code on the Verification Screen to confirm your email address and activate your account.
        </p>
        <p>
            Get ready to explore, connect and make the most of our platform's exciting features.
        </p>
        <p>
            If you ever need assistance or have questions, don't hesitate to reach out at 
            <a href="mailto:<EMAIL>"><EMAIL></a>
        </p>
        <p>
            Best regards,<br>
            Team Artisteverse
        </p>
    </body>
    </html>
    
    `;
  return body;
};

export const getSignupSubject = () => {
  const subject = "Artisteverse Verification Code";
  return subject;
};

export const getForgotBody = (otp: number): string => {
  const body = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>Welcome to Artisteverse</title>
    </head>
    <body>
        <h4>Dear, User!</h4>

        <p>
        We received a request to reset the password for your Metastar account. To proceed, use the following verification code,
        </p>

        <p>
            Verification code: <strong>${otp}</strong>
        </p>
        
        <p>
        Enter this code on the verification page to reset your password. Please note that this code is valid for a limited time.        </p>
        <p>
        If you did not initiate this request or have any concerns, please contact our support team immediately at 
            <a href="mailto:<EMAIL>"><EMAIL></a>
        </p>
        <p>
            Best regards,<br>
            Team Artisteverse
        </p>
    </body>
    </html>
    
    `;
  return body;
};

export const getForgotSubject = () => {
  const subject = "Reset Your Password";
  return subject;
};

export const cleanUserName = (value: string) => {
  if (!value) {
    throw 'Value Not passed'
  }
  return value.trim().toLowerCase();
};

export const formatRegex = /^\d{4}-\d{2}-\d{2}$/;

export const USER_TYPES = {
  USER: 1,
  ADMIN: 2,
};

export const invalidArray = ["", null, undefined];

const generateFormDetail = (data: Object) => {
  let htmlContent = "<h1>User Details</h1>";
  const filteredData = Object.values(data).filter((item) => item.key);
  const sortedData = filteredData.sort((a, b) => a.order - b.order);
  for (const item of sortedData) {
    if (item.key == "video") {
      htmlContent += `<p><strong>${item.title}:</strong> <a href="${item.value}">${item.value}</a></p>`;
    } else {
      htmlContent += `<p><strong>${item.title}:</strong> ${item.value}</p>`;
    }
  }
  return htmlContent;
};

export const getArtistAlertBody = (
  baseUrl: string,
  id: string,
  name: string,
  token: string,
  data: Object,
  meetType: number
) => {
  let html = `
    <h2>${name}</h2>
    ${generateFormDetail(data)}
  `;

  if (meetType !== 3) {
    html += `
      <a href="${baseUrl}/api/accept-request/?id=${id}&token=${token}">
        <button type="button" class="btn accept">Accept</button>
      </a>
      <a href="${baseUrl}/api/reject-request-detail/?id=${id}&token=${token}">
        <button type="button" class="btn reject">Reject</button>
      </a>
    `;
  }

  return html;
};

export const getUserRequestSavedBody = (name: string, artistName: string, meetName: string) => {
  const html = `<p>Dear ${name},<br>
    <br>
   Thank you for your application for the ${meetName}. <br>
   Your application is under review and you shall hear back from us within 72 hours.</p>
   
   <p>Your patience during the review process is appreciated.</p>
   
   <p>Best regards,<br>
    <br>
   Team ${artistName}<br>
   </p>`;

  return html;
};

export const getUserAcknowldgementSub = "Thank You for your Application";

export const getArtistReqAlertSubject = () => {
  const subject = "Request Alert";
  return subject;
};

export const getAcceptOrRejectBody = (
  name: string,
  baseUrl: string,
  id: string,
  token: string
) => {
  const html = `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Accept or Reject Form</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                background-color: #f4f4f4;
            }
            .container {
                text-align: center;
            }
            .form-group {
                margin-bottom: 20px;
            }
            .form-group label {
                display: block;
                font-weight: bold;
                margin-bottom: 5px;
            }
            .form-group input[type="text"] {
                width: 300px;
                padding: 10px;
                font-size: 16px;
                border: 1px solid #ccc;
                border-radius: 5px;
                box-sizing: border-box;
                margin-top: 5px;
            }
            .form-group .btn {
                padding: 10px 20px;
                font-size: 16px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                margin-right: 10px;
            }
            .form-group .btn.accept {
                background-color: #4CAF50;
                color: white;
            }
            .form-group .btn.reject {
                background-color: #f44336;
                color: white;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h2>Accept or Reject Form</h2>
            <form id="acceptRejectForm">
                <div class="form-group">
                    <label for="name">Name: ${name}</label>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn accept" onclick="accept(event)">Accept</button>
                    <button type="button" class="btn reject" onclick="reject(event)">Reject</button>
                </div>
            </form>
        </div>
    
        <script>
            // Function to handle rejection button click
            function reject(event) {
                event.preventDefault();
                fetch("${baseUrl}/api/reject-request/?id=${id}&token=${token}", {
                    method: "POST"
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error("Network response was not ok");
                    }
                    return response.json();
                })
                .then(data => {
                    alert(data.message);
                })
                .catch(error => {
                    console.error('Error calling API:', error);
                    alert("Failed to call API. Please try again later.");
                });
            }

            function accept(event) {
                event.preventDefault();
                fetch("${baseUrl}/api/accept-request/?id=${id}&token=${token}", {
                    method: "POST"
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error("Network response was not ok");
                    }
                    return response.json();
                })
                .then(data => {
                    alert(data.message);
                })
                .catch(error => {
                    console.error('Error calling API:', error);
                    alert("Failed to call API. Please try again later.");
                });
            }
        </script>
    </body>
    </html>
    `;

  return html;
};

export const getRejectDetailBody = (
  baseUrl: string,
  id: string,
  token: string
) => {
  const html = `<!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Feedback Form</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 20px;
                        }
                        .radio-group {
                            margin-bottom: 20px;
                        }
                        .radio-group label {
                            display: block;
                            margin-bottom: 10px;
                        }
                        button {
                            background-color: #4CAF50;
                            color: white;
                            padding: 10px 20px;
                            font-size: 16px;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                        }
                    </style>
                </head>
                <body>
                    <h2>Please Select Your Feedback</h2>

                    <form id="feedbackForm">
                        <div class="radio-group">
                            <label>
                                <input type="radio" name="feedback" value="We feel you are not ready to fully benefit from this Masterclass just yet and we hope you will apply again in the future.">
                                We feel you are not ready to fully benefit from this Masterclass just yet and we hope you will apply again in the future.
                            </label>
                            <label>
                                <input type="radio" name="feedback" value="We have been overwhelmed with applications and are unable to accept you at this point. But we would be happy to re-consider your application in the future, if you so wish to apply again.">
                                We have been overwhelmed with applications and are unable to accept you at this point. But we would be happy to re-consider your application in the future, if you so wish to apply again.
                            </label>
                            <label>
                                <input type="radio" name="feedback" value="Your application did not furnish enough details for us to make an informed decision. We request you to re-apply providing more detailed inputs.">
                                Your application did not furnish enough details for us to make an informed decision. We request you to re-apply providing more detailed inputs.
                            </label>
                        </div>
                        <button type="button" onclick="submitFeedback()">Submit</button>
                    </form>

                    <script>
                        function submitFeedback() {
                            const form = document.getElementById('feedbackForm');
                            const selectedOption = form.querySelector('input[name="feedback"]:checked');

                            if (selectedOption) {
                                const xhr = new XMLHttpRequest();
                                const apiUrl = "${baseUrl}/api/reject-request/?id=${id}&token=${token}";

                                xhr.open('POST', apiUrl, true);
                                xhr.setRequestHeader('Content-Type', 'application/json;charset=UTF-8');
                                xhr.onreadystatechange = function() {
                                    if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
                                        alert('Feedback submitted successfully!');
                                    }
                                };

                                xhr.send(JSON.stringify({
                                    feedback: selectedOption.value
                                }));
                            } else {
                                alert('Please select an option.');
                            }
                        }
                    </script>
                </body>
                </html>
`;

  return html;
};

export const getUserRequestStatusAlertBody = (status: string) => {
  const html = `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Meet Request Status</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                background-color: #f4f4f4;
            }
            .container {
                text-align: center;
            }
            .form-group {
                margin-bottom: 20px;
            }
            .form-group label {
                display: block;
                font-weight: bold;
                margin-bottom: 5px;
            }
            .form-group input[type="text"] {
                width: 300px;
                padding: 10px;
                font-size: 16px;
                border: 1px solid #ccc;
                border-radius: 5px;
                box-sizing: border-box;
                margin-top: 5px;
            }
            .form-group .btn {
                padding: 10px 20px;
                font-size: 16px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                margin-right: 10px;
            }
            .form-group .btn.accept {
                background-color: #4CAF50;
                color: white;
            }
            .form-group .btn.reject {
                background-color: #f44336;
                color: white;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h2>Meet Request Status</h2>
            <form id="acceptRejectForm">
                <div class="form-group">
                    <label for="name">${status}</label>
                </div>
            </form>
        </div>
    </body>
    </html>
    `;

  return html;
};

export const AckEmail = {
  acceptedSubject: `Congratulations! You've Been Selected for the Masterclass`,
  acceptedBody: (name: string, linkUrl: string, artistName: string) => {
    return `<!DOCTYPE html>
                <html lang="en">
                <body>
                    <p>Dear ${name},</p>

                    <p>We are happy to inform you that your application has been successful and we invite you to finalize your place for the Masterclass with ${artistName}.</p>

                    <p>To secure your spot, please follow the link below to choose a time slot from the available options and complete your payment:</p>

                    <p><a href=${linkUrl}>Pay Now</a></p>

                    <p>Kindly note that by booking your slot, you agree to the terms and conditions outlined for the masterclass with ${artistName}.</p>

                    <p>If you have any questions or concerns through this process, please do not hesitate to contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

                    <p>Best regards,</p>

                    <p>Team ${artistName}</p>
                </body>
                </html>`;
  },
  rejectedSubject: `Thank You for your Enquiry`,
  rejectedBody: (name: string, artistName: string, feedBack: string) => {
    return `<!DOCTYPE html>
                <html lang="en">
                <body>
                    <p>Dear ${name},</p>

                    <p>Thank you for taking the time to share your details and inquire about the Masterclass with ${artistName}.</p>

                    <p>We regret to inform you that we have been unable to accept your application at this time because,</p>

                    <ul>
                        <li>${feedBack}</li>
                    </ul>

                    <p>Best regards,</p>

                    <p>Team ${artistName}</p>
                </body>
                </html>
                `;
  },
};

export const BGAckEmail = {
  acceptSubject: `Congratulations! You’re Selected for the Bickram Ghosh Mentorship Program`,
  acceptBody: (userName: string, artistName: string, meetName: string, linkUrl: string,) => {
    return `
    <!DOCTYPE html>
      <html lang="en">
        <body>
          <p>Dear ${userName},</p>
          <p>We’re excited to inform you that you’ve been selected for the ${meetName}!</p>
          <p> 
            This is a unique opportunity to collaborate, learn, and create music with the maestro himself. 
            To confirm your spot, please follow the link below to choose a time slot from the available options for the introduction call and 
            complete your payment:
          </p>
          <p><a href=${linkUrl}>Pay Now</a></p>
          <p> 
            Kindly note that by booking your slot, you agree to the terms and 
            conditions outlined for the ${meetName}.
          </p>
          <p>Best regards,</p>
          <p>Team ${artistName}</p>
        </body>
      </html>
    `;
  },
  acceptBody2: (userName: string, artistName: string, meetName: string, linkUrl: string,) => {
    return `
    <!DOCTYPE html>
      <html lang="en">
        <body>
          <p>Dear ${userName},</p>
          <p>We’re excited to inform you that you’ve been selected for the ${meetName}!</p>
          <p> 
            This is a unique opportunity to collaborate, learn, and create music with the maestro himself. To confirm your participation, please complete your payment using the link below:
          </p>
          <p><a href=${linkUrl}>Pay Now</a></p>
          <p> 
            Once your payment is confirmed, our team will create a 
            WhatsApp group to coordinate the next steps, 
            including project discussions, recording details, and timelines.
          </p>
          <p>
            Kindly note that by making the payment, you agree to the terms and conditions outlined for the Mentorship Program with Bickram Ghosh.
          </p>
          <p>Best regards,</p>
          <p>Team ${artistName}</p>
        </body>
      </html>
    `;
  },
  rejectedSubject: `Update on Your Application – Bickram Ghosh Mentorship Program`,
  rejectBody: (userName: string, artistName: string, meetName: string) => {
    return `<!DOCTYPE html>
      <html lang="en">
        <body>
          <p>Dear ${userName},</p>
          <p>Thank you for applying for the ${meetName} and for sharing your music with us. We truly appreciate your passion and enthusiasm.</p>
          <p>
            At this time, we regret to inform you that we are unable to move forward with your application. 
            Due to a high number of submissions and limited slots, we had to make some difficult decisions
          </p>
          <p>
            We encourage you to keep creating and exploring new opportunities, and we hope to connect with you again in the future.
          </p>
          <p>
            Wishing you all the best on your musical journey!
          </p>

          <p>Best regards,</p>
          <p>Team ${artistName}</p>
        </body>
      </html>`;
  }
}

export const MeetInvoiceEmail = {
  body: (
    purchase: any,
    duration: string,
    link: string,
    date: string,
    time: string
  ) => {
    return `
                <!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Masterclass Confirmation</title>
                </head>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <p>Dear ${purchase.customerName},</p>

                    <p>We are happy to confirm your place for a Masterclass with Priyadarsini Govind.</p>

                    ${date ? `<p>Mentioned below are the details of your sessions:</p>

                              <ul>
                                  <li><strong>Date: ${date}</strong></li>
                                  <li><strong>Time: ${time}</strong></li>
                                  <li><strong>Duration:</strong> ${duration}</li>
                                  <li>${link}</li>
                                  ${purchase && purchase.address && purchase.address.line1
          ? `
                                  <address>
                                      ${purchase.customerName} <br/>
                                      ${purchase.address.line1}, <br/>
                                      ${purchase.address.line2}, ${purchase.address.line3}<br/>
                                      ${purchase.address.city}, ${purchase.address.state} - ${purchase.address.pincode}
                                  </address>
                                  `
          : ""
        }
                              </ul>

                              <p>Please make a note of these details in your calendar.</p>` : ``
      }

                    <p>We wish you an enriching learning journey through this process. If you have any questions or concerns through this process, please do not hesitate to contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

                    <p>Best regards,</p>

                    <p>Team Priyadarsini Govind</p>
                </body>
                </html>
                `;
  },
  subject: "Confirmation of Masterclass with Priyadarsini Govind",
};

export const MeetInvoiceEmailPGOffline = {
  subject: "Welcome to Abhinaya – Level 1 | Your Enrollment is Confirmed",
  body: (name: string, timing: string) => {
    return `<p>Dear ${name},<br>
      <br/>
    <p>Thank you for enrolling in <b>Abhinaya – Level 1,</b>. a 16-week certificate course guided by
    <b>Priyadarsini Govind</b> through her signature <b>Learning Ladder Methodology.</b>
    </p>
    <p>We’re delighted to have you on board as you begin this transformative journey into the art of Abhinaya – the craft of emotional expression in Indian classical performance.</p>
    <br/>
    <b>Your Enrollment Details:</b>
    <br/>
    <p><b>Course Name:</b> Abhinaya – Level 1 (Beginner) <br/>
    <b>Start Date:</b> 8th July 2025 <br/>
    <b>End Date:</b> 21st October 2025 <br/>
    <b>Class Timings:</b> ${timing} </p>

    <br/>

    <p> <b>What’s Next?</b> You will receive class links via email one week before the course begins</p>
    <br/>
    <p> <b>Note:</b> Attendance in a minimum of 28 classes is required to receive the course completion certificate. <br/>

     If you have any questions, feel free to contact us at, <EMAIL></p>
    <br/>
    <p>Warm regards,<br/>
    Team Artisteverse
    </p>`
    ;
  }
}

export const BGMeetInvoiceEmail = {
  subject: `Confirmation of Mentorship Program with Bickram Ghosh`,
  body: (
    purchase: any,
    date: string,
    time: string
  ) => {
    return `
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <p>Dear ${purchase.customerName},</p>
        <p>
          We are happy to confirm your spot for The Mentorship program with Bickram Ghosh.
        </p>
        <p>
          Your introductory call with Bickram Ghosh has been scheduled as per your selected time slot. 
         ${(date && time) ? "Below are the details:" : ""} 
        </p>
          ${date ? `Date: ${date}` : ""}
        <p>
        <p>
          ${time ? `Time: ${time}` : ""}
        </p>
        <p>
          During this call, Bickram Ghosh will discuss your project, provide initial guidance, and 
          help you prepare for the recording session. Please ensure you are in a quiet space with a 
          stable internet connection for the call.
        </p>
        <p>Best regards,</p>
        <p>Team Bickram Ghosh</p>
      </body>
    `;
  },
  body1: (
    purchase: any
  ) => {
    return `
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <p>Dear ${purchase.customerName},</p>
        <p>
          We are happy to confirm your spot for The Mentorship program with Bickram Ghosh.
        </p>
        <p>
          Our team will be creating a WhatsApp group to coordinate the next steps, including project discussions, 
          recording details, and timelines. Please keep an eye out for the group invite.
        
        <p>
          Through this program, you will receive expert guidance from Bickram Ghosh, 
          collaborate on your track, and work towards an official release.
        </p>
        <p>
          Looking forward to this exciting musical journey together!
        </p>
        <p>Best regards,</p>
        <p>Team Bickram Ghosh</p>
      </body>
    `;
  }
}

export const VIDEO_ACCESS = {
  FREE: 1,
  PAID: 2,
};

export const CURRENCIES = {
  INR: 1,
  USD: 2,
};

export const PAYMENT_METHOD = {
  PHONEPE: 1,
  PAYPAL: 2,
  PAYGLOCAL: 3,
};

export const PAYPAL_PLATFORM_FEES = 0.1;