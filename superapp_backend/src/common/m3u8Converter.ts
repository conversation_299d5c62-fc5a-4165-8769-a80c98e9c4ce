import { spawn } from 'node:child_process';
import { promises as fs } from 'node:fs';
import { existsSync } from 'node:fs';
import * as path from 'node:path';

export interface ConversionOptions {
  outputDir?: string;
  segmentDuration?: number;
  videoCodec?: string;
  audioCodec?: string;
  videoBitrate?: string;
  audioBitrate?: string;
  resolution?: string;
  playlistName?: string;
  segmentPrefix?: string;
}

export interface ConversionResult {
  success: boolean;
  outputPath?: string;
  playlistUrl?: string;
  segmentCount?: number;
  duration?: number;
  error?: string;
}

const DEFAULT_OPTIONS: Required<Omit<ConversionOptions, 'resolution'>> & Pick<ConversionOptions, 'resolution'> = {
  outputDir: './hls-output',
  segmentDuration: 10,
  videoCodec: 'libx264',
  audioCodec: 'aac',
  videoBitrate: '1000k',
  audioBitrate: '128k',
  resolution: undefined,
  playlistName: 'playlist.m3u8',
  segmentPrefix: 'segment_%03d.ts'
};

class FFmpegError extends Error {
  constructor(message: string, public code?: number, public stderr?: string) {
    super(message);
    this.name = 'FFmpegError';
  }
}

async function runCommand(command: string, args: string[]): Promise<string> {
  return new Promise((resolve, reject) => {
    const process = spawn(command, args);
    let stdout = '';
    let stderr = '';

    process.stdout?.on('data', (data) => {
      stdout += data.toString();
    });

    process.stderr?.on('data', (data) => {
      stderr += data.toString();
    });

    process.on('close', (code) => {
      if (code === 0) {
        resolve(stdout);
      } else {
        reject(new FFmpegError(`Command failed with code ${code}`, code || undefined, stderr));
      }
    });

    process.on('error', (error) => {
      reject(new FFmpegError(`Process error: ${error.message}`));
    });
  });
}

async function validateFFmpeg(): Promise<void> {
  try {
    await runCommand('ffmpeg', ['-version']);
  } catch (error) {
    throw new FFmpegError('FFmpeg is not installed or not available in PATH');
  }
}

async function validateInputFile(inputPath: string): Promise<void> {
  if (!existsSync(inputPath)) {
    throw new Error(`Input file does not exist: ${inputPath}`);
  }

  const stats = await fs.stat(inputPath);
  if (!stats.isFile()) {
    throw new Error(`Input path is not a file: ${inputPath}`);
  }

  const ext = path.extname(inputPath).toLowerCase();
  const supportedFormats = ['.mp4', '.mkv', '.mov', '.avi', '.webm', '.flv', '.wmv'];
  if (!supportedFormats.includes(ext)) {
    throw new Error(`Unsupported file format: ${ext}. Supported: ${supportedFormats.join(', ')}`);
  }
}

async function createOutputDirectory(outputDir: string): Promise<void> {
  try {
    await fs.mkdir(outputDir, { recursive: true });
  } catch (error) {
    throw new Error(`Failed to create output directory: ${error instanceof Error ? error.message : String(error)}`);
  }
}

async function countSegmentFiles(outputDir: string): Promise<number> {
  try {
    const files = await fs.readdir(outputDir);
    return files.filter(file => file.endsWith('.ts')).length;
  } catch (error) {
    return 0;
  }
}

function buildFFmpegArgs(inputPath: string, options: Required<Omit<ConversionOptions, 'resolution'>> & Pick<ConversionOptions, 'resolution'>): string[] {
  const outputPath = path.join(options.outputDir, options.playlistName);
  const segmentPath = path.join(options.outputDir, options.segmentPrefix);

  const args = [
    '-i', inputPath,
    '-c:v', options.videoCodec,
    '-c:a', options.audioCodec,
    '-b:v', options.videoBitrate,
    '-b:a', options.audioBitrate,
    '-f', 'hls',
    '-hls_time', options.segmentDuration.toString(),
    '-hls_playlist_type', 'vod',
    '-hls_segment_filename', segmentPath,
    '-hls_flags', 'independent_segments',
    '-y',
    outputPath
  ];

  if (options.resolution) {
    const insertIndex = args.indexOf('-f');
    args.splice(insertIndex, 0, '-s', options.resolution);
  }

  return args;
}

/**
 * Convert MP4/MKV file to HLS (m3u8) format
 * @param inputPath - Path to the input video file
 * @param options - Conversion options
 * @returns Promise<ConversionResult>
 */
export async function convertToHLS(
  inputPath: string,
  options: ConversionOptions = {}
): Promise<ConversionResult> {
  const startTime = Date.now();
  
  try {
    await validateFFmpeg();
    await validateInputFile(inputPath);
    
    const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
    await createOutputDirectory(mergedOptions.outputDir);
    const ffmpegArgs = buildFFmpegArgs(inputPath, mergedOptions);
    await runCommand('ffmpeg', ffmpegArgs);
    
    const outputPath = path.join(mergedOptions.outputDir, mergedOptions.playlistName);
    if (!existsSync(outputPath)) {
      throw new Error('Conversion completed but playlist file was not created');
    }
    
    const segmentCount = await countSegmentFiles(mergedOptions.outputDir);
    const conversionTime = Math.round((Date.now() - startTime) / 1000);
    
    return {
      success: true,
      outputPath,
      playlistUrl: outputPath,
      segmentCount,
      duration: conversionTime
    };
    
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}
