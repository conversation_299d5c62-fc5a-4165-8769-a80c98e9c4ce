import AWS from 'aws-sdk';
import fs from 'fs';
import logger from '../logger';
import mime from 'mime-types';

export interface FileUpload {
    fileName: string;
    filePath: string;
    folderName: string;
}

export interface UploadResult {
    fileName: string;
    url?: string;
    error?: string;
}

export const uploadMultipleFiles = async (
    bucketName: string="metastar-superapp", 
    files: FileUpload[]
): Promise<UploadResult[]> => {
    const s3 = new AWS.S3({
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY
    });

    const uploadPromises = files.map(async (file): Promise<UploadResult> => {
        const { fileName, filePath, folderName } = file;

        try {
            if (!fs.existsSync(filePath)) {
                logger.error(`File Not Found in the given path ${filePath}`);
                return {
                    fileName,
                    error: `File not found: ${filePath}`
                };
            }

            const fileContent = fs.readFileSync(filePath);
            const ContentType = mime.contentType(fileName) || '';

            const params = {
                Bucket: bucketName,
                Key: `${folderName}/${fileName}`,
                Body: fileContent,
                ACL: 'bucket-owner-full-control', // permissions
                ContentType: ContentType
            };

            await s3.putObject(params).promise();
            
            const url = `https://d1su9s3pi6vr0g.cloudfront.net/${folderName}/${fileName}`;
            
            return {
                fileName,
                url
            };

        } catch (error) {
            logger.error(`Error uploading file ${fileName} to S3:`, error);
            return {
                fileName,
                error: `Upload failed: ${error}`
            };
        }
    });

    const results = await Promise.allSettled(uploadPromises);
    
    return results.map((result, index) => {
        if (result.status === 'fulfilled') {
            return result.value;
        } else {
            return {
                fileName: files[index].fileName,
                error: `Promise rejected: ${result.reason}`
            };
        }
    });
}