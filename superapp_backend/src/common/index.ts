import axios from "axios";
import bcrypt from "bcrypt";
import * as jwt from "jsonwebtoken";

export const handleResponse = async (
  res: any,
  response: any,
  statusCode: number = 200
) => {
  return res.status(statusCode).send(response);
};

export const hashPassword = async (password: string) => {
  const hshdPass = bcrypt.hash(password, 10);
  return hshdPass;
};

export const comparePasswprd = async (
  storedHashedPassword: string,
  userProvidedPassword: string
) => {
  const compare = await bcrypt.compare(
    userProvidedPassword,
    storedHashedPassword
  );
  return compare;
};

export const makePostCall = async (url: string, payload: any, config?: any) => {
  try {
    let res = await axios.post(url, payload, config);
    let data = res.data;
    return data;
  } catch (error: any) {
    console.error("Error on making axios post request : ", {
      url,
      payload,
      config,
      error,
    });
    return Promise.reject({
      status: false,
      message: error.response.data.message
        ? error.response.data.message
        : "Eroor in executing API",
      rawError: error,
    });
  }
};

export const makeGetCall = async (
  url: string,
  params: any,
  headers: any = {}
) => {
  try {
    let res = await axios.get(url, {
      params,
      headers: headers,
    });
    let data = res.data;
    return data;
  } catch (error: any) {
    console.error("Error on making axios GET request : ", {
      url,
      params,
      error,
    });
    return Promise.reject({
      status: false,
      message: error.response.data.message
        ? error.response.data.message
        : "Eroor in executing API",
      rawError: error,
    });
  }
};

export const checkCredentialParams = (paramsArray: any[]) => {
  const notNullCount = paramsArray.filter(
    (param) => param != null || param != undefined
  ).length;

  if (notNullCount === 1) {
    // Only one parameter is not null
    return true;
  } else {
    // Either none or more than one parameter is not null
    return false;
  }
};

export const createJWTToken = (payload: Object) => {
  return jwt.sign(payload, process.env.SECRET!);
};

export const createJWTTokenWithExp = (
  payload: Object,
  expiresInMinutes: number
) => {
  return jwt.sign(payload, process.env.SECRET!, {
    expiresIn: expiresInMinutes * 60,
  });
};

export const calculatePercentage = (part: number, whole: number) => {
  const percentage = (part / whole) * 100;
  return whole - percentage;
};

export const isValidDateFormat = (dateString: string) => {
  var dateFormatRegex = /^(0[1-9]|1[0-2])\/(0[1-9]|[12][0-9]|3[01])\/\d{4}$/;
  return dateFormatRegex.test(dateString);
};

export const isValidTimeFormat = (timeString: string) => {
  var dateFormatRegex = /^(0[1-9]|1[0-2])\/(0[1-9]|[12][0-9]|3[01])\/\d{4}$/;
  return dateFormatRegex.test(timeString);
};

export const reducePercent = (amount: number, gst: number, dis: number) => {
  const productwithoutgst = amount / (1 + (gst / 100));
  const percentage = (productwithoutgst * dis) / 100;
  const finalPrice = productwithoutgst - percentage + ((productwithoutgst - percentage) * (gst / 100));

  return parseFloat(finalPrice.toFixed(2));
};