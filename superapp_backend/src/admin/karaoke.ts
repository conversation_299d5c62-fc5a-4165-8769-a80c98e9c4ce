import { formatRegex, invalidArray } from "../common/constants";
import { handleResponse } from "../common";
import { query } from "../db";

export const countKaraokeQuery = `
SELECT 
 COUNT(*)
FROM karaoke_recordings`;

export const karokeQuery = (key: string, order: string) => {
    return `
    select 
	kr.id songid, 
 	coalesce(us.email,us.mobile_number) user_name,
	ks.name song_name,
	ar.name artist_name,
	kr.url song_url,
    TO_CHAR(kr.created at time zone 'asia/kolkata','Mon DD YYYY') song_created
from karaoke_recordings kr
inner join karaoke_songs ks on ks.id = kr.song_id
inner join users us on us.id = kr.user_id
inner join artist ar on ar.id = ks.artist_id
WHERE (kr.created between ($3 at time zone 'asia/kolkata') and (($4 at time zone 'asia/kolkata') + interval '1 day' - interval '1 second') OR $3 IS NUll)
AND (ar.id = $5 or $5 is null)
ORDER BY ${key} ${order}
    LIMIT $1
    OFFSET $2
    `
};

export const getKaraoke = async (req: any, res: any) => {
    let response: any = {}
    let { page, pageSize, key, order, fromDate, toDate, artistId } = req.query;
    page = Number(page) ? Number(page) : 1;
    pageSize = Number(pageSize) ? Number(pageSize) : 10;
    key = key ? key : 'user_id';
    order = order ? order : 'desc';

    const offset = (page - 1) * pageSize;

    if ((fromDate && toDate) && (!formatRegex.test(fromDate) || !formatRegex.test(toDate))) {
        response = {
            status: false,
            message: `Invalid Date Format`
        }
        return handleResponse(res, response, 400);
    }
    else if ((fromDate && !toDate) || (toDate && !fromDate)) {
        response = {
            status: false,
            message: `Invalid Date Provided`
        }
        return handleResponse(res, response, 400);
    }

    try {
        const totalResp = await query(countKaraokeQuery, []);
        const resp = await query(karokeQuery(key, order), [pageSize, offset, !invalidArray.includes(fromDate) ? fromDate : null, !invalidArray.includes(toDate) ? toDate : null, !invalidArray.includes(artistId) ? artistId : null]);
        response = {
            status: true,
            message: "Success",
            result: {
                items: resp.rows,
                total: parseInt(totalResp.rows[0].count)
            }
        }
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}
