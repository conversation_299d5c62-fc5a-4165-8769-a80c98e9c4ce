import { sendEmail } from "../services/mailer";
import { query } from "../db";
import logger from "../logger";
import { Parser } from 'json2csv'
import moment from "moment";

const summaryQuery = `select 
sum(case when login_method = 1 then 1 else 0 end) google_signups,
sum(case when login_method = 2 then 1 else 0 end) email_signups,
sum(case when login_method = 3 then 1 else 0 end) mobile_signups,
count(*) total_signups,
(
    select count(*) from users
    where created between
    (NOW() at time zone 'asia/kolkata' - INTERVAL '1 WEEK')
    and (NOW() at time zone 'asia/kolkata')
) weekly_signup,
(
    select created at time zone 'asia/kolkata' from users 
    order by id desc limit 1
) last_signup
from users;`;

const downloadQuery = `
select 
	us.email, us.mobile_number, us.email_verified, us.mobile_verified,
	lm.name login_method, cl.name client,
	us.created at time zone 'asia/kolkata' created_at
from users us
inner join login_methods lm on lm.id = us.login_method
inner join clients cl on cl.id = us.client_id
order by us.id desc;
`;

let emailString = process.env.REPORT_TO_MAIL as unknown as string;
let email : string[] = emailString.split(",");
export interface SummaryReport {
    google_signups: number,
    email_signups: number,
    mobile_signups: number,
    total_signups: number,
    weekly_signup: number,
    last_signup: string
}

export interface UserReport {
    email: string,
    mobile_number: string,
    email_verified: boolean,
    mobile_verified: boolean,
    login_method: string,
    client: string,
    created_at: string,
}



export const getOverallReport = async () => {
    try {
        const result = await query(summaryQuery, []);
        return result.rows[0];
    } catch (error) {
        logger.error("Error on getting overall report : ", error);
        return []
    }
}

export const getAllUserReport = async () => {
    try {
        const result = await query(downloadQuery, []);
        return result.rows;
    } catch (error) {
        logger.error("Error on getting All User report : ", error);
        return []
    }
}


export const sendReport = async () => {
    try {


        const reportSummary: SummaryReport = await getOverallReport();

        const userReport: UserReport[] = await getAllUserReport();

         // Get the date of the last week in dd/mm/yy format
         const lastWeekDate = moment().subtract(1, 'week').format('DD/MM/YY');

         // Get the current date in dd/mm/yy format
         const currentDate = moment().format('DD/MM/YY');

        const subject = `Weekly Signup Report for ${lastWeekDate} - ${currentDate}`;
        const jsonCsvParser = new Parser();

        const reportCsv = jsonCsvParser.parse(userReport);

       

        let attachements: any = [];
        let cmpnyReport = {
            filename: `UserReport ${lastWeekDate} - ${currentDate}.csv`,
            content: reportCsv
        }
        attachements.push(cmpnyReport);
        const body = `
    <!DOCTYPE html>
        <html>
            <body>
                <h4 style='text-align:justify'>Metastar Weekly Report</h4>
                <p style='text-align:justify'> Total Signups : <strong>${reportSummary.total_signups}  </strong></p>
                <p style='text-align:justify'> Weekly Signups : <strong>${reportSummary.weekly_signup} </strong> </p>
                <p style='text-align:justify'> Google Signups : <strong>${reportSummary.google_signups}</strong> </p> 
                <p style='text-align:justify'> Email Signups : <strong>${reportSummary.email_signups}</strong> </p>
                <p style='text-align:justify'> Mobile Signups :<strong> ${reportSummary.mobile_signups}</strong> </p> 
                <p style='text-align:justify'> Last Signups : <strong>${reportSummary.last_signup} </strong> </p>
                <h6>Please find the attached file</h6>
            </body>
        </html>
    `

        await sendEmail(email, subject, body, undefined, attachements);
    } catch (error) {
        logger.error("Error in sending report mail", error)
    }
}