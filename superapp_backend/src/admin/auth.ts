import { REG<PERSON>, USER_TYPES, cleanUserName } from "../common/constants";
import { checkCredentialParams, comparePasswprd, createJWTToken, handleResponse } from "../common";
import { findUser } from "../services/auth";

export const adminLogin = async (req: any, res: any) => {
    let response: any = {};

    let email = '';
    let mobile;
    let google = false;
    let password;
    if (req.user) {
        google = true;
        email = req.user.email
        email = cleanUserName(email);
    } else {
        let userName = req.body.userName;
        password = req.body.password;
        userName = cleanUserName(userName);
        email = REGEX.MOBILE.test(userName) ? null : userName;
        mobile = REGEX.MOBILE.test(userName) ? "91" + userName : null;

        if (!password || !checkCredentialParams([email, mobile])) {
            response = {
                status: false,
                message: `Invalid Params`
            }
            return handleResponse(res, response, 400);
        }
    }

    try {
        const user = await findUser(USER_TYPES.ADMIN, undefined, email, mobile);
        if (!user) {
            response = {
                status: false,
                message: `User not found.`
            }
            return handleResponse(res, response, 400);
        }

        if (!google) {
            const verifyPass: any = await comparePasswprd(user.password_hash, password);

            if (!verifyPass) {
                response = {
                    status: false,
                    message: 'Invalid Credential'
                }
                return handleResponse(res, response, 400);
            }
        }

        const token = createJWTToken({
            id: user.id,
            email: user.email,
            mobile: user.mobile_number,
            email_verified: user.email_verified,
            mobile_verified: user.mobile_number,
            client_id: user.client_id,
            is_admin: true
        })

        response = {
            status: true,
            message: 'Success',
            data: {
                token: token
            }

        }
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}