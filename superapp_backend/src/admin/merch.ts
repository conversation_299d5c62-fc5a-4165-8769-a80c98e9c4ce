import { formatRegex, invalidArray } from "../common/constants";
import { handleResponse } from "../common";
import { query } from "../db";
import {
  getOrderDetailAdmin,
  getPurchaseAddress,
  getPurchasedProducts,
} from "../services/orders";

export const countOrderHistoryQuery = `
SELECT 
 COUNT(*)
FROM order_details`;

export const countOrderHistoryQueryV2 = `select (count(distinct oi.id) + count(distinct cpl.id) +
count(distinct upv.id) + count(distinct aml.id)) as count from users u
left join order_details od on od.user_id = u.id
left join order_items oi on oi.order_id = od.id
left join course_purchase_logs cpl on cpl.user_id = u.id
left join user_paid_video_entries upv on upv.user_id = u.id
left join artist_meeting_logs aml on aml.user_id = u.id`;

export const orderHistoryQuery = (key: string, order: string) => {
  return `
    SELECT 
	od.id order_id,
	us.id userid, 
 	COALESCE(us.email,us.mobile_number) user_name,
	ps.name payment_status,
	os.name order_status,
	invoice_url,
	TO_CHAR(od.created at time zone 'asia/kolkata','Mon DD YYYY') order_created
FROM order_details od
INNER JOIN order_items oi on oi.order_id = od.id
INNER JOIN product_item pi on pi.id = oi.product_item_id
INNER JOIN products p on p.id = pi.product_id
INNER JOIN payment_status ps on ps.id = od.payment_status
INNER JOIN order_status os on os.id = od.order_status
INNER JOIN users us on us.id = od.user_id
WHERE (od.created between ($3 at time zone 'asia/kolkata') and (($4 at time zone 'asia/kolkata') + interval '1 day' - interval '1 second') OR $3 IS NUll)
AND (p.artist_id = $5 or $5 is null)
ORDER BY ${key} ${order}
    LIMIT $1
    OFFSET $2
    `;
};

export const orderHistoryV2 = (
  key: string,
  order: string,
  filterBy: string = ""
) => {
  const queryProductForFilter =
    filterBy !== ""
      ? `WHERE os.name = '${filterBy}'
    and (p.artist_id = $5 OR $5 IS NULL)`
      : `WHERE p.artist_id = $5 OR $5 IS NULL`;

  const queryCourseForFilter =
    filterBy !== ""
      ? `WHERE ps.name = '${filterBy}'
  and (cd.artist_id = $5 OR $5 IS NULL)`
      : `WHERE cd.artist_id = $5 OR $5 IS NULL`;

  const queryArtistMeetForFilter =
    filterBy !== ""
      ? `WHERE ps.name = '${filterBy}'
  and (am.artist_id = $5 OR $5 IS NULL)`
      : `WHERE am.artist_id = $5 OR $5 IS NULL`;

  const queryPaidVideoForFilter =
    filterBy !== "" ? `WHERE ps.name = '${filterBy}'` : ``;

  return `select
    	ROW_NUMBER() OVER (ORDER BY created) order_id,*
    from (
	(SELECT
	od.id order_number,
	us.id userid,
 	COALESCE(us.email,us.mobile_number) user_name,
	ps.name payment_status,
	os.name order_status,
	invoice_url,
	TO_CHAR(od.created at time zone 'asia/kolkata','Mon DD YYYY') order_created,
	'PRODUCT' order_type,
	od.created
	FROM order_details od
	INNER JOIN order_items oi on oi.order_id = od.id
	INNER JOIN product_item pi on pi.id = oi.product_item_id
	INNER JOIN products p on p.id = pi.product_id
	INNER JOIN payment_status ps on ps.id = od.payment_status
	INNER JOIN order_status os on os.id = od.order_status
	INNER JOIN users us on us.id = od.user_id
	${queryProductForFilter})

union all
	(select
    cpl.id order_number,
	cpl.user_id,
 	COALESCE(us.email,us.mobile_number) user_name,
	ps.name payment_status,
	ps.name order_status,
	cpl.invoice invoice_url,
	TO_CHAR(cpl.created at time zone 'asia/kolkata','Mon DD YYYY') order_created,
 	'COURSE' order_type,
	cpl.created
    from course_purchase_logs cpl
    inner join payment_status ps on ps.id = cpl.payment_status
    inner join course_details cd on cd.id = cpl.course_id
	INNER JOIN users us on us.id = cpl.user_id
	${queryCourseForFilter})
union all
	(select aml.id order_number,
	aml.user_id,
 	COALESCE(us.email,us.mobile_number) user_name,
	ps.name payment_status,
	ps.name order_status,
	aml.invoice invoice_url,
	TO_CHAR(aml.created at time zone 'asia/kolkata','Mon DD YYYY') order_created,
 	'MEET' order_type,
 	aml.created
    from artist_meeting_logs aml
    inner join payment_status ps on ps.id = aml.payment_status
    inner join artist_meet_detail amd on amd.artist_meet_id = aml.artist_meet_id
 	inner join artist_meets am on am.id = amd.artist_meet_id
	INNER JOIN users us on us.id = aml.user_id
	${queryArtistMeetForFilter})
union all
	(select upv.id order_number,
	upv.user_id,
 	COALESCE(us.email,us.mobile_number) user_name,
	ps.name payment_status,
	ps.name order_status,
	upv.invoice invoice_url,
	TO_CHAR(upv.created at time zone 'asia/kolkata','Mon DD YYYY') order_created,
	'PAID_VIDEO' order_type,
	upv.created
    from user_paid_video_entries upv
    inner join payment_transactions pt on pt.id = upv.txn_id
    inner join payment_status ps on ps.id = pt.payment_status
    inner join paid_videos pv on pv.id = upv.video_id
	INNER JOIN users us on us.id = upv.user_id
	 ${queryPaidVideoForFilter})
) as combined_result
where (created between ($3 at time zone 'asia/kolkata') and (($4 at time zone 'asia/kolkata') + interval '1 day' - interval '1 second') OR $3 IS NUll)
ORDER BY ${key} ${order}
     LIMIT $1
     OFFSET $2`;
};

export const getOrderHistory = async (req: any, res: any) => {
  let response: any = {};
  let { page, pageSize, key, order, fromDate, toDate, artistId, filterBy } =
    req.query;
  page = Number(page) ? Number(page) : 1;
  pageSize = Number(pageSize) ? Number(pageSize) : 10;
  key = key ? key : "created";
  order = order ? order : "desc";

  if (
    fromDate &&
    toDate &&
    (!formatRegex.test(fromDate) || !formatRegex.test(toDate))
  ) {
    response = {
      status: false,
      message: `Invalid Date Format`,
    };
    return handleResponse(res, response, 400);
  } else if ((fromDate && !toDate) || (toDate && !fromDate)) {
    response = {
      status: false,
      message: `Invalid Date Provided`,
    };
    return handleResponse(res, response, 400);
  }

  const offset = (page - 1) * pageSize;

  try {
    const totalResp = await query(countOrderHistoryQueryV2, []);
    const resp = await query(orderHistoryV2(key, order, filterBy), [
      pageSize,
      offset,
      !invalidArray.includes(fromDate) ? fromDate : null,
      !invalidArray.includes(toDate) ? toDate : null,
      !invalidArray.includes(artistId) ? artistId : null,
    ]);

    response = {
      status: true,
      message: "Success",
      result: {
        items: resp.rows,
        total: parseInt(totalResp.rows[0].count),
      },
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};

export const countCartHistoryQuery = `
SELECT 
 COUNT(*)
FROM shopping_cart`;

export const cartHistoryQuery = (key: string, order: string) => {
  return `
	select 
	sc.id cart_id,
	coalesce(us.email,us.mobile_number) user_name, 
	ar.name artist_name,
	count(sci.*) total_items,
	TO_CHAR(sc.updated at time zone 'asia/kolkata','Mon DD YYYY') cart_updated
from shopping_cart sc
inner join users us on us.id = sc.user_id
inner join artist ar on ar.id = sc.artist_id
inner join shopping_cart_items sci on sci.cart_id = sc.id
group by sc.id,us.id,ar.id
ORDER BY ${key} ${order}
    LIMIT $1
    OFFSET $2
    `;
};

export const getCartHistory = async (req: any, res: any) => {
  let response: any = {};
  let { page, pageSize, key, order } = req.query;
  page = Number(page) ? Number(page) : 1;
  pageSize = Number(pageSize) ? Number(pageSize) : 10;
  key = key ? key : "cart_id";
  order = order ? order : "desc";

  const offset = (page - 1) * pageSize;
  try {
    const totalResp = await query(countCartHistoryQuery, []);
    const resp = await query(cartHistoryQuery(key, order), [pageSize, offset]);
    response = {
      status: true,
      message: "Success",
      result: {
        items: resp.rows,
        total: parseInt(totalResp.rows[0].count),
      },
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Request failed`,
      error: error,
    };
    return handleResponse(res, response, 500);
  }
};

// const orderDetailByIdQuery = `select od.id "orderNumber",od.created date,u.email email,
// od.total_items total,p.price "subTotal",p.tax,
// pm.name as "paymentMethod",
// 'Free Shipping' shipping,
// p.id product_id,
// pms.name payment_status,
// os.name order_status,
// od.address_id,
// od.invoice_url
// from order_details od
// inner join order_items oi on oi.order_id = od.id
// inner join product_item pi on pi.id = oi.product_item_id
// inner join products p on p.id = pi.product_id
// inner join users u on u.id = od.user_id
// inner join payment_method pm on pm.id = od.payment_method
// inner join payment_status pms on pms.id = od.payment_status
// inner join order_status os on os.id = od.order_status
// where od.id = $1
// group by od.id,u.id,p.id,pm.id,pms.id,os.id;`

// const getAddressByIdQuery = `select
// coalesce(ua.email,u.email) email,
// coalesce(ua.phone,u.mobile_number) phone,
// u.id user_id,
// ua.first_name,
// ua.last_name,
// ua.address_line_one,
// ua.address_line_two,
// ua.address_line_three,
// ua.city,
// ua.state,
// ua.pincode,
// ua.country
// from users u
// left join user_address ua on ua.user_id = u.id
// where ua.id = $1`;

// const productVariantsQuery = `select pi.id,pi.title,oi.quantity,oi.purchased_price total,p.tax,oi.price_per_quantity price from order_items oi
// inner join product_item pi on pi.id = oi.product_item_id
// inner join products p on p.id = pi.product_id
// where oi.order_id = $1;`

export const orderDetail = async (req: any, res: any) => {
  let response: any = {};
  const { order_item_id, order_type } = req.query;

  if (!order_item_id || !order_type) {
    response = {
      status: false,
      message: `Missing Params`,
    };
    return handleResponse(res, response, 400);
  }

  try {
    const orderDetail = await getOrderDetailAdmin(order_item_id, order_type);

    const address = await getPurchaseAddress(
      order_type,
      orderDetail.user_id,
      order_type == "PRODUCT" ? orderDetail.address_id : null
    );

    const productsWithSubtotal = await getPurchasedProducts(
      order_item_id,
      order_type
    );

    const total = productsWithSubtotal.reduce(
      (totalSum, { total }) => totalSum + Number(total),
      0
    );
    const subTotal = productsWithSubtotal.reduce(
      (total, { subtotal }) => total + Number(subtotal),
      0
    );

    response = {
      status: true,
      message: `Success`,
      data: {
        detail: {
          ...orderDetail,
          total,
          subTotal,
          tax: Number(total - subTotal).toFixed(2),
          address: address,
        },
        products: productsWithSubtotal,
      },
    };
    return handleResponse(res, response, 200);
  } catch (error) {
    response = {
      status: false,
      message: `Requst failed`,
    };
    return handleResponse(res, response, 500);
  }
};
