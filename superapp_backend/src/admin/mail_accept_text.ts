export interface OrderDATA {
    customerName: string;
    total: number;
    address: {
        line1: string,
        line2: string,
        line3: string,
        city: string,
        state: string,
        pincode: number
    };
    products: (ProductsEntity)[];
    subText?: string
}
export interface ProductsEntity {
    name: string;
    imageUrl: string;
    quantity: number;
    price: number;
    symbol: string;
    size?: string
}
export interface AttributesEntity {
    key: string;
    value: string;
}


export const mailAcceptText = (purchase: OrderDATA) => {

    let productList = '';

    purchase.products.forEach(product => {
        productList += `
        <tr style="border-bottom: 1.5px solid black;">
                    <td style="width: 120px">
                        <img width="70px" height="70px"
                            src="${product.imageUrl}"
                            alt="Product Image">
                    </td>
                    <td style="width: 150px;">${product.name} x ${product.quantity}</td>
    
                    <td style="width: 80px;text-align: right;">${product.symbol}${product.price}</td>
                </tr>
        `
    });

    return `<!DOCTYPE html>
    <html lang="en">
    
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your Order is Confirmed!</title>
    </head>
    
    <body>
        <div style="padding: 2rem;">
            <p>Dear ${purchase.customerName},</p>
            ${!purchase.subText ? `<p>Thank you for your purchase! Your order has been accepted by Artisteverse.</p>` : ``}
            ${purchase.subText ? purchase.subText : `<p>We&rsquo;re getting your order ready to be shipped. We will notify you when it has been sent. The ideal time
                required to ship your order is 2-5 working days.</p>`}
            <br>
            <h4>Order Summary</h4>
            <table style="border-collapse: collapse;">
                ${productList}
                <tr>
                    <td style="width: 120px">
                        Total (Shipping and charges are included)
                    </td>
    
                    <td style="width: 150px">
    
                    </td>
    
                    <td style="width: 80px;text-align: right;"><span>${purchase.products[0].symbol}</span>${purchase.total}</td>
                </tr>
            </table>
    
            <h4>Customer Information</h4>
    
            <h5>Shipping Address</h5>
            <address>
            ${purchase.customerName} <br/>
            ${purchase.address.line1 + ","} <br/>
            ${purchase.address.line2}, ${purchase.address.line3}<br/>
            ${purchase.address.city}, ${purchase.address.state} - ${purchase.address.pincode}
            </address>
            <p>
                <span style="font-size:10pt;">
                    If you have any questions, contact us at&nbsp;
                </span>
                <a href="mailto:<EMAIL>">
                    <u>
                        <span style="color:#1155cc;font-size:10pt;"><EMAIL>
                        </span>
                    </u>
                </a>
            </p>
            <span style="font-size:11pt;">Regards,</span>
            <br/>
            <span style="font-size:11pt;">Artisteverse Team</span>
        </div>
    </body>
    
    </html>
`
};

export const mailShippedText = (purchase: OrderDATA) => {

    let productList = '';

    purchase.products.forEach(product => {
        productList += `
        <tr style="border-bottom: 1.5px solid black;">
                    <td style="width: 120px">
                        <img width="70px" height="70px"
                            src="${product.imageUrl}"
                            alt="Product Image">
                    </td>
                    <td style="width: 150px;">${product.name} x ${product.quantity}</td>
    
                    <td style="width: 80px;text-align: right;"><span>&#8377;</span>${product.price}</td>
                </tr>
        `
    });

    return `<!DOCTYPE html>
    <html lang="en">
    
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your Order is on the Way!</title>
    </head>
    
    <body>
        <div style="padding: 2rem;">
            <p>Dear ${purchase.customerName},</p>
            <p>Your order has been shipped! It will reach you in 5-7 working days.</p>
            <br>
            <h4>Order Details</h4>
            <table style="border-collapse: collapse;">
                ${productList}
                <tr>
                    <td style="width: 120px">
                        Total (Shipping and charges are included)
                    </td>
    
                    <td style="width: 150px">
    
                    </td>
    
                    <td style="width: 80px;text-align: right;"><span>${purchase.products[0].symbol}</span>${purchase.total}</td>
                </tr>
            </table>
    
            <h4>Shipping Details</h4>
    
            <h5>Shipping Address</h5>
            <address>
            ${purchase.customerName} <br/>
            ${purchase.address.line1 + ","} <br/>
            ${purchase.address.line2}, ${purchase.address.line3}<br/>
            ${purchase.address.city}, ${purchase.address.state} - ${purchase.address.pincode}
            </address>

            
            <p>
                <span style="font-size:10pt;">
                    If you have any questions, contact us at&nbsp;
                </span>
                <a href="mailto:<EMAIL>">
                    <u>
                        <span style="color:#1155cc;font-size:10pt;"><EMAIL>
                        </span>
                    </u>
                </a>
            </p>
            <span style="font-size:11pt;">Regards,</span>
            <br/>
            <span style="font-size:11pt;">Artisteverse Team</span>
        </div>
    </body>
    
    </html>
`
};

export const mailDeliveredText = (purchase: OrderDATA) => {

    let productList = '';

    purchase.products.forEach(product => {
        productList += `
        <tr style="border-bottom: 1.5px solid black;">
                    <td style="width: 120px">
                        <img width="70px" height="70px"
                            src="${product.imageUrl}"
                            alt="Product Image">
                    </td>
                    <td style="width: 150px;">${product.name} x ${product.quantity}</td>
    
                    <td style="width: 80px;text-align: right;"><span>&#8377;</span>${product.price}</td>
                </tr>
        `
    });

    return `<!DOCTYPE html>
    <html lang="en">
    
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your Order has been Delivered!</title>
    </head>
    
    <body>
        <div style="padding: 2rem;">
            <p>Dear ${purchase.customerName},</p>
            <p>Your order has been successfully delivered to the following address.</p>
            <br>

            <h5>Delivery Address</h5>
            <address>
            ${purchase.customerName} <br/>
            ${purchase.address.line1 + ","} <br/>
            ${purchase.address.line2}, ${purchase.address.line3}<br/>
            ${purchase.address.city}, ${purchase.address.state} - ${purchase.address.pincode}
            </address>

            <br>
            <h4>Package Details</h4>
            <table style="border-collapse: collapse;">
                ${productList}
                <tr>
                    <td style="width: 120px">
                        Total (Shipping and charges are included)
                    </td>
    
                    <td style="width: 150px">
    
                    </td>
    
                    <td style="width: 80px;text-align: right;"><span>${purchase.products[0].symbol}</span>${purchase.total}</td>
                </tr>
            </table>
                
            <p>
                <span style="font-size:10pt;">
                If you have any questions or need assistance with your order, please contact our customer support team at
                </span>
                <a href="mailto:<EMAIL>">
                    <u>
                        <span style="color:#1155cc;font-size:10pt;"><EMAIL>
                        </span>
                    </u>
                </a>
            </p>
            <span style="font-size:11pt;">Regards,</span>
            <br/>
            <span style="font-size:11pt;">Artisteverse Team</span>
        </div>
    </body>
    
    </html>
`
};

export const mailCancelledText = (purchase: any) => {

    let productList = '';

    purchase.products.forEach((product: any) => {
        productList += `
                <div class="product">
                    <img src="${product.imageUrl}" alt="Product 1">
                    <div>
                        <p><strong>${product.name}</strong></p>
                        ${product.size ? `<p>Size: ${product.size} &emsp; Qty: ${product.quantity}</p>` : `<p>Qty: ${product.quantity}</p>`}
                    </div>
                </div>
        `
    });

    return `<!DOCTYPE html>
    <html lang="en">
    
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Order Cancellation</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
            }
            .container {
                max-width: 600px;
                margin: 20px auto;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 5px;
                box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
            }
            .footer {
                margin-top: 20px;
                font-size: 0.9em;
                color: #555;
            }
            .order-details {
                margin-top: 20px;
                border-top: 1px solid #ddd;
                padding-top: 10px;
            }
            .product {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
            }
            .product img {
                width: 50px;
                height: 50px;
                margin-right: 10px;
                border: 1px solid #ddd;
            }
            .order-details-bottom {
                border-top: 1px solid #ddd;
                margin-top: 10px;
                padding-top: 10px;
            }
        </style>
    </head>
    
    <body>
        <div class="container">
            <p>Dear ${purchase.customerName},</p>
            <p>We regret to inform you that your order has been cancelled as per your request. Below are the details of the cancelled order:</p>

            <div class="order-details">
                <h3>Order Details:</h3>
                ${productList}
            </div>

            <div class="order-details-bottom"></div>

            <p>Since your order was cancelled before shipping, no charges have been processed. If you have already made a payment, the refund will be initiated shortly and should reflect in your account within 5-7 business days.</p>
            <p>If you have any questions or need assistance, please contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
            <p>We apologize for any inconvenience caused and hope to serve you again in the future.</p>

            <p class="footer">Warm regards,<br>Team Artisteverse</p>
        </div>
    </body>
    
    </html>
`
};
