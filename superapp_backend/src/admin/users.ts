import { handleResponse } from "../common";
import { query } from "../db";

export const countUserQuery = `
SELECT 
 COUNT(*)
FROM users us`;

export const userQuery = (key: string, order: string) => {
    return `
    SELECT 
     us.id user_id,
     coalesce(email,mobile_number) user_name, 
     TO_CHAR(us.created at time zone 'asia/kolkata','Mon DD YYYY') user_created, 
     display_name,
     cl.name client_name,
     lm.name login_method_name
    FROM users us
    INNER JOIN clients cl ON cl.id = us.client_id
    INNER JOIN login_methods lm ON lm.id = us.login_method
    ORDER BY ${key} ${order}
    LIMIT $1
    OFFSET $2
    `
};

export const getUser = async (req: any, res: any) => {
    let response: any = {}
    let { page, pageSize, key, order } = req.query;
    page = Number(page) ? Number(page) : 1;
    pageSize = Number(pageSize) ? Number(pageSize) : 10;
    key = key ? key : 'user_id';
    order = order ? order : 'desc';

    const offset = (page - 1) * pageSize;
    try {
        const totalResp = await query(countUserQuery, []);
        const resp = await query(userQuery(key, order), [pageSize, offset]);
        response = {
            status: true,
            message: "Success",
            result: {
                items: resp.rows,
                total: parseInt(totalResp.rows[0].count)
            }
        }
        return handleResponse(res, response, 200);
    } catch (error) {
        response = {
            status: false,
            message: `Request failed`,
            error: error
        }
        return handleResponse(res, response, 500);
    }
}
