{"name": "superapp-backend", "version": "1.0.0", "description": "Backend For the artistverse", "main": "src/app.ts", "author": "Veerapandi S", "scripts": {"start": "NODE_PATH=./build node -r dotenv/config build/app.js", "dev": "NODE_OPTIONS=--max-old-space-size=4096 nodemon -r dotenv/config src/app.ts", "build": "tsc -p ."}, "dependencies": {"@aws-sdk/client-s3": "^3.550.0", "@google-analytics/data": "^5.1.0", "@google-cloud/storage": "^7.6.0", "aws-sdk": "^2.1692.0", "axios": "^1.8.3", "bcrypt": "^6.0.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "download": "^8.0.0", "exceljs": "^4.4.0", "express": "^4.21.2", "firebase-admin": "^13.1.0", "fluent-ffmpeg": "^2.1.2", "google-auth-library": "^9.2.0", "googleapis": "^149.0.0", "jose": "^5.9.6", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "payglocal-js-client": "github:PayGlocal-Technologies/payglocal-js-client", "pdfmake": "^0.2.8", "pg": "^8.11.3", "randomstring": "^1.3.0", "razorpay": "^2.9.4", "uuid": "^9.0.1", "winston": "^3.11.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.16", "@types/download": "^8.0.5", "@types/express": "^4.17.21", "@types/fluent-ffmpeg": "^2.1.24", "@types/json2csv": "^5.0.7", "@types/jsonwebtoken": "^9.0.5", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.10", "@types/node": "^20.9.0", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@types/pdfmake": "^0.2.8", "@types/pg": "^8.10.9", "@types/randomstring": "^1.3.0", "@types/uuid": "^9.0.7", "@types/xlsx": "^0.0.36", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "keywords": [], "license": "ISC"}