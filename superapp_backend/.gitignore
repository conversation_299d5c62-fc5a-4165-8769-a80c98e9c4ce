# Ignore Node.js modules and the 'node_modules' directory
node_modules/

# Ignore built TypeScript output
dist/
build/

# Ignore development-specific files
*.log
npm-debug.log*
logs/

# Ignore environment-specific files
.env

# Ignore Visual Studio Code settings
.vscode/

# Ignore macOS system files
.DS_Store

# Ignore TypeScript build artifacts
*.js
*.js.map

# Ignore configuration files
# tsconfig.json
package-lock.json
yarn.lock

assets/
temp/
output/
