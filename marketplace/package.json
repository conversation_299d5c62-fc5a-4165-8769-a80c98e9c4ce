{"name": "marketplace", "version": "0.0.4", "scripts": {"dev": "vite", "build": "vite build && echo '{\"buildTime\": '$(date +%s)'}' > dist/version.json", "preview": "vite preview", "lint": "eslint . --fix --ignore-path .gitignore"}, "dependencies": {"@mdi/font": "7.4.47", "@paypal/paypal-js": "^8.1.2", "axios": "^1.7.7", "core-js": "^3.37.1", "dayjs": "^1.11.13", "firebase": "^11.3.1", "hls.js": "^1.5.20", "moment": "^2.30.1", "roboto-fontface": "*", "vue": "^3.4.31", "vue3-google-login": "^2.0.33", "vuetify": "^3.6.11"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.5", "eslint": "^8.57.0", "eslint-config-standard": "^17.1.0", "eslint-config-vuetify": "^1.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.4.0", "eslint-plugin-vue": "^9.27.0", "pinia": "^2.1.7", "sass": "1.77.6", "terser": "^5.36.0", "unplugin-auto-import": "^0.17.6", "unplugin-fonts": "^1.1.1", "unplugin-vue-components": "^0.27.2", "unplugin-vue-router": "^0.10.0", "vite": "^5.3.3", "vite-plugin-mkcert": "^1.17.6", "vite-plugin-vue-layouts": "^0.11.0", "vite-plugin-vuetify": "^2.0.3", "vue-router": "^4.4.0"}}