<template>
  <v-app class="bg-color">
    <AppHeader v-if="!hideHeaderFooter" />
    <v-main>
      <router-view />
    </v-main>

    <AppFooter v-if="!hideHeaderFooter" />
  </v-app>
</template>

<script setup>
import { useRoute } from 'vue-router';
const route = useRoute();

console.log(route.path);


const hideHeaderFooter = /^\/(video|popup\/html|popup\/space)\/[a-zA-Z0-9]+$/.test(route.path);

</script>

<style scoped>
.bg-color {
  background-color: black;
}
</style>
