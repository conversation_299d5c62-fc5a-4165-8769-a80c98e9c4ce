<template>
  <v-app>
    <v-main v-if="CurrencyCode !== ''">
      <router-view />
    </v-main>
  </v-app>
</template>

<script setup>
import { onMounted } from 'vue'
import { useAppStore } from './stores/app'

const appStore = useAppStore()
const CurrencyCode = computed(() => appStore.currencyCode)


onMounted(async () => {
  try {
    await appStore.selectCurrencyCode()
  } catch (err) {
    console.error("Country detection failed:", err)
    appStore.selectCurrencyCode("USD")
  }
})
//
</script>
