<template>
  <div class="product-card-list">
    <div v-for="(product, index) in products" :key="index" class="product-card" @click="onSelectCategory(product.id)">
      <div>
        <div class="product-icon">
          <img :src="product.iconUrl" :alt="product.name" />
        </div>
        <h3 class="product-name">{{ product.name }}</h3>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { useAppStore } from "@/stores/app";
const appStore = useAppStore();

const products = ref([
  {
    id: 8,
    name: "Learning",
    iconUrl: "https://d2b4rv4q8lb0q0.cloudfront.net/marketplace/learning.png",
  },
  {
    id: 2,
    name: "Fashion",
    iconUrl: "https://d2b4rv4q8lb0q0.cloudfront.net/marketplace/fashion.png",
  },
  {
    id: 5,
    name: "Music Instruments",
    iconUrl: "https://d2b4rv4q8lb0q0.cloudfront.net/marketplace/instrument.png",
  },
  {
    id: 3,
    name: "Art ",
    iconUrl: "https://d2b4rv4q8lb0q0.cloudfront.net/marketplace/art.png",
  },
  {
    id: 1,
    name: "Beauty & Healing ",
    iconUrl: "https://d2b4rv4q8lb0q0.cloudfront.net/marketplace/beauty.png",
  },
  {
    id: 11,
    name: "Flim ",
    iconUrl: "https://d2b4rv4q8lb0q0.cloudfront.net/marketplace/art.png",
  },
]);

const onSelectCategory = (id) => {
  appStore.filterByCategory(id);
};
</script>

<style scoped>
.product-card-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem;
}

.product-card {
  width: 150px;
  height: 100px;
  background-color: #0066cc;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: left;
  padding: 10px;
  box-sizing: border-box;
  min-width: 100px;
}

.product-card:hover {
  cursor: pointer;
}


.product-icon {
  height: 30px;
  width: 30px;
  margin-bottom: 5px;
  align-self: left;
}

.product-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.product-name {
  color: white;
  font-size: 18px;
  text-align: left;
  margin: 0;
  max-width: 100%;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.2em;
  max-height: 2.4em;
}

@media (max-width: 768px) {
  .product-card-list {
    flex-wrap: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .product-card-list::-webkit-scrollbar {
    width: 0;
    height: 0;
  }

  .product-card {
    width: 100px;
    height: 100px;
  }

  .text-overlay p {
    font-size: 0.9rem;
  }
}
</style>
