<template>
  <div class="input-wrapper" :style="{ marginBottom: specific ? '0' : '1rem' }">
    <input
      :value="modelValue"
      @input="$emit('update:modelValue', $event.target.value)"
      :type="type"
      :class="['dark-input', { 'error-input': error }]"
      :placeholder="placeholder"
      :required="required"
      :style="{
        borderColor: specific ? 'black' : 'white',
        color: specific ? 'black' : 'white',
      }"
    />
    <!-- Subtext as a prop -->
    <div v-if="subtext" class="subtext">
      {{ subtext }}
    </div>
    <!-- Error message below subtext -->
    <div v-if="error" class="error-message">
      {{ error }}
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: "",
  },
  type: {
    type: String,
    default: "text",
  },
  placeholder: {
    type: String,
    default: "",
  },
  required: {
    type: Boolean,
    default: false,
  },
  error: {
    type: String,
    default: "",
  },
  specific: {
    type: Boolean,
    default: false,
  },
  // New subtext prop
  subtext: {
    type: String,
    default: "",
  },
});

defineEmits(["update:modelValue"]);
</script>

<style scoped>
.input-wrapper {
  width: 100%;
}
.dark-input {
  background-color: transparent;
  border: 0.2px solid white;
  border-radius: 25px;
  color: rgba(255, 255, 255, 0.9);
  padding: 12px 20px;
  font-size: 16px;
  width: 100%;
  outline: none;
}
.dark-input::placeholder {
  color: white;
}
.dark-input:focus {
  border-color: white;
  box-shadow: 0 0 0 1px white;
}
.error-input {
  border-color: red;
  box-shadow: 0 0 0 1px red;
}
.error-message {
  color: red;
  font-size: 12px;
  margin-top: 4px;
  padding-left: 20px;
}
.subtext {
  color: rgba(255, 255, 255, 0.7); /* Subtle white for subtext */
  font-size: 14px; /* Slightly smaller than input text */
  margin-top: 6px; /* Space between input and subtext */
  padding-left: 20px; /* Align with input padding */
}
</style>