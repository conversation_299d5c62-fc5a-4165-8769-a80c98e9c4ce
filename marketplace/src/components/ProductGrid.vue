<template>
  <v-container fluid class="fill-height">
    <v-row
      v-if="appStore.isLoading"
      justify="center"
      align="center"
      class="fill-height"
    >
      <v-col cols="auto">
        <v-progress-circular
          :size="70"
          :width="7"
          color="white"
          indeterminate
        ></v-progress-circular>
      </v-col>
    </v-row>
    <v-row
      v-if="props.products.length === 0"
      justify="center"
      align="center"
      class="fill-height"
    >
      <v-col cols="auto" v-if="!appStore.isLoading">
        <p>No Product Available</p>
      </v-col>
    </v-row>
    <template v-else>
      <v-row>
        <v-col
          v-for="(product, index) in displayedProducts"
          :key="product.id || index"
          cols="12"
          sm="6"
          md="6"
          lg="4"
          xl="4"
        >
          <ProductCard :product="product" />
        </v-col>
      </v-row>
      <v-row justify="center" v-if="showLoadMore">
        <v-col cols="12" sm="6" md="4">
          <v-btn block color="primary" @click="loadMore">Load More</v-btn>
        </v-col>
      </v-row>
    </template>
  </v-container>
</template>

<script setup>
import { ref, computed } from "vue";
import ProductCard from "./ProductCard.vue";
import { useAppStore } from "@/stores/app";
const appStore = useAppStore();
const props = defineProps({
  products: {
    type: Array,
    required: true,
  },
});

const itemsPerPage = ref(50);
const currentPage = ref(1);

const displayedProducts = computed(() =>
  props.products.slice(0, itemsPerPage.value * currentPage.value)
);

const showLoadMore = computed(
  () => displayedProducts.value.length < props.products.length
);

const loadMore = () => {
  currentPage.value++;
};
</script>

<style scoped>
.fill-height {
  height: 100%;
}
</style>
