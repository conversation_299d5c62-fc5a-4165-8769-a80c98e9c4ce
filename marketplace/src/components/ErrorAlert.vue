<script setup>
import { onMounted, onUnmounted } from 'vue';

const props = defineProps({
  message: String,
});

let originalOverflow;

onMounted(() => {
  originalOverflow = document.body.style.overflow;
  document.body.style.overflow = 'hidden';
});

onUnmounted(() => {
  document.body.style.overflow = originalOverflow;
});
</script>

<template>
  <div class="error-alert-overlay">
    <div class="error-alert-container">
      <v-row justify="center" class="my-3 mx-2">
        <v-col cols="12" md="12" sm="12" xs="12">
          <v-alert :text="message" type="error" dismissible></v-alert>
        </v-col>
      </v-row>
    </div>
  </div>
</template>

<style scoped>
.error-alert-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-start;
  z-index: 9999;
}

.error-alert-container {
  position: relative;
  top: 20px;
  width: 100%;
  max-width: 400px;
}
</style>
