<template>
  <div class="textarea-container">
    <textarea
      :value="modelValue"
      @input="$emit('update:modelValue', $event.target.value)"
      :placeholder="placeholder"
      :required="required"
      :class="['custom-textarea', { 'error': error }]"
    ></textarea>
    <div v-if="error" class="error-message">{{ error }}</div>
  </div>
</template>

<script setup>
defineProps({
  modelValue: {
    type: [String, Number],
    default: "",
  },
  placeholder: {
    type: String,
    default: "",
  },
  required: {
    type: Boolean,
    default: false,
  },
  error: {
    type: String,
    default: "",
  },
});

defineEmits(["update:modelValue"]);
</script>

<style scoped>
.textarea-container {
  width: 100%;
  position: relative;
}

.custom-textarea {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  box-sizing: border-box;
  border: 0.2px solid white;
  border-radius: 8px;
  background-color: transparent;
  color: white;
  font-size: 16px;
  resize: vertical;
  outline: none;
  transition: border-color 0.3s ease;
}

.custom-textarea::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.custom-textarea:focus {
  border-color: #ffffff;
}

.custom-textarea.error {
  border-color: #ff5252;
}

.error-message {
  color: #ff5252;
  font-size: 14px;
  margin-top: 4px;
}
</style>