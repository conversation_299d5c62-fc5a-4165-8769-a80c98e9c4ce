<template>
  <div class="sort-by-container" ref="dropdownRef">
    <button @click="toggleDropdown" class="sort-by-button">
      Sort By {{ selectedOption }}
      <span class="down-arrow">▼</span>
    </button>
    <ul v-if="isOpen" class="sort-options">
      <li
        v-for="option in sortOptions"
        :key="option"
        @click="selectOption(option)"
      >
        {{ option }}
      </li>
    </ul>
  </div>
</template>
  
  <script setup>
import { useAppStore } from '@/stores/app';
import { ref, onMounted, onUnmounted } from 'vue'
const dropdownRef = ref(null)

const sortOptions = ["Name", "Price", "Newest"];
const selectedOption = ref(null);
const isOpen = ref(false);
const appStore = useAppStore();
const toggleDropdown = () => {
  isOpen.value = !isOpen.value;
};

const selectOption = (option) => {
  selectedOption.value = option;
  isOpen.value = false;
  appStore.sortProducts(selectedOption.value);
};

const handleClickOutside = (event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target)) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

</script>
  
<style scoped>
.sort-by-container {
  position: relative;
  display: inline-block;
}

.sort-by-button {
  display: flex;
  align-items: center;
  padding: 5px 8px;
  border: 1px solid #ccc;
  border-radius: 50px;
  cursor: pointer;
  font-size: 12px;
}

.down-arrow {
  margin-left: 10px;
  font-size: 12px;
}

.sort-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  border: 1px solid #ccc;
  border-radius: 10px;
  margin-top: 5px;
  padding: 0;
  list-style-type: none;
  z-index: 1000;
  background-color: black;
  width: fit-content;
}

.sort-options li {
  padding: 10px 15px;
  cursor: pointer;
}

.sort-options li:hover {
  background-color: blue;
}
</style>