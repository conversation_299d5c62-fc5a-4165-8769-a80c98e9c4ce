<template>
  <div class="image-list">
    <div v-for="(item, index) in items" :key="index" class="image-item">
      <div class="image-container" @click="onSelectArtist(item.id)">
        <img :src="item.imageUrl" :alt="item.text" class="rounded-image" />
        <p>{{ item.text }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAppStore } from "@/stores/app";
import { ref } from "vue";
const appStore = useAppStore();

const onSelectArtist = (id) => {
  appStore.filterByArtist(id);
};

const items = ref([
  {
    id: 1,
    imageUrl:
      "https://d2b4rv4q8lb0q0.cloudfront.net/marketplace/artiste/Bickram Ghosh.jpg",
    text: "Bickram Ghosh",
  },
  {
    id: 2,
    imageUrl:
      "https://d2b4rv4q8lb0q0.cloudfront.net/marketplace/artiste/<PERSON>asi <PERSON>.jpg",
    text: "<PERSON><PERSON>",
  },
  {
    id: 3,
    imageUrl:
      "https://d2b4rv4q8lb0q0.cloudfront.net/marketplace/artiste/Purbayan Chatterjee.jpg",
    text: "Purbayan Chatterjee",
  },
  {
    id: 4,
    imageUrl:
      "https://d2b4rv4q8lb0q0.cloudfront.net/marketplace/artiste/Priyadarsini.jpg",
    text: "Priyadarsini Govind",
  },
  {
    id: 5,
    imageUrl:
      "https://d2b4rv4q8lb0q0.cloudfront.net/indian_ocean/Indian Ocean-min.png",
    text: "Indian Ocean",
  },
  // Add more items as needed
]);
</script>

<style scoped>
.image-list {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  flex-wrap: wrap;
}

.image-item {
  flex: 0 0 auto;
}

.image-container {
  position: relative;
  width: 200px;
  height: 150px;
  border-radius: 10%;
  border: 1px solid white;
  overflow: hidden;
}

.image-container:hover
{
  cursor: pointer;
}

.rounded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.text-overlay {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: flex-start;
  justify-content: left;
  background-color: rgba(0, 0, 0, 0.5);
  padding-left: 1rem;
  padding-top: 1rem;
}

.text-overlay p {
  color: white;
  text-align: left;
  font-size: 1.1rem;
  line-height: 1.2;
  margin: 0;
  max-width: 80%;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-transform: uppercase;
}

@media (max-width: 768px) {
  .image-list {
    flex-wrap: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* Internet Explorer 10+ */
  }

  .image-list::-webkit-scrollbar {
    /* WebKit */
    width: 0;
    height: 0;
  }

  .image-container {
    width: 150px;
    height: 112.5px;
  }

  .text-overlay p {
    font-size: 0.9rem;
  }
}
</style>
