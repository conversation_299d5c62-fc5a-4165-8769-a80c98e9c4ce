<template>
  <v-card width="300" height="500" class="mx-auto product-container d-flex flex-column" @click="onClickProduct()">
    <v-img :src="product.product_image[0] ? product.product_image[0] : dummyImage" height="300" cover>
      <v-avatar color="black" size="45" class="favorite-icon" @click.stop="toggleFavorite">
        <v-icon :color="isFavorite || product.is_wishlist ? 'red' : 'white'">mdi-heart</v-icon>
      </v-avatar>

      <template v-slot:placeholder>
        <v-row class="fill-height ma-0" align="center" justify="center">
          <v-progress-circular indeterminate color="grey-lighten-5"
            v-if="product.product_image[0]"></v-progress-circular>
        </v-row>
      </template>
      <template v-slot:error>
        <v-img :src="dummyImage" height="300" cover>
          <v-row class="fill-height ma-0" align="center" justify="center">
            <v-icon icon="mdi-image-off" size="48" color="grey-lighten-2"></v-icon>
          </v-row>
        </v-img>
      </template>
      <v-card-title class="text-white pa-2 product-title">
        {{ product.artist_name }}
      </v-card-title>
    </v-img>

    <v-card-text class="px-4 pt-4 pb-2 d-flex flex-column flex-grow-1">
      <div class="d-flex justify-space-between align-center mb-2">
        <div class="text-subtitle-1 font-weight-bold text-truncate mr-2">
          {{ product.product_name }}
        </div>
        <div class="text-h6 font-weight-bold flex-shrink-0" v-if="Number(product.currencies[0].price)">
          {{ currencyValue }}
        </div>
      </div>

      <div class="d-flex align-center mb-2">
        <div class="mr-2">Available:</div>
        <img :src="india_flag" alt="In Stock" class="availability-icon mr-1"
          :class="{ 'greyed-out': !isIndiaAvailable }" />
        <img :src="globe_flag" alt="Global Shipping" class="availability-icon"
          :class="{ 'greyed-out': !isGlobalAvailable }" />
      </div>

      <v-spacer></v-spacer>

      <v-btn block color="white" class="add-to-bag-btn">
        <span class="button-text">Add To Bag</span>
        <v-avatar color="black" size="45" class="shopping-icon">
          <v-icon icon="mdi-shopping" style="font-size: xx-large" color="white"></v-icon>
        </v-avatar>
      </v-btn>
    </v-card-text>

    <v-snackbar :color="isFavorite ? 'success' : 'red'" rounded="pill" v-model="snackbar.show"
      :timeout="snackbar.timeout">
      {{ snackbar.message }}
    </v-snackbar>
  </v-card>
</template>

<script setup>
import { formatIndianCurrency } from "@/helper/common";
import { useRouter } from "vue-router";
import dummyImage from "@/assets/image_not_available.jpg";
import india_flag from "@/assets/india_flag.webp";
import globe_flag from "@/assets/global.webp";
import { computed, ref } from "vue";
import { addWishList, removeWishList } from "@/services/productService";
import { useAppStore } from "@/stores/app";

const appStore = useAppStore();
const router = useRouter();

const currencyCode = appStore.currencyCode;

const props = defineProps({
  product: {
    type: Object,
    required: true,
  },
});

const GlobalCurrency = computed(() =>
  props.product.countries.find(item => item.country_name === "Global")
);

const inrCurrency = computed(() =>
  props.product.currencies.find(item => item.currency === "INR")
);

const usdCurrency = computed(() =>
  props.product.currencies.find(item => item.currency === "USD")
);

const currencyValue = computed(() => {
  if (currencyCode === "INR") {
    return inrCurrency.value?.price
      ? `Rs ${formatIndianCurrency(inrCurrency.value.price)}`
      : "---";
  }

  if (GlobalCurrency.value && usdCurrency.value?.price) {
    return `$ ${formatIndianCurrency(usdCurrency.value.price)}`;
  }

  return "---";
});


const isIndiaAvailable = computed(() => {
  return props.product.countries.some((country) => country.country_id === 1);
});

const isGlobalAvailable = computed(() => {
  return props.product.countries.some((country) => country.country_id === 2);
});


const isFavorite = ref(false);

const snackbar = ref({
  show: false,
  message: "",
  timeout: 3000,
});

const toggleFavorite = () => {
  if (isFavorite.value || props.product.is_wishlist) {
    isFavorite.value = false;

    removeWishList({
      id: props.product.product_id,
      type: props.product.product_type,
    });

    props.product.is_wishlist = false;
    snackbar.value.show = true;
    snackbar.value.message = `${props.product.product_name} removed from wishlist`;
  } else {
    isFavorite.value = true;
    addWishList({
      id: props.product.product_id,
      type: props.product.product_type,
    });
    props.product.is_wishlist = true;

    snackbar.value.message = `${props.product.product_name} added to wishlist`;

    snackbar.value.show = true;
  }
};

const onClickProduct = () => {
  if (props.product.product_type === "PAID_VIDEO") {
    router.push({
      path: `/paidVideo/${props.product.product_id}`,
    });
  }

  if (props.product.product_type === "PRODUCT") {
    if (props.product.design_data && props.product.design_data.type) {
      return router.push({
        path: `/productNFTDetail/${props.product.product_id}`,
      });
    }
    return router.push({
      path: `/productDetails/${props.product.product_id}`,
    });
  }

  if (props.product.product_type === "MEET") {
    router.push({
      path: `/slotBook/${props.product.product_id}`,
    });
  }

  if (props.product.product_type === "COURSE") {
    router.push({
      path: `/course/${props.product.product_id}`,
    });
  }
};
</script>

<style>
.product-container {
  border-radius: 30px;
  margin-bottom: 20px;
  background-color: transparent;
  transition: transform 0.2s ease-in-out;
}

.product-container:hover {
  cursor: pointer;
  transform: scale(1.05);
}

.product-title {
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: black;
  border-radius: 0 20px 20px 0;
  max-width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 16px;
  padding: 10px 40px 10px 15px !important;
}

.add-to-bag-btn {
  border-radius: 40px !important;
  font-weight: bold;
  color: black !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 0 7px 0 20px !important;
  position: relative !important;
  overflow: hidden !important;
  height: 30px;
}

.button-text {
  position: absolute !important;
  left: 20px !important;
}

.shopping-icon {
  position: absolute !important;
  right: 7px !important;
}

.availability-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.greyed-out {
  filter: grayscale(100%);
  opacity: 0.5;
}

.favorite-icon {
  cursor: pointer;
  position: absolute;
  top: 10px;
  right: 10px;
}

.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
