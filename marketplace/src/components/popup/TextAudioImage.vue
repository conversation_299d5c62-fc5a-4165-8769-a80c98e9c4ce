<template>
  <div class="page-container">
    <v-container class="h-100">
      <!-- Main Content Section -->
      <v-row class="fill-height align-center">
        <!-- Audio Player -->
        <v-col cols="12" sm="4" md="4" lg="4" class="d-flex justify-center">
          <v-card
            class="audio-player"
            max-width="400"
            min-height="250"
            elevation="4"
            color="white"
            rounded="xl"
          >
            <audio
              ref="audioElement"
              :src="audioSrc"
              @timeupdate="onTimeUpdate"
              @ended="onAudioEnded"
              @canplay="onCanPlay"
            ></audio>

            <v-card-text class="position-relative d-flex flex-column justify-center">
              <v-btn
                icon
                color="black"
                class="play-button"
                @click="togglePlay"
                size="x-large"
              >
                <v-icon color="white" size="32">
                  {{ isPlaying ? "mdi-pause" : "mdi-play" }}
                </v-icon>
              </v-btn>

              <div class="waveform">
                <div
                  v-for="(bar, index) in waveformBars"
                  :key="index"
                  class="wave-bar"
                  :class="{ active: currentBar === index }"
                  :style="{
                    height: `${bar}%`,
                    opacity: currentBar === index ? 1 : 0.7,
                  }"
                ></div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>

        <!-- Image Section -->
        <v-col cols="12" sm="8" md="8" lg="8" class="d-flex justify-center">
          <div class="image-container">
            <img v-if="images.length == 1" :src="images[0]" class="popup-image" />
            <ImageSlider v-else :images="images" />
          </div>
        </v-col>
      </v-row>

      <!-- Text Content Section -->
      <v-row class="justify-center mt-16">
        <v-col cols="12">
          <div class="content-container" v-html="text"></div>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
// Script remains the same
import { onBeforeMount, onMounted, ref } from "vue";
import ImageSlider from "./ImageSlider.vue";

const audioElement = ref(null);
const isPlaying = ref(false);
const waveformBars = ref([]);
const currentBar = ref(-1);
const isAudioReady = ref(false);
let animationInterval = null;

const props = defineProps({
  audioSrc: {
    type: String,
    required: true,
  },
  images: {
    type: Array,
    required: true,
  },
  text: {
    type: String,
    required: true,
  },
});

const animateWaveform = () => {
  if (animationInterval) {
    clearInterval(animationInterval);
  }
  animationInterval = setInterval(() => {
    currentBar.value = (currentBar.value + 1) % waveformBars.value.length;
  }, 100);
};

const togglePlay = async () => {
  if (!audioElement.value) return;

  try {
    if (isPlaying.value) {
      await audioElement.value.pause();
      isPlaying.value = false;
      clearInterval(animationInterval);
      currentBar.value = -1;
    } else {
      await audioElement.value.play();
      isPlaying.value = true;
      animateWaveform();
    }
  } catch (error) {
    console.error("Audio playback error:", error);
    isPlaying.value = false;
  }
};

const onTimeUpdate = () => {
  if (audioElement.value) {
    const progress =
      (audioElement.value.currentTime / audioElement.value.duration) *
      waveformBars.value.length;
    currentBar.value = Math.floor(progress);
  }
};

const onAudioEnded = () => {
  isPlaying.value = false;
  clearInterval(animationInterval);
  currentBar.value = -1;
};

const onCanPlay = () => {
  isAudioReady.value = true;
};

onMounted(() => {
  waveformBars.value = Array.from(
    { length: 40 },
    () => Math.random() * 70 + 30
  );
});

onBeforeMount(() => {
  if (animationInterval) {
    clearInterval(animationInterval);
  }
});
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.v-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.audio-player {
  border-radius: 16px !important;
  width: 100%;
}

.position-relative {
  position: relative;
  min-height: 250px;
  padding: 20px !important;
}

.play-button {
  position: absolute !important;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 2;
  width: 64px !important;
  height: 64px !important;
}

.waveform {
  position: absolute;
  top: 50%;
  left: 20px;
  right: 20px;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 3px;
  z-index: 1;
  height: 120px;
}

.wave-bar {
  flex-grow: 1;
  background-color: black;
  transition: height 0.2s ease, opacity 0.2s ease;
}

.wave-bar.active {
  opacity: 1 !important;
}

.image-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.popup-image {
  width: 100%;
  max-height: 60vh;
  object-fit: contain;
}

.content-container {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

@media screen and (max-width: 600px) {
  .v-row {
    flex-direction: column;
  }
  
  .audio-player {
    margin-bottom: 2rem;
  }
  
  .popup-image {
    max-width: 90vw;
    margin-top: 2rem;
  }
}
</style>