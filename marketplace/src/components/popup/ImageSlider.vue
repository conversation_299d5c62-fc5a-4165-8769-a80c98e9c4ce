<template>
    <v-container fluid>
      <v-carousel
        v-model="currentSlide"
        show-arrows
        height="400"
        class="carousel"
        :next-icon="nextIcon"
        :prev-icon="prevIcon"
      >
        <v-carousel-item
          v-for="(image, index) in images"
          :key="index"
          :src="image"
        ></v-carousel-item>
      </v-carousel>
    </v-container>
  </template>
  
  <script setup>
  import { ref } from 'vue';
  
  const props = defineProps({
    images: {
      type: String,
      required: true,
    },
  });
  
  const currentSlide = ref(0);
  const nextIcon = 'mdi-chevron-right'; // Use Material Design Icons
  const prevIcon = 'mdi-chevron-left';
  </script>
  
  <style scoped>
  .carousel {
    position: relative;
  }
  
  .v-carousel__controls {
    background-color: black; /* Black background for arrows */
  }
  
  .v-icon {
    color: white; /* White color for icons */
  }
  </style>
