<template>
  <v-container fluid class="fill-height">
    <v-row class="fill-height align-center justify-center" no-gutters>
      <v-col cols="12" md="8" class="d-flex justify-center">
        <v-card
          color="transparent"
          flat
          class="d-flex flex-column align-center"
        >
          <div class="video-container">
            <video
              ref="videoPlayer"
              class="video-player"
              controls
              controlsList="nodownload"
              @play="isPlaying = true"
              @pause="isPlaying = false"
              @ended="isPlaying = false"
            ></video>
            <div v-if="!isPlaying" class="play-button" @click="playVideo">
              <v-icon x-large color="white" style="font-size: 50px"
                >mdi-play</v-icon
              >
            </div>
          </div>
        </v-card>
      </v-col>

      <v-col
        cols="12"
        md="4"
        class="text-left d-flex align-center justify-center"
      >
        <div class="text-wrapper">
          {{ text }}
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { onBeforeUnmount, onMounted, ref } from "vue";
import Hls from "hls.js";

const isPlaying = ref(false);
const videoPlayer = ref(null);
const hls = ref(null);
const error = ref(null);

const props = defineProps({
  video: {
    type: String,
    required: true,
  },
  text: {
    type: String,
    required: true,
  },
});

const initializePlayer = (url) => {
  if (Hls.isSupported()) {
    if (hls.value) {
      hls.value.destroy();
    }

    hls.value = new Hls({
      xhrSetup: (xhr) => {
        xhr.withCredentials = true;
      },
    });

    hls.value.loadSource(url);
    hls.value.attachMedia(videoPlayer.value);

    hls.value.on(Hls.Events.MANIFEST_PARSED, () => {});

    hls.value.on(Hls.Events.ERROR, (event, data) => {
      console.error("HLS error:", data);
      error.value = `Video playback error: ${data.details}`;
    });
  } else if (videoPlayer.value.canPlayType("application/vnd.apple.mpegurl")) {
    videoPlayer.value.src = url;
    videoPlayer.value.addEventListener("loadedmetadata", () => {});
    videoPlayer.value.addEventListener("error", (e) => {
      error.value = `Video playback error: ${e.message || "Unknown error"}`;
    });
  } else {
    error.value = "HLS is not supported in this browser.";
  }
};
const playVideo = () => {
  if (videoPlayer.value) {
    videoPlayer.value.play();
  }
};

onMounted(() => {
  initializePlayer(props.video);
});

onBeforeUnmount(() => {
  if (hls.value) {
    hls.value.destroy();
  }
});
</script>

<style scoped>
.video-container {
  position: relative;
  width: 100%;
  min-width: 100%; /* Responsive by default */
  max-width: 100%;
}

@media (min-width: 800px) {
  .video-container {
    min-width: 600px;
  }
}

.video-player {
  width: 100%;
  height: auto;
  border: 2px solid white;
  border-radius: 8px;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.play-button:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.text-wrapper {
  max-width: 100%;
  padding: 8px;
}

@media (max-width: 600px) {
  .video-container {
    min-width: 100% !important;
    margin-bottom: 16px;
  }
}
</style>
