<template>
<v-row
      class="fill-height ma-0 align-center justify-center"
    >
      <v-col cols="12" class="text-center">
        <v-img
          v-if="images.length === 1"
          :src="images[0]"
          max-width="600"
          class="mx-auto"
        />
        <v-carousel
          v-else
          hide-delimiters
          :show-arrows="images.length > 1"
        >
          <v-carousel-item
            v-for="(image, index) in images"
            :key="index"
            :src="image"
          ></v-carousel-item>
        </v-carousel>
      </v-col>
    </v-row>

</template>

<script setup>
const props = defineProps({
  images: {
    type: Array,
    required: true,
  }
});
</script>

<style scoped>
/* Optional: Add custom styles for image display */
.v-img {
  max-height: 80vh;
  object-fit: contain;
}
.v-carousel {
  max-width: 800px;
  margin: 0 auto;
}
</style>