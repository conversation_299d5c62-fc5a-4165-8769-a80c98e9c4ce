<template>
    <v-container fluid class="fill-height pa-0">
      <v-row no-gutters class="fill-height">
        <v-col cols="12" md="6" class="image-col d-flex flex-column justify-center">
          <!-- Single image case -->
          <v-img
            v-if="images && images.length === 1"
            :src="images[0]"
            class="my-2 mx-auto image"
            contain
            max-height="80vh"
            max-width="100%"
            width="100%"
            @error="handleImageError"
          ></v-img>
          <!-- Multiple images case with carousel -->
          <v-carousel
            v-else-if="images && images.length > 1"
            hide-delimiters
            :show-arrows="images.length > 1"
            height="80vh"
            class="carousel"
          >
            <v-carousel-item
              v-for="(image, index) in images"
              :key="index"
              :src="image"
              contain
              class="carousel-item"
            ></v-carousel-item>
          </v-carousel>
          <!-- Fallback if no images -->
          <div v-else class="no-image pa-4">No images available</div>
        </v-col>
        <v-col cols="12" md="6" class="text-col d-flex align-center">
          <div class="text-content pa-4">{{ text || 'No text provided' }}</div>
        </v-col>
      </v-row>
    </v-container>
  </template>
  
  <script setup>
  import { ref } from 'vue';
  
  defineProps({
    text: String,
    images: Array,
  });
  
  const handleImageError = (event) => {
    console.error('Image failed to load:', event.target.src);
    event.target.src = '/path/to/fallback-image.jpg'; // Replace with your fallback image path
  };
  </script>
  
  <style scoped>
  .fill-height {
    height: 100vh;
    overflow: hidden;
  }
  
  .image-col {
    height: 100vh;
    overflow-y: auto;
  }
  
  .text-col {
    height: 100vh;
    overflow-y: auto;
  }
  
  .text-content {
    width: 100%;
    font-size: 1.1rem;
    line-height: 1.6;
  }
  
  .image {
    width: 100% !important; /* Ensure single image takes full column width */
    max-width: 100%;
    height: auto; /* Maintain aspect ratio */
  }
  
  .carousel {
    width: 100%;
    max-width: 100%;
  }
  
  .carousel-item {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .no-image {
    text-align: center;
    color: #888;
  }
  
  /* Ensure images are visible */
  .v-img__img--contain {
    object-fit: contain;
    width: 100% !important;
    height: auto !important;
    max-width: 100%;
    max-height: 100%;
  }
  
  /* Responsive adjustments */
  @media (max-width: 960px) {
    .image-col,
    .text-col {
      height: auto;
      max-height: 50vh;
    }
  
    .carousel {
      height: 50vh;
    }
  
    .image {
      max-height: 50vh;
    }
  }
  </style>