<template>
  <v-row>
    <v-col cols="12" md="6" sm="6" lg="6">
      <div class="image-container">
        <v-card class="transparent-card">
          <v-img v-if="productImage.imageURL" :src="productImage.imageURL" width="400" cover class="centered-image">
            <template v-slot:placeholder>
              <v-row class="fill-height ma-0" align="center" justify="center">
                <v-progress-circular indeterminate color="grey lighten-5"></v-progress-circular>
              </v-row>
            </template>
          </v-img>

          <video v-else-if="productImage.video" width="400" controls class="centered-image">
            <source :src="productImage.video" type="video/mp4">
            Your browser does not support the video tag.
          </video>
        </v-card>
      </div>
    </v-col>

    <v-col cols="12" md="6" sm="6" lg="6">
      <div v-html="htmlContent"></div>
    </v-col>
  </v-row>
</template>

<script setup>
const props = defineProps({
  productImage: {
    type: Object,
    required: true,
  },
  htmlContent: {
    type: String,
    required: true,
  }
});
</script>

<style scoped>
.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.transparent-card {
  background-color: transparent !important;
  box-shadow: none !important;
}

.centered-image {
  margin: auto;
}
</style>
