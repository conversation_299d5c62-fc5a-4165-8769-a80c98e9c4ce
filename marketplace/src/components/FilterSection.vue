<template>
  <div
    v-if="mdAndUp"
    v-for="(section, index) in sections"
    :key="index"
    class="container"
  >
    <hr />
    <div
      class="heading-arrow"
      @click="toggleSection(index)"
      :class="{ expanded: section.open }"
    >
      <h2 class="text-h6 mb-2">{{ section.title }}</h2>
      <span class="down-arrow" :class="{ rotated: section.open }">▼</span>
    </div>
    <div v-show="section.open" class="item-list">
      <div v-for="(item, idx) in section.items" :key="idx" class="item">
        <label class="checkbox-container">
          <input
            type="checkbox"
            v-model="item.selected"
            @change="handleFilterChange"
          />
        </label>
        <span class="text-subtitle-2">{{ item.text }}</span>
      </div>
    </div>
  </div>
  <div
    v-else
    v-for="(section, index1) in sections"
    :key="index1"
    class="container"
  >
    <hr />

    <div style="display: flex; justify-content: space-between">
      <div
        @click="toggleSection(index1)"
        :class="{ expanded: section.open }"
        style="width: 45%"
      >
        <h2 class="text-h6 mb-2">
          {{ section.title }}
        </h2>
      </div>
      <div v-show="section.open" class="itemMob">
        <div v-for="(item, idx) in section.items" :key="idx" class="item">
          <label class="checkbox-container">
            <input
              type="checkbox"
              v-model="item.selected"
              @change="handleFilterChange"
            />
            <span class="text-subtitle-2">{{ item.text }}</span>
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAppStore } from "@/stores/app";
import { ref } from "vue";
import { useDisplay } from "vuetify";

const { mdAndUp } = useDisplay();

const appStore = useAppStore();

const sections = ref([
  {
    title: "Artiste",
    open: true,
    items: [
      { id: 1, text: "Bickram Gosh", selected: false },
      { id: 2, text: "Manasi Scott", selected: false },
      { id: 3, text: "Purbayan Chatterjee", selected: false },
      { id: 4, text: "Priyadarsini Govind", selected: false },
      { id: 5, text: "Indian Ocean", selected: false },
    ],
  },
  {
    title: "Categories",
    open: false,
    items: [
      { id: 8, text: "Learning", selected: false },
      { id: 2, text: "Fashion", selected: false },
      { id: 5, text: "Music Instruments", selected: false },
      { id: 3, text: "Art", selected: false },
      { id: 1, text: "Beauty & Healing", selected: false },
      { id: 11, text: "Flim", selected: false },
    ],
  },
  {
    title: "Home Brands",
    open: false,
    items: [{ id: 1, text: "Mana", selected: false }],
  },
  {
    title: "Availability",
    open: false,
    items: [
      { id: 1, text: "India", selected: false },
      { id: 2, text: "World-wide", selected: false },
    ],
  },
]);

const toggleSection = (index) => {
  sections.value[index].open = !sections.value[index].open;
};

const getSelectedItems = () => {
  const result = {};

  sections.value.forEach((section) => {
    const selectedItems = section.items
      .filter((item) => item.selected && item.id)
      .map((item) => item.id);

    if (selectedItems.length) {
      result[section.title] = selectedItems;
    }
  });

  return result;
};

const handleFilterChange = () => {
  const selectedFilters = getSelectedItems();

  applyFilters(selectedFilters);
};

const applyFilters = (filters) => {
  const updates = {};

  if (filters.Artiste?.length) {
    updates.artistId = filters.Artiste.join();
  } else {
    updates.artistId = null;
  }

  if (filters.Categories?.length) {
    updates.categoryId = filters.Categories.join();
  } else {
    updates.categoryId = null;
  }

  if (filters.Availability?.length) {
    updates.countryId = filters.Availability.join();
  } else {
    updates.countryId = null;
  }

  appStore.updateFilters(updates);
};
</script>

<style scoped>
.container {
  margin-top: 10px;
  border-radius: 10px;
  padding: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.heading-arrow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 10px;
  padding: 8px 12px;
  color: white;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: background-color 0.3s;
}

.down-arrow {
  transition: transform 0.3s ease;
}

.down-arrow.rotated {
  transform: rotate(180deg);
}

.item-list {
  padding: 5px 15px;
}

.item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 4px 0;
}

.itemMob {
  width: 44%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  padding: 4px 0;
}

.checkbox-container {
  display: flex;
  gap: 10px;
  align-items: center;
}
</style>
