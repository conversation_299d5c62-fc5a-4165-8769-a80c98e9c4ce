<template>
  <footer class="footer">
    <div class="footer-content">
      <div class="footer-column left-column">
        <a href="https://artisteverse.com">
          <img
            src="https://artisteverse.com/img/footer_logo.png"
            alt="ArtistVerse Logo"
            class="footer-logo"
          />
        </a>
        <p class="powered-by">Powered by:</p>
        <img
          :src="poweredByLogoUrl"
          alt="Powered by Logo"
          class="powered-by-logo"
        />
      </div>

      <div class="footer-column center-column">
        <ul class="footer-links">
          <li><a :href="LINKS.faq">FAQ</a></li>
          <li><a :href="LINKS.aboutUs">About Us</a></li>
          <li><a :href="LINKS.terms">T&C</a></li>
        </ul>
        <div class="social-icons">
          <a :href="LINKS.facebook" class="social-icon"
            ><v-icon>mdi-facebook</v-icon></a
          >
          <a :href="LINKS.twitter" class="social-icon"
            ><v-icon>mdi-twitter</v-icon></a
          >
          <a :href="LINKS.instgaram" class="social-icon"
            ><v-icon>mdi-instagram</v-icon></a
          >
          <a :href="LINKS.linkedin" class="social-icon"
            ><v-icon>mdi-linkedin</v-icon></a
          >
        </div>
      </div>

      <div class="footer-column right-column">
        <ul class="footer-links">
          <li><a :href="LINKS.refund">Refund Policy</a></li>
          <li><a :href="LINKS.contact">Contact Us</a></li>
          <li><a :href="LINKS.privacy">Privacy Policy</a></li>
        </ul>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { ref } from "vue";
import artistLogo from "@/assets/artist_logo.png";
import { LINKS } from "@/helper/common";

const poweredByLogoUrl = ref(
  "https://d2b4rv4q8lb0q0.cloudfront.net/assets/company/metastarlogo.png"
);
</script>

<style scoped>
.footer {
  padding: 2rem 0;
  position: relative;
}

.footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: white;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.footer-column {
  flex: 1 1 300px;
  padding: 0 1rem;
  margin-bottom: 1.5rem;
}

.footer-logo {
  max-width: 200px;
}

.powered-by-logo {
  max-width: 100px;
  height: auto;
  margin-bottom: 0.5rem;
}

.company-name {
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.powered-by {
  margin-bottom: 0.25rem;
  font-size: small;
}

.footer-links {
  list-style-type: none;
  padding: 0;
  margin-bottom: 1rem;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  text-decoration: none;
  color: white;
}

.social-icons {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.social-icon {
  font-size: 1.2rem;
  color: white;
}

@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .footer-column {
    flex-basis: 100%;
    margin-bottom: 2rem;
  }

  .left-column {
    order: -1;
  }

  .footer-links {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }

  .footer-links li {
    margin-bottom: 0;
  }

  .social-icons {
    justify-content: center;
    margin-top: 1rem;
  }
}
</style>
