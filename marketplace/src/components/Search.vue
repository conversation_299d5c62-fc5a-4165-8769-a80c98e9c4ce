<template>
  <div class="search-input-container">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="search-icon"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
      />
    </svg>
    <input
      v-model="searchText"
      type="text"
      placeholder="Search"
      class="search-input"
    />
  </div>
</template>

<script>
export default {
  name: "SearchInput",
  data() {
    return {
      searchText: "",
    };
  },
};
</script>

<style scoped>
.search-input-container {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: 10px 10px 10px 40px;
  border: 1px solid #ccc;
  border-radius: 50px;
  font-size: 16px;
  outline: none;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #888;
  z-index: 1;
}

.search-input::placeholder {
  color: #888;
}
</style>
