<template>
  <v-container>
    <v-row>
      <v-col cols="12" sm="4">
        <v-img :src="data.image" height="300" cover class="rounded-lg"> </v-img>
      </v-col>
      <v-col cols="12" sm="8">
        <div class="text-subtitle-1 mb-2">By {{ data.authorName }}</div>
        <h1 class="text-h4 font-weight-bold mb-4">{{ data.title }}</h1>
        <v-divider
          class="mb-2 border-opacity-50"
          style="width: 200px"
          dark
          thickness="1"
        ></v-divider>
        <div class="text-h5 font-weight-bold mb-6">
          {{ data.currency }} {{ formatIndianCurrency(data.price) }}
        </div>

        <v-row class="mt-2 mb-2" style="max-width: 400px">
          <v-col>
            <v-btn
              block
              height="50"
              color="white"
              class="white--text rounded-xl"
              @click="buyNow"
              v-if="!data.isPurchased"
            >
              Buy Now
            </v-btn>
            <v-btn
              block
              height="50"
              color="white"
              class="white--text rounded-xl"
              @click="accessNow"
              v-if="data.isPurchased"
            >
              Access Now
            </v-btn>
          </v-col>
        </v-row>

        <div class="d-flex align-center mb-4">
          <div class="mr-2">Available:</div>
          <img
            :src="india_flag"
            alt="In Stock"
            class="availability-icon mr-1"
          />
          <img
            :src="globe_flag"
            alt="Global Shipping"
            class="availability-icon"
          />
        </div>

        <v-chip variant="outlined" class="mr-2 mb-2 custom-chip">
          {{ getDurationString(data.duration) }}
        </v-chip>
        <v-chip variant="outlined" class="mr-2 mb-2 custom-chip">
          {{ data.language }}
        </v-chip>
        <v-chip variant="outlined" class="mr-2 mb-2 custom-chip">
          Updated on
          {{
            new Date(data.updated).toLocaleDateString("en-US", {
              month: "long",
              day: "numeric",
              year: "numeric",
            })
          }}
        </v-chip>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { useRouter } from "vue-router";

import { formatIndianCurrency, getDurationString } from "@/helper/common";

import india_flag from "@/assets/india_flag.webp";
import globe_flag from "@/assets/global.webp";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const router = useRouter();

const buyNow = () => {
  router.push({
    path: `/singleCheckout/${props.data.id}`,
    query: {
      productId: props.data.id,
      image: props.data.image,
      name: props.data.title,
      tax: props.data.tax,
      price: props.data.price,
      currency: props.data.currency,
      quantity: 1,
      type: 3,
    },
  });
};

const accessNow = () => {
  router.push({
    path: `/course-video/${props.data.id}`,
  });
};
</script>

<style scoped>
.add-to-bag-btn {
  border-radius: 30px !important;
  font-weight: bold;
  color: black !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 0 7px 0 20px !important;
  position: relative !important;
  overflow: hidden !important;
}

.button-text {
  position: absolute !important;
  left: 20px !important;
}

.shopping-icon {
  position: absolute !important;
  right: 7px !important;
}

.availability-icon {
  width: 30px;
  height: 30px;
  object-fit: contain;
}

.custom-chip {
  background-color: transparent !important;
  color: white !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
}

.custom-chip:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}
</style>
