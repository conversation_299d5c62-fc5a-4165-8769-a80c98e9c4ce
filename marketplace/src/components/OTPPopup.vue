<template>
    <v-dialog v-model="dialog" max-width="400px" persistent>
      <v-card class="elevation-12 black">
        <v-card-title class="text-right">
          <v-btn icon @click="closeDialog" class="white--text">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text class="text-center">
          <v-img
            :src="artistLogo"
            alt="AV Logo"
            contain
            height="100"
            class="mb-4"
          ></v-img>
        </v-card-text>
        <v-card-text>
          <v-form @submit.prevent="onVerifyOTP">
            <p class="text-center mb-4 white--text">Enter the OTP sent to {{ email }}</p>
            <CustomInput
              v-model="otp"
              placeholder="Enter OTP"
              name="otp"
              prepend-inner-icon="mdi-numeric"
              type="text"
              custom-class="mb-6"
            />
            <v-row justify="center">
              <v-col cols="12" sm="6" class="text-center">
                <v-btn
                  type="submit"
                  color="white"
                  block
                  rounded
                  outlined
                  class="black--text custom-button custom-outlined"
                >
                  Verify OTP
                </v-btn>
              </v-col>
            </v-row>
            <v-row justify="center" class="mt-4">
              <v-col cols="12" class="text-center">
                <v-btn
                  text
                  color="white"
                  :disabled="resendCountdown > 0"
                  @click="resendOTP"
                  class="custom-text-button"
                >
                  {{ resendButtonText }}
                </v-btn>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </v-card>
    </v-dialog>
  </template>
  
  <script setup>
  import { ref, watch, computed, onUnmounted } from "vue";
  import artistLogo from "@/assets/artist_logo.png";
  
  const props = defineProps({
    modelValue: Boolean,
    email: String,
  });
  
  const emit = defineEmits(["update:modelValue", "verify-otp", "resend-otp"]);
  
  const dialog = ref(props.modelValue);
  const otp = ref("");
  const resendCountdown = ref(0);
  let countdownInterval = null;
  
  const resendButtonText = computed(() => {
    if (resendCountdown.value > 0) {
      return `Resend OTP in ${resendCountdown.value}s`;
    }
    return "Resend OTP";
  });
  
  watch(
    () => props.modelValue,
    (newValue) => {
      dialog.value = newValue;
      if (newValue) {
        startResendCountdown();
      }
    }
  );
  
  watch(dialog, (newValue) => {
    emit("update:modelValue", newValue);
  });
  
  const onVerifyOTP = () => {
    emit("verify-otp", otp.value);
  };
  
  const closeDialog = () => {
    dialog.value = false;
  };
  
  const startResendCountdown = () => {
    resendCountdown.value = 30;
    countdownInterval = setInterval(() => {
      if (resendCountdown.value > 0) {
        resendCountdown.value--;
      } else {
        clearInterval(countdownInterval);
      }
    }, 1000);
  };
  
  const resendOTP = () => {
    emit("resend-otp");
    startResendCountdown();
  };
  
  onUnmounted(() => {
    if (countdownInterval) {
      clearInterval(countdownInterval);
    }
  });
  </script>
  
  <style scoped>
  .custom-button {
    height: 48px !important;
    text-transform: none !important;
    font-weight: normal !important;
    font-size: 16px !important;
    letter-spacing: normal !important;
  }
  
  .custom-outlined {
    border: 1px solid white !important;
  }
  
  .custom-text-button {
    text-transform: none !important;
    font-weight: normal !important;
    font-size: 14px !important;
    letter-spacing: normal !important;
  }
  
  :deep(.v-btn__content) {
    opacity: 1 !important;
  }
  
  :deep(.v-card) {
    background-color: black !important;
  }
  
  :deep(.v-card__text) {
    color: white !important;
  }
  </style>
  