<template>
  <header class="header">
    <div class="logo-container" @click="logoClick">
      <img src="https://artisteverse.com/img/logo.png" alt="ArtisteVerse Logo" class="logo-image" />
    </div>
    <nav class="navigation desktop-nav">

      <button @click="openLink(LINKS.aboutUs)">About Us</button>
      <button @click="onStoreClick">Store</button>
      <button @click="loginClick" v-if="!isAuthenticated">Log In</button>
      <button @click="profileClick" v-if="isAuthenticated">Profile</button>
      <v-btn @click="cartClick" icon>
        <v-icon>mdi-shopping</v-icon>
      </v-btn>
      <button @click="logoutClick" v-if="isAuthenticated">Log Out</button>
    </nav>
    <v-btn icon class="mobile-menu-btn" @click="toggleMobileMenu">
      <v-icon>mdi-menu</v-icon>
    </v-btn>

    <v-dialog v-model="mobileMenuOpen" fullscreen transition="dialog-bottom-transition">
      <v-card class="mobile-menu">
        <v-toolbar dark color="transparent">
          <v-btn icon dark @click="toggleMobileMenu">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-list>
          <!-- <v-list-item>
            <v-select :items="items" variant="outlined" v-model="selectedCurrency"
              @update:modelValue="handleCurrencyChange"></v-select>
          </v-list-item> -->
          <v-list-item @click="toggleMobileMenu">
            <v-list-item-title>About Us</v-list-item-title>
          </v-list-item>
          <v-list-item @click="toggleMobileMenu">
            <v-list-item-title>Store</v-list-item-title>
          </v-list-item>
          <v-list-item @click="profileClick">
            <v-list-item-title>Profile</v-list-item-title>
          </v-list-item>
          <v-list-item @click="toggleMobileMenu">
            <v-list-item-title>Log In</v-list-item-title>
          </v-list-item>
          <v-list-item @click="toggleMobileMenu">
            <v-list-item-title>
              <v-icon>mdi-shopping</v-icon>
              Shopping Bag
            </v-list-item-title>
          </v-list-item>
        </v-list>
      </v-card>
    </v-dialog>
  </header>
</template>

<script setup>
import { ref } from "vue";
import {
  useRouter
} from "vue-router";
import { getUserToken } from "@/services/userService";
import { LINKS } from "@/helper/common";

const mobileMenuOpen = ref(false);
const isAuthenticated = ref(false);
const router = useRouter();

const checkAuthStatus = () => {
  isAuthenticated.value = !!getUserToken();
};

onMounted(() => {
  checkAuthStatus();
});

watch(
  () => router.currentRoute.value,
  () => {
    checkAuthStatus();
  },
  { deep: true }
);

const openLink = (URL) => {
  window.location.href = URL;
};

const onStoreClick = () => {
  router.push({
    path: "/",
  });
};

const profileClick = () => {
  router.push({
    path: "/profile",
  });
};

const logoClick = () => {
  window.location.replace("https://artisteverse.com");
};

const loginClick = () => {
  router.push({
    path: "/login",
  });
};

const logoutClick = () => {
  localStorage.clear();
  router.push({
    path: "/login",
  });
};

const cartClick = () => {
  router.push({
    path: "/cart",
  });
};

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
};
</script>

<style scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  margin-left: 1rem;
  margin-right: 1rem;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-container:hover {
  cursor: pointer;
}

.logo-image {
  height: 40px;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: bold;
}

.navigation {
  display: flex;
  align-items: center;
  gap: 1rem;
}

button {
  padding: 0.5rem 1rem;
  border: none;
  background-color: transparent;
  cursor: pointer;
}

.mobile-menu-btn {
  display: none;
}

.mobile-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
}

@media (max-width: 768px) {
  .desktop-nav {
    display: none;
  }

  .mobile-menu-btn {
    display: block;
  }
}
</style>
