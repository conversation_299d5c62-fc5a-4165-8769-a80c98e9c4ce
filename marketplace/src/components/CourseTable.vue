<template>
  <v-row>
    <v-col cols="12">
      <!-- Header with Chapters and Filter Button -->
      <div class="d-flex justify-space-between align-center mb-4">
        <h4 class="text-white ma-0">Chapters</h4>
        <v-menu v-if="hasFilterKeywords">
          <template v-slot:activator="{ props }">
            <v-btn
              v-bind="props"
              class="bg-white"
              variant="flat"
              density="comfortable"
            >
              <v-icon left class="mr-2">mdi-filter-variant</v-icon>
              Filter by Category
            </v-btn>
          </template>
          <v-list>
            <v-list-item
              v-for="(item, index) in safeFilterKeywords"
              :key="index"
              @click="filterByKeyword(item)"
            >
              <v-list-item-title>{{ item }}</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>

      <!-- Search Field -->
      <v-text-field
        v-model="search"
        prepend-inner-icon="mdi-magnify"
        label="Search"
        single-line
        hide-details
        filled
        rounded
        dense
        class="mb-4"
      ></v-text-field>

      <!-- Results Dialog for Filter -->
      <v-dialog v-model="showResults" max-width="600">
        <v-card class="dialog-dark">
          <v-card-title class="dialog-title pa-4">
            <span>Filter Results</span>
            <v-btn
              icon
              variant="text"
              @click="showResults = false"
              class="close-button"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-card-title>
          <v-divider></v-divider>
          <v-card-text class="pa-4">
            <v-list class="dialog-list">
              <template v-if="filteredResults.length > 0">
                <v-list-item
                  v-for="(episode, index) in filteredResults"
                  :key="index"
                  @click="playEpisode(episode)"
                  class="list-item mb-2"
                  rounded
                >
                  <template v-slot:prepend>
                    <v-icon 
                      :icon="episode.access == 1 || purchased ? 'mdi-play' : 'mdi-lock'"
                      class="mr-2"
                    ></v-icon>
                  </template>
                  <v-list-item-title>{{ episode.name }}</v-list-item-title>
                </v-list-item>
              </template>
              <v-list-item v-else class="text-center">
                <v-list-item-title>No results found</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-dialog>

      <!-- Chapters List -->
      <v-expansion-panels flat>
        <v-expansion-panel
          v-for="(chapter, index) in displayedChapters"
          :key="chapter.id"
          class="bg-transparent"
        >
          <v-expansion-panel-title class="text-white">
            {{ index + 1 }}. {{ chapter.name }}
          </v-expansion-panel-title>
          <v-expansion-panel-text>
            <template v-for="(play, index1) in chapter.playList" :key="play.id">
              <v-row
                v-if="matchesSearch(play.name)"
                @click="playVideo(id, index, index1, play.streamingUrl)"
                class="flex-nowrap"
                :class="play.access == 1 || purchased ? 'black-text' : 'grey-text'"
                no-gutters
              >
                <v-col cols="1" class="flex-grow-0 flex-shrink-0">
                  <v-icon :icon="play.access == 1 || purchased ? 'mdi-play' : 'mdi-lock'"></v-icon>
                </v-col>
                <v-col cols="8" class="text-left flex-grow-1 flex-shrink-0" style="min-width: 120px; max-width: 100%">
                  <div class="text-subtitle-1">
                    {{ index + 1 + "." + (index1 + 1) + " " + play.name }}
                  </div>
                </v-col>
                <v-col cols="2" class="text-no-wrap text-right flex-grow-0 flex-shrink-1">
                  <div class="text-subtitle-1">{{ getDurationString(play.duration) }}</div>
                </v-col>
              </v-row>
            </template>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>

      <v-btn
        v-if="chapters.length > displayLimit"
        @click="loadMore"
        block
        text
        class="mt-4 text-white"
      >
        More
        <v-icon right>mdi-chevron-down</v-icon>
      </v-btn>
    </v-col>
  </v-row>
</template>

<script setup>
import { getDurationString } from "@/helper/common";
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();
const search = ref("");
const displayLimit = ref(10);
const showResults = ref(false);
const filteredResults = ref([]);
const filterKeywords = ref([]);

const props = defineProps({
  chapters: {
    type: Array,
    required: true,
  },
  id: {
    type: Number,
    required: true,
  },
  purchased: {
    type: Boolean,
    required: true,
  },
  filterKeywords: {
    type: Array,
    default: () => [],
  },
});

const safeFilterKeywords = computed(() => {
  return props.filterKeywords || [];
});

const hasFilterKeywords = computed(() => {
  return Array.isArray(props.filterKeywords) && props.filterKeywords.length > 0;
});

const matchesSearch = (name) => {
  if (!search.value) return true;
  return name.toLowerCase().includes(search.value.toLowerCase());
};

const filterByKeyword = (keyword) => {
  if (!keyword) return;
  
  const results = [];
  props.chapters.forEach((chapter) => {
    chapter.playList.forEach((episode) => {
      if (episode.name.includes(keyword)) {
        results.push({
          ...episode,
          chapterIndex: props.chapters.indexOf(chapter),
          episodeIndex: chapter.playList.indexOf(episode)
        });
      }
    });
  });
  filteredResults.value = results;
  showResults.value = true;
};

const playEpisode = (episode) => {
  const url = episode.streamingUrl;
  playVideo(props.id, episode.chapterIndex, episode.episodeIndex, url);
  showResults.value = false;
};

const displayedChapters = computed(() => {
  return props.chapters.slice(0, displayLimit.value);
});

const playVideo = (courseId, chapterIndex, playListIndex, streamingUrl) => {
  if (streamingUrl) {
    router.push({
      path: `/course-video/${courseId}`,
      query: {
        chapterIndex: chapterIndex,
        playListIndex: playListIndex,
      },
    });
  }
};

const loadMore = () => {
  displayLimit.value += 10;
};

onMounted(() => {
  filterKeywords.value = Array.isArray(props.filterKeywords) ? props.filterKeywords : [];
});
</script>

<style scoped>
.v-expansion-panel {
  background-color: transparent !important;
}

.v-expansion-panel::before {
  box-shadow: none !important;
}

.v-expansion-panel-title {
  min-height: 48px;
}

.v-expansion-panel-title::after {
  content: none !important;
}

.black-text {
  color: white;
}

.grey-text {
  color: rgba(255, 255, 255, 0.6);
}

.bg-white {
  color: #000 !important;
}

/* Dialog styles */
:deep(.dialog-dark) {
  background-color: #1E1E1E !important;
  color: white !important;
  border-radius: 8px;
}

.dialog-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white !important;
}

:deep(.dialog-list) {
  background-color: transparent !important;
  color: white !important;
}

.list-item {
  transition: background-color 0.2s ease;
  margin: 4px 0;
}

.list-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  cursor: pointer;
}

:deep(.close-button) {
  color: white !important;
}

:deep(.v-list-item) {
  color: white !important;
}

:deep(.v-list-item__content) {
  color: white !important;
}

:deep(.v-list-item-title) {
  color: white !important;
}

:deep(.v-divider) {
  border-color: rgba(255, 255, 255, 0.12) !important;
}
</style>
