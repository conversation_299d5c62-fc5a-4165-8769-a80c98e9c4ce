export const formatIndianCurrency = (number) => {
  const num = parseFloat(number);
  if (isNaN(num)) return "0.00";

  const [integerPart, decimalPart = "00"] = num.toFixed(2).split(".");

  let formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  formattedInteger = formattedInteger.replace(
    /^(\d+?)(?=(\d\d)+(\d)(?!\d))(\.\d+)?$/g,
    "$1,"
  );

  return `${formattedInteger}.${decimalPart}`;
};

export const checkIfSafari = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  return (
    userAgent.includes("safari") &&
    !userAgent.includes("chrome") &&
    !userAgent.includes("android")
  );
};

export const getDurationString = (duration) => {
  let string = "";

  if (duration.hours) {
    string += duration.hours + "h ";
  }

  if (duration.minutes) {
    string += duration.minutes + "m ";
  }

  if (duration.seconds) {
    string += duration.seconds + "s ";
  }

  return string;
};

export const isValidIndianMobileNumber = (number) => {
  const pattern = /^(?:(?:\+?91|0)\s?)?[6789]\d{9}$/;
  return pattern.test(number);
};

export const isValidEmail = (email) => {
  const re =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(String(email).toLowerCase());
};

export const isValidPassword = (password) => {
  const result = password.length >= 8;
  return result;
};

export const isValidUserNamePass = (userName, pass) => {
  if (
    (isValidEmail(userName) || isValidIndianMobileNumber(userName)) &&
    isValidPassword(pass)
  ) {
    return true;
  } else {
    return false;
  }
};

export const LINKS = {
  facebook: "https://www.facebook.com/artisteverse.in",
  twitter: "https://x.com/artisteverse",
  instgaram: "https://www.instagram.com/artisteverse",
  linkedin: "https://www.linkedin.com/company/metastar-media/",

  faq: "https://artisteverse.com/faq.php",
  aboutUs: "https://artisteverse.com/about-us.php",
  terms: "https://artisteverse.com/terms-of-use.php",

  refund: "https://artisteverse.com/refund-policy.php",
  contact: "https://artisteverse.com/contact-us.php",
  privacy: "https://artisteverse.com/privacy-policy.php",
};

export const sendDataToParent = (data) => {
  const targetOrigin = "*";
  window.parent.postMessage(data, targetOrigin);
};

export const getArtistName = (id) => {
  switch (Number(id)) {
    case 1:
      return "Planet Bickram";

    case 2:
      return "Manasi Scott";
    case 3:
      return "Purbayan Chatterjee";
    case 4:
      return "Priyadarsini Govind";
    case 5:
      return "Indian Ocean";
    default:
      return "Metastar";
  }
};

export const getOrderType = (id) => {
  switch (Number(id)) {
    case 1:
      return "PRODUCT";
    case 2:
      return "MEET";
    case 3:
      return "COURSE";
    case 4:
      return "PAID_VIDEO";
    default:
      return "PRODUCT";
  }
};
