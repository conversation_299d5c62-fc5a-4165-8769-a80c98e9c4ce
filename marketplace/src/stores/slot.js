// Utilities
import {
  getArtistDetail,
  getArtistDetailWithoutLogin,
} from "@/services/saleAssistService";
import { defineStore } from "pinia";

export const useSlotStore = defineStore("slot", {
  state: () => ({
    isLoading: false,
    errorMsg: null,
    artistDetail: {
      name: "",
      description: "",
      artist_name: "",
      bookId: "",
      eligibile: null,
      request_status: "",
      meet: {},
      metadata: {
        video: "",
      },
      image: "",
    },
  }),
  actions: {
    async getPopupArtist(id) {
      try {
        this.isLoading = true;
        const res = await getArtistDetail(id);
        this.artistDetail = {
          name: res.name,
          artist_name: res.artist_name,
          description: res.description,
          bookId: res.artist_meet_id,
          eligibile: res.eligibile,
          request_status: res.request_status,
          meet: res.meet,
          metadata: res.metadata,
          image: res.image,
        };
        this.isLoading = false;
      } catch (error) {
        this.isLoading = true;
        this.errorMsg = "Error in fetching popup artist sale assist";
        console.error("Error in fetching popup artist sale assist", error);
      }
    },

    async getPopupArtistOutLogin(id) {
      try {
        this.isLoading = true;
        const res = await getArtistDetailWithoutLogin(id);
        this.artistDetail = {
          name: res.name,
          description: res.description,
          artist_name: res.artist_name,
          bookId: res.artist_meet_id,
          eligibile: res.eligibile,
          request_status: res.request_status,
          meet: res.meet,
          metadata: res.metadata,
          image: res.image,
        };
        this.isLoading = false;
      } catch (error) {
        this.isLoading = true;
        this.errorMsg = "Error in fetching popup artist sale assist";
        console.error("Error in fetching popup artist sale assist", error);
      }
    },
  },
});
