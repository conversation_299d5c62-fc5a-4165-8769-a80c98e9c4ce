// Utilities
import { getTracks } from "@/services/karaokeService";
import { getAllProducts } from "@/services/productService";
import { defineStore } from "pinia";

export const useAppStore = defineStore("app", {
  state: () => ({
    artistId: null,
    categoryId: null,
    countryId: null,
    products: [],
    isLoading: false,
    isError: null,
    sortBy: null,
    karaokeList: [],
    selectedSong: {
      id: null,
      video_url: null,
      name: null,
    },
    currencyCode: "",
  }),
  actions: {
    async getKaraoke(id) {
      try {
        this.isLoading = true;
        const res = await getTracks(id);
        if (res.status) {
          this.karaokeList = res.data.track;
          this.selectedSong = this.karaokeList[0];
        }
        this.isLoading = false;
      } catch (error) {
        this.isLoading = true;
        console.error("Error in fetching karaoke", error);
      }
    },
    selectSong(item) {
      this.selectedSong = item;
    },
    async selectCurrencyCode() {
      try {
        const res = await fetch("https://ipapi.co/json/");
        const data = await res.json();
        const code = data.country === "IN" ? "INR" : "USD";
        this.currencyCode = code;
        sessionStorage.setItem("currency", code);
      } catch (err) {
        console.error("Geo fetch failed:", err);
      }
    },
    async getProductsStore() {
      try {
        this.isLoading = true;
        const result = await getAllProducts(
          this.artistId,
          this.categoryId,
          this.countryId
        );
        this.products = result;
        this.isLoading = false;
      } catch (error) {
        this.isLoading = false;
        this.isError = "Error in getting products";
      }
    },

    updateFilters({ artistId = null, categoryId = null, countryId = null }) {
      if (artistId !== null) this.artistId = artistId;
      if (categoryId !== null) this.categoryId = categoryId;
      if (countryId !== null) this.countryId = countryId;

      this.getProductsStore();
    },

    filterByArtist(id) {
      this.updateFilters({ artistId: id });
    },

    filterByCountry(id) {
      this.updateFilters({ countryId: id });
    },

    filterByCategory(id) {
      this.updateFilters({ categoryId: id });
    },

    sortProducts(sortBy) {
      this.products = this.products.sort((a, b) => {
        if (sortBy === "Name") {
          return a.product_name.localeCompare(b.product_name);
        } else if (sortBy === "Price") {
          return a.currencies[0].price - b.currencies[0].price;
        }
      });
    },
  },
});
