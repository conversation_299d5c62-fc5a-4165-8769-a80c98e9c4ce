import { apiWrapper } from "../helper/apiWrapper";

export const registerSaleAssistUser = async (body, header) => {
    try {
        const response = await apiWrapper.post("/api/register-user", body, header)
        if (response.status == 200) {
            const data = response.data;
            return data.data;
        }

        if (response?.response) {
            console.error("Error In Register Sale Assist API Internal", response.response.data)
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });

    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}

export const startMeetBook = async (body, header) => {
    try {
        const response = await apiWrapper.post("/api/init-meet", body, header)
        if (response.status == 200) {
            const data = response.data;
            return data;
        }

        if (response?.response) {
            console.error("Error In Register Sale Assist API Internal", response.response.data)
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });

    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}

export const cancelMeetBook = async (body, header) => {
    try {
        const response = await apiWrapper.post("/api/cancel-meet", body, header)
        if (response.status == 200) {
            const data = response.data;
            return data.payment;
        }

        if (response?.response) {
            console.error("Error In Register Sale Assist API Internal", response.response.data)
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });

    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}

export const getArtistDetail = async (artistId = 5) => {
    try {
        const response = await apiWrapper.get(`/api/getArtistBookDetail/${artistId}`);
        if (response.status == 200) {
            const data = response.data.data[0];
            return data;
        }
        if (response?.response) {
            console.error("Error In Get Artist Book API Internal", response.response.data)
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });
    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}


export const getArtistDetailWithoutLogin = async (artistId = 5) => {
    try {
        const response = await apiWrapper.get(`/api/getArtistBookWithoutLogin/${artistId}`);
        if (response.status == 200) {
            const data = response.data.data[0];
            return data;
        }
        if (response?.response) {
            console.error("Error In Get Artist Book API Internal", response.response.data)
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });
    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}

export const startMeetBookRP = async (body, header) => {
    try {
        const response = await apiWrapper.post("/api/v2/init-meet", body, header)
        if (response.status == 200) {
            const data = response.data;
            return data.data.payment;
        }

        if (response?.response) {
            console.error("Error In Register Sale Assist API Internal", response.response.data)
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });

    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}
