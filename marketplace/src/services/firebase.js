import { initializeApp } from "firebase/app";
import { getAuth, FacebookAuthProvider,TwitterAuthProvider, signInWithPopup, signOut } from "firebase/auth";

const firebaseConfig = {
    apiKey: "AIzaSyC8eJT2VQMFCufTWZRspF0pSvWyCo6pQ7I",
    authDomain: "artisteverse-c7b44.firebaseapp.com",
    projectId: "artisteverse-c7b44",
    storageBucket: "artisteverse-c7b44.firebasestorage.app",
    messagingSenderId: "1077850222475",
    appId: "1:1077850222475:web:567ffac006be2771db098e",
    measurementId: "G-XPK74Y5R5J"
  };

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const Facebookprovider = new FacebookAuthProvider();
Facebookprovider.addScope("email");
const TwitterProvider = new TwitterAuthProvider();

export { auth, Facebookprovider,TwitterProvider, signInWithPopup, signOut };