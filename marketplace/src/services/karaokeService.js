// import router from "@/router";
import { useRouter } from "vue-router";
import { apiWrapper } from "../helper/apiWrapper";

export const sendAudio = async (body, header) => {
  try {
    const response = await apiWrapper.post(
      "/api/process-karaoke",
      body,
      header
    );
    if (response.status == 200) {
      const data = response.data;
      return data;
    }

    if (response?.response) {
      console.error("Error In karaoke API Internal", response.response.data);
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const getTracks = async (artist_id = 1) => {
  const router = useRouter();

  try {
    const response = await apiWrapper.get(`/api/getKaraokeTracks/${artist_id}`);
    if (response.status == 200) {
      const data = response.data;
      return data;
    }

    if (response.status == 401) {
      router.push({
        path: `/login`,
        query: {
          toSend: `/karaoke/${artist_id}`,
          query: {},
        },
      });
      return;
    }

    if (response?.response) {
      console.error("Error In Get Tracks API Internal", response.response.data);
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};
