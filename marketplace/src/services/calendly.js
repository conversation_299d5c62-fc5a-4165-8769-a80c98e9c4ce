import { apiWrapper } from "@/helper/apiWrapper";

export const registerSaleAssistUser = async (body, header) => {
  try {
    const response = await apiWrapper.post("/api/register-user", body, header);
    if (response.status == 200) {
      const data = response.data;
      return data.data;
    }

    if (response?.response) {
      console.error(
        "Error In Register Sale Assist API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const getAvailableSlot = async (body, header) => {
  try {
    const response = await apiWrapper.post(
      "/api/getAvailableSlot",
      body,
      header
    );
    if (response.status == 200) {
      const data = response.data;
      return data.data;
    }

    if (response?.response) {
      console.error(
        "Error In Register Sale Assist API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const startCalendlyCheckout = async (body, header) => {
  try {
    const response = await apiWrapper.post(
      "/api/start-calendly-checkout",
      body,
      header
    );
    if (response.status == 200) {
      const data = response.data;
      return data.data;
    }

    if (response?.response) {
      console.error(
        "Error In Register Sale Assist API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};
export const startCalendlyPayment = async (body, header) => {
  try {
    const response = await apiWrapper.post(
      "/api/start-calendly-payment",
      body,
      header
    );
    if (response.status == 200) {
      const data = response.data;
      return data.data;
    }

    if (response?.response) {
      console.error(
        "Error In Register Sale Assist API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const cancelCalendlyEvent = async (body, header) => {
  try {
    const response = await apiWrapper.post(
      "/api/cancel-calendly-event",
      body,
      header
    );
    if (response.status == 200) {
      const data = response;
      return data;
    }

    if (response?.response) {
      console.error(
        "Error In Register Sale Assist API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const callingArtistDataViaSource = async (source_id) => {
  try {
    const response = await apiWrapper.get(
      `/api/callingArtistDataViaSource/${source_id}`
    );
    if (response.status == 200) {
      const data = response.data;
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In Get Artist Book API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};
