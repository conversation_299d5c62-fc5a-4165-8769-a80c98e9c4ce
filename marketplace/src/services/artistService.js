import { apiWrapper } from "../helper/apiWrapper";
import { useRouter } from "vue-router";

export const getPopupContent = async (popupId) => {
  const router = useRouter();
  try {
    const response = await apiWrapper.get(`/api/popup?id=${popupId}`);
    const data = response.data;
    if (response.status == 200) {
      return data.data;
    }

    if (response.status == 401) {
      router.push({
        path: `/login`,
        query: {
          toSend: `/popup/space/${popupId}`,
          query: {},
        },
      });
      return;
    }

    if (response?.response) {
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const getVideoContent = async (popupId) => {
  const router = useRouter();

  try {
    const response = await apiWrapper.get(`/api/video?id=${popupId}`);
    const data = response.data;
    if (response.status == 200) {
      return data.data;
    }

    if (response.status == 401) {
      router.push({
        path: `/login`,
        query: {
          toSend: `/video/${popupId}`,
          query: {},
        },
      });
      return;
    }
    if (response?.response) {
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const getHTMLPopupContent = async (popupId) => {
  const router = useRouter();

  try {
    const response = await apiWrapper.get(
      `/api/getHTMLPopup?popupId=${popupId}`
    );
    const data = response.data;
    if (response.status == 200) {
      return data.data;
    }

    if (response.status == 401) {
      router.push({
        path: `/login`,
        query: {
          toSend: `/popup/html/${popupId}`,
          query: {},
        },
      });
      return;
    }

    if (response?.response) {
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const checkingUser = async () => {
  try {
    const response = await apiWrapper.get(`/api/checkingUser`);
    const data = response;
    if (response.status) {
      return data.data;
    }

    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const checkAvailable = async (userUri) => {
  try {
    const response = await apiWrapper.get(
      `/api/checkAvailable?userUri=${userUri}`
    );
    const data = response;
    if (response.status) {
      return data.data;
    }

    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};
