import { apiWrapper } from "../helper/apiWrapper";


export const getPaidVideoDetail = async (videoId) => {
    try {
        const response = await apiWrapper.get(`/api/paid-video-details/${videoId}`)
        const data = response.data;
        if (response.status == 200) {
            return data.data;
        }
        if (response?.response) {
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });

    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}

export const startSubscribe = async (payload) => {
    try {
        const response = await apiWrapper.post(`/api/subscribe-video`,payload)
        const data = response.data;
        if (response.status == 200) {
            return data;
        }
        if (response?.response) {
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });

    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}

export const startSubscribeRP = async (payload) => {
    try {
        const response = await apiWrapper.post(`/api/v2/subscribe-video`,payload)
        const data = response.data;
        if (response.status == 200) {
            return data.data;
        }
        if (response?.response) {
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });

    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}

export const cancelVideoBuy = async (body, header) => {
    try {
        const response = await apiWrapper.post("/api/cancel-video-purchase", body, header)
        if (response.status == 200) {
            const data = response.data;
            return data;
        }
        if (response?.response) {
            console.error("Error In Cancel Course Buy API Internal", response.response.data)
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });
    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}

export const getProductIdByItemId = async (itemId, productType) => {
    try {
        const response = await apiWrapper.post("/api/get-product-id", { itemId, productType })
        const data = response.data;
        if (response.status == 200) {
            return data.data.productId;
        }
        if (response?.response) {
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });

    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something went wrong while getting the product Id",
            rawError: error
        });
    }
}