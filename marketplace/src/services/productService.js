import { apiWrapper } from "../helper/apiWrapper";

export const getAllProducts = async (artistId, categoryId, countryId) => {
  try {
    const queryParts = [];

    if (artistId != null && artistId !== "") {
      queryParts.push(`artistId=${artistId}`);
    }

    if (categoryId != null && categoryId !== "") {
      queryParts.push(`categoryId=${categoryId}`);
    }

    if (countryId != null && countryId !== "") {
      queryParts.push(`countryId=${countryId}`);
    }

    const queryString = queryParts.length > 0 ? `?${queryParts.join("&")}` : "";
    const url = `/api/allProducts${queryString}`;

    const response = await apiWrapper.get(url);
    if (response.status == 200) {
      const result = response.data;
      const resultData = result.data;

      const hiddenProducts = [
        { artist: "bickram ghosh", productId: 9 },
      ];

      return filterBlacklistedProducts(resultData, hiddenProducts)
    }
    if (response?.response) {
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

function filterBlacklistedProducts(resultData, hiddenProducts) {
  if (!Array.isArray(resultData)) {
    return [];
  }

  return resultData.filter(item => {
    const artistName = item.artist_name?.toLowerCase();
    const productId = parseInt(item.product_id);

    return !hiddenProducts.some(hiddenItem => 
      hiddenItem.artist === artistName && 
      hiddenItem.productId === productId
    );
  });
}

const processProductDetailResp = (data) => {
  let imageList = data.details.dispaly_images;
  let productDetail = data.details;
  let selectedImage = {};
  if (data.details.dispaly_images.length) {
    selectedImage = data.details.dispaly_images[0];
  }
  let dropDownValues = {};
  data.details.variation_order.forEach((data) => {
    const key = Object.keys(data)[0];
    const value = data[key];
    dropDownValues[key] = value;
  });
  let isVariation = Object.keys(dropDownValues).length;

  let sendData = {
    imageList,
    productDetail,
    selectedImage,
    dropDownValues,
    isVariation,
  };
  return sendData;
};
export const getProductById = async (id) => {
  try {
    const response = await apiWrapper.get(`/api/product-details/${id}`);
    const data = response.data;
    if (response.status == 200) {
      if (data.data.details) {
        return processProductDetailResp(data.data);
      }
      return null;
    }
    if (response?.response) {
      console.error(
        "Error In getting Product Detail API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const updateCartAPI = async (body, header) => {
  try {
    const response = await apiWrapper.post("/api/cart", body, header);
    if (response.status == 200) {
      const data = response.data;
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In update cart API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const getCart = async (artistId, currency, coupon) => {
  try {
    const response = await apiWrapper.get(
      `/api/cart/${artistId}?${currency}&${coupon}`
    );
    if (response.status == 200) {
      const data = response.data.data.cartItems.map((el, index) => {
        return {
          ...el,
          id: el.product_item_id,
        };
      });
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In getting cart API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};
export const cartSingle = async (currency, coupon, productItemId, type) => {
  try {
    const response = await apiWrapper.get(
      `/api/cartSingle?${currency}&${coupon}&${productItemId}&${type}`
    );
    if (response.status == 200) {
      const data = response.data.data.cartItems.map((el, index) => {
        return {
          ...el,
          id: el.product_item_id,
        };
      });
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In getting cart API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const updateBulkCartAPI = async (body) => {
  try {
    const response = await apiWrapper.post("/api/bulk-cart", body);
    if (response.status == 200) {
      const data = response.data;
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In update Bulk cart API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const placeOrder = async (body) => {
  try {
    const response = await apiWrapper.post("/api/order", body);
    if (response.status == 200) {
      const data = response.data;
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In update cart API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const orderStatusCancel = async (body) => {
  try {
    const response = await apiWrapper.post("/api/cancelOrder", body);
    if (response.status == 200) {
      const data = response.data;
      return data;
    }

    if (response?.response) {
      console.error(
        "Error In Google Login API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const cancelOrder = async (body, header) => {
  try {
    const response = await apiWrapper.post("/api/cancel-payment", body, header);
    if (response.status == 200) {
      const data = response.data;
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In Cancel Order API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const getOrderHistory = async () => {
  try {
    const response = await apiWrapper.get(`/api/v1/orders`);
    if (response.status == 200) {
      const data = response.data.data;
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In Getting order list API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const singleOrderPlace = async (body) => {
  try {
    const response = await apiWrapper.post("/api/order-now", body);
    if (response.status == 200) {
      const data = response.data;
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In update cart API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const addWishList = async (body, header) => {
  try {
    const response = await apiWrapper.post("/api/addWishlist", body, header);
    if (response.status == 200) {
      const data = response.data;
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In update cart API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const removeWishList = async (body, header) => {
  try {
    const response = await apiWrapper.post("/api/removeWishlist", body, header);
    if (response.status == 200) {
      const data = response.data;
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In update cart API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const placeOrderPaypal = async (body) => {
  try {
    const response = await apiWrapper.post("/api/v2/order", body);
    if (response.status == 200) {
      const data = response.data;
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In update cart API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const orderNowinitMeetPaypal = async (body) => {
  try {
    const response = await apiWrapper.post("/api/v2/init-meet", body);
    if (response.status == 200) {
      const data = response.data;
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In update cart API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const orderNowCoursePaypal = async (body) => {
  try {
    const response = await apiWrapper.post("/api/v2/course-purchase", body);
    if (response.status == 200) {
      const data = response.data;
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In update cart API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const orderNowVideoPaypal = async (body) => {
  try {
    const response = await apiWrapper.post("api/v2/subscribe-video", body);
    if (response.status == 200) {
      const data = response.data;
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In update cart API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};
