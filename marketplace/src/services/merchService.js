import { apiWrapper } from "../helper/apiWrapper";

export const getOrderDetail = async (order_id, order_type) => {
    try {
        const response = await apiWrapper.get(`/api/order-detail?order_item_id=${order_id}&order_type=${order_type}`);
        if (response.status == 200) {
            const data = response.data.data;
            return data;
        }
        if (response?.response) {

            console.error("Error In Getting order detail API Internal", response.response.data)
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });
    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}
