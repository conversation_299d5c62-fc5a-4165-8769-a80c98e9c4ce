import { apiWrapper } from "@/helper/apiWrapper";

export const getUserToken = () => {
  const token = localStorage.getItem("token");
  return token;
};

export const removeUserToken = () => {
  localStorage.removeItem("token");
};

export const setUserToken = (token) => {
  localStorage.setItem("token", token);
  sendTokenToParent(token);
};

function sendTokenToParent(token, userData) {
  window.parent.postMessage({
    type: 'auth_token',
    token: token,
    user: userData || null
  }, '*');
}

export const updateUserName = (userName) => {
  if (userName == null) {
    localStorage.removeItem("userName");
    return;
  }
  localStorage.setItem("userName", userName);
};

export const getUserName = () => {
  const token = localStorage.getItem("userName");
  return token;
};

export const updateUserPassword = (value) => {
  if (value == null) {
    localStorage.removeItem("userName");
    return;
  }
  localStorage.setItem("userPass", value);
};

export const getUserPassword = () => {
  const token = localStorage.getItem("userPass");
  return token;
};

export const login = async (body, header) => {
  try {
    const response = await apiWrapper.post("/api/login", body, header);
    if (response.status == 200) {
      const data = response.data;
      setUserToken(data.data.token);
      return data;
    }

    if (response?.response) {
      console.error("Error In Login API Internal", response.response.data);
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const register = async (body, header) => {
  try {
    const response = await apiWrapper.post("/api/user", body, header);
    const data = response.data;
    if (response.status == 200) {
      return data;
    }
    if (response?.response) {
      console.error("Error In Login API Internal", response.response.data);
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export async function verifyOTP(body, header) {
  try {
    const response = await apiWrapper.post("/api/verify-otp", body, header);
    const data = response.data;
    if (response.status == 200) {
      setUserToken(data.data.token);
      return data;
    }
    if (response?.response) {
      console.error("Error In Verify API Internal", response.response.data);
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
}

export const resendOTP = async (body, header) => {
  try {
    const response = await apiWrapper.post("/api/send-otp", body, header);
    const data = response.data;
    if (response.status == 200) {
      return data;
    }
    if (response?.response) {
      console.error("Error In Resend OTP API Internal", response.response.data);
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const forgotPassword = async (body, header) => {
  try {
    const response = await apiWrapper.post(
      "/api/forgot-password",
      body,
      header
    );
    const data = response.data;
    if (response.status == 200) {
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In Forgot Password API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const resetPassword = async (body, header) => {
  try {
    const response = await apiWrapper.post(
      "/api/update-password",
      body,
      header
    );
    const data = response.data;
    if (response.status == 200) {
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In Reset Password API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const googleLoginAPI = async (body, header) => {
  try {
    const response = await apiWrapper.post("/api/google-user", body, header);
    if (response.status == 200) {
      const data = response.data;
      setUserToken(data.data.token);
      return data;
    }

    if (response?.response) {
      console.error(
        "Error In Google Login API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const facebookLoginAPI = async (body, header) => {
  try {
    const response = await apiWrapper.post("/api/facebook-user", body, header);
    if (response.status == 200) {
      const data = response.data;
      setUserToken(data.data.token);
      return data;
    }

    if (response?.response) {
      console.error(
        "Error In Google Login API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const twitterLoginAPI = async (body, header) => {
  try {
    const response = await apiWrapper.post("/api/twitter-user", body, header);
    if (response.status == 200) {
      const data = response.data;

      setUserToken(data.data.token);
      return data;
    }

    if (response?.response) {
      console.error(
        "Error In Google Login API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const getUserAddress = async () => {
  try {
    const response = await apiWrapper.get("/api/address");
    if (response.status == 200) {
      const data = response.data.data;
      return data;
    }

    if (response?.response) {
      console.error(
        "Error In Geting User Address API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const getUserAllAddress = async () => {
  try {
    const response = await apiWrapper.get("/api/getAddressAll");
    if (response.status == 200) {
      const data = response.data.data;
      return data;
    }

    if (response?.response) {
      console.error(
        "Error In Geting User Address API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const updateAddress = async (body, header) => {
  try {
    const response = await apiWrapper.post("/api/updateAddress", body, header);
    if (response.status == 200) {
      const data = response.data;
      return data;
    }

    if (response?.response) {
      console.error(
        "Error In Google Login API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const deleteAddressFromDb = async (body, header) => {
  try {
    const response = await apiWrapper.post("/api/deleteAddress", body, header);
    if (response.status == 200) {
      const data = response.data;
      return data;
    }

    if (response?.response) {
      console.error(
        "Error In Google Login API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const addAddress = async (body, header) => {
  try {
    const response = await apiWrapper.post("/api/address", body, header);
    if (response.status == 200) {
      const data = response.data;
      return data;
    }

    if (response?.response) {
      console.error(
        "Error In Google Login API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const getUserProfile = async () => {
  try {
    const response = await apiWrapper.get("/api/getUserProfile");
    if (response.status == 200) {
      const data = response.data.data;
      return data;
    }

    if (response?.response) {
      console.error(
        "Error In Geting User Address API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const addUserProfile = async (body, header) => {
  try {
    const response = await apiWrapper.post(
      "/api/insertUserProfile",
      body,
      header
    );
    if (response.status == 200) {
      const data = response.data;
      return data;
    }

    if (response?.response) {
      console.error(
        "Error In Google Login API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const updateUserProfile = async (body, header) => {
  try {
    const response = await apiWrapper.post(
      "/api/updateUserProfile",
      body,
      header
    );
    if (response.status == 200) {
      const data = response.data;
      return data;
    }

    if (response?.response) {
      console.error(
        "Error In Google Login API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};
