import { apiWrapper } from "../helper/apiWrapper";

export const meetRequest = async (body) => {
    try {
        const response = await apiWrapper.post(`/api/meet-request`, body);
        if (response.status == 200) {
            const data = response.data;
            return data;
        }
        if (response?.response) {
            console.error("Error In Meet Request API Internal", response.response.data)
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });
    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}
