import { apiWrapper } from "../helper/apiWrapper";

export const getCourseContent = async (body) => {
    try {
        const response = await apiWrapper.get(`/api/course-detail/?courseId=${body.courseId}&courselevelavail=${body.courselevelavail}&CurrencyCode=${body.CurrencyCode}`)
        const data = response.data;
        if (response.status == 200) {
            return data.data;
        }
        if (response?.response) {
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });

    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}

export const buyCourseContent = async (body, header) => {
    try {
        const response = await apiWrapper.post("/api/course-purchase", body, header)
        if (response.status == 200) {
            const data = response.data;
            return data;
        }

        if (response?.response) {
            console.error("Error In Buy Course API Internal", response.response.data)
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });

    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}

export const cancelCourseBuy = async (body, header) => {
    try {
        const response = await apiWrapper.post("/api/cancel-course-purchase", body, header)
        if (response.status == 200) {
            const data = response.data;
            return data;
        }
        if (response?.response) {
            console.error("Error In Cancel Course Buy API Internal", response.response.data)
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });
    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}

export const getVideoURL = async (videoId) => {
    try {
        const response = await apiWrapper.get(`/api/course-video/${videoId}`)
        const data = response.data;
        if (response.status == 200) {
            return data.data;
        }
        if (response?.response) {
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });

    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}

// export const buyRazorpayCourseContent = async (body, header) => {
//     try {
//         const response = await apiWrapper.post("/api/v2/course-purchase", body, header)
//         if (response.status == 200) {
//             const data = response.data;
//             return data;
//         }

//         if (response?.response) {
//             console.error("Error In Buy Course API Internal", response.response.data)
//             return Promise.reject({
//                 status: false,
//                 message: response.response.data.message,
//                 rawError: response.response
//             });
//         }
//         return Promise.reject({
//             status: false,
//             message: "Broken Connection",
//             rawError: response
//         });

//     } catch (error) {
//         console.error(error);
//         return Promise.reject({
//             status: false,
//             message: "Something Went Wrong on Connection",
//             rawError: error
//         });
//     }
// }
