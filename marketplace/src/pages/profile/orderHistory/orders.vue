<template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular
          :size="70"
          :width="7"
          color="white"
          indeterminate
        ></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <v-container v-else>
    <h4 class="pl-4 mb-6 text-white">Your Orders</h4>
    <!-- Scrollable Container -->
    <div class="order-list-container">
      <v-table v-if="items?.length > 0" class="hide-on-mobile">
        <thead>
          <tr>
            <th class="text-left text-white">Product</th>
            <th class="text-left text-white">Ordered on</th>
            <th class="text-left text-white">Status</th>
            <th class="text-left text-white"></th>
          </tr>
        </thead>
        <tbody>
          <template v-for="(item, index) in items" :key="item.id">
            <tr>
              <td>
                <v-row no-gutters class="mb-5">
                  <v-col cols="auto" class="mr-4">
                    <div class="image-container">
                      <img
                        :src="item.images"
                        height="100"
                        alt="Image 1"
                        class="image-front"
                      />
                    </div>
                  </v-col>
                  <v-col class="d-flex flex-column justify-space-between">
                    <div>
                      {{ `Order No :  ${item.order_id}` }}
                    </div>
                    <div>
                      {{ `x${item.order_item_count} Items` }}
                    </div>
                  </v-col>
                </v-row>
              </td>
              <td class="text-white">
                {{ moment(item.ordered_on).format("MMM Do YYYY") }}
              </td>
              <td class="text-white">{{ item.status.replace("_", " ") }}</td>
              <td class="text-right text-white">
                <v-btn
                  @click="onViewDetail(item.order_id, item.order_type)"
                  color="white"
                  class="view-order mb-2"
                  height="50"
                >
                  <span>View Order</span>
                </v-btn>
              </td>
            </tr>
          </template>
        </tbody>
      </v-table>
      <div v-if="items.length === 0">No Order History Found</div>
      <template v-for="(item, index) in items" :key="item.id">
        <v-row no-gutters class="mb-5 show-on-mobile">
          <v-col cols="auto" class="mr-4">
            <div>
              <img
                :src="item.images"
                height="200"
                alt="Image 1"
                class="image-front"
              />
            </div>
          </v-col>
          <v-col class="d-flex flex-column justify-space-between">
            <div>
              {{ `Order No :  ${item.order_id}` }}
            </div>
            <div>
              Ordered On {{ moment(item.ordered_on).format("MMM Do YYYY") }}
            </div>
            <div>
              {{ `x${item.order_item_count} Items` }}
            </div>
            <div>
              {{ item.status.replace("_", " ") }}
            </div>
            <div>
              <v-btn
                @click="onViewDetail(item.order_id, item.order_type)"
                color="white"
                class="view-order mb-2"
                height="50"
              >
                <span>View Order</span>
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </template>
    </div>
  </v-container>
</template>

<script setup>
import { onBeforeMount, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import moment from "moment";
import { getOrderHistory } from "@/services/productService";
import { getUserToken, setUserToken } from "@/services/userService";
import { sendDataToParent } from "@/helper/common";

const router = useRouter();
const route = useRoute();

let isLoading = ref(false);
const items = ref(null);

const onViewDetail = (orderId, order_type) => {
  router.push({
    path: `/orderDetail/${orderId}`,
    query: {
      order_type,
    },
  });
};

onBeforeMount(async () => {
  isLoading.value = true;

  if (route.query.token) {
    setUserToken(route.query.token);
  } else if (!getUserToken()) {
    sendDataToParent({ message: "Login Needed" });
    router.push({
      path: `/login`,
      query: {
        toSend: `/orderHistory`,
        query: {},
      },
    });
    return;
  }
  try {
    const resp = await getOrderHistory();
    items.value = resp.orders;
    isLoading.value = false;
  } catch (error) {
    isLoading.value = false;
    console.error("Error in order list API", error);
  }
});
</script>

<style scoped>
.v-table {
  background-color: transparent !important;
}

.v-table > .v-table__wrapper > table {
  background-color: transparent !important;
}

.v-table .v-table__wrapper > table > tbody > tr:not(:last-child) > td {
  border-bottom: none;
}

/* Remove border from table header */
.v-table .v-table__wrapper > table > thead > tr:last-child > th {
  border-bottom: none !important;
}

/* Add some padding to the header for better spacing */
.v-table .v-table__wrapper > table > thead > tr > th {
  padding-bottom: 16px;
}

/* Add some padding to the first row of the body for separation from header */
.v-table .v-table__wrapper > table > tbody > tr:first-child > td {
  padding-top: 16px;
}

.view-order {
  border-radius: 30px !important;
  font-weight: bold;
  color: black !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  position: relative !important;
  overflow: hidden !important;
}

.image-container {
  position: relative;
  width: 100px;
  height: 100px;
}

.image-container img {
  width: 100px;
  height: 100px;
  position: absolute;
}

.image-front {
  z-index: 1;
}

.order-list-container {
  padding: 16px;
}

@media (min-width: 601px) {
  .show-on-mobile {
    display: none;
  }
}

@media (max-width: 600px) {
  .hide-on-mobile {
    display: none;
  }
}
</style>
