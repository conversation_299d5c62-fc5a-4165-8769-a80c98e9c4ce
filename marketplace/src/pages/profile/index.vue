<template>
  <div :class="!isMobileOrTablet ? 'app-container' : ''">
    <div class="profile-sidebar" v-if="!isMobileOrTablet">
      <h1 class="sidebar-title">My Profile</h1>
      <v-list
        density="compact"
        lines="three"
        class="sidebar-list"
        style="background-color: black"
      >
        <v-list-item
          @click="currentSection = 'editProfile'"
          class="sidebar-item"
          :class="{ active: currentSection === 'editProfile' }"
        >
          <v-list-item-title class="text">Edit Profile</v-list-item-title>
        </v-list-item>
        <v-list-item
          @click="currentSection = 'manageAddress'"
          class="sidebar-item"
          :class="{ active: currentSection === 'manageAddress' }"
        >
          <v-list-item-title class="text">My Address</v-list-item-title>
        </v-list-item>
        <v-list-item
          @click="currentSection = 'orders'"
          class="sidebar-item"
          :class="{ active: currentSection === 'orders' }"
        >
          <v-list-item-title class="text">My Orders</v-list-item-title>
        </v-list-item>
        <v-list-item
          @click="currentSection = 'logout'"
          class="sidebar-item"
          :class="{ active: currentSection === 'logout' }"
        >
          <v-list-item-title class="text">Log Out</v-list-item-title>
        </v-list-item>
      </v-list>
    </div>

    <v-dialog
      v-model="dialog"
      max-width="90%"
      transition="dialog-bottom-transition"
      fullscreen
      v-if="isMobileOrTablet"
    >
      <v-card>
        <v-card-title>
          <div style="display: flex; justify-content: space-between">
            <h1>My Profile</h1>
            <div>
              <v-btn icon @click="dialog = false">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </div>
        </v-card-title>
        <v-card-text>
          <v-list density="compact" lines="three" class="sidebar-list">
            <v-list-item
              @click="selectSection('editProfile')"
              class="sidebar-item"
              :class="{ active: currentSection === 'editProfile' }"
            >
              <v-list-item-title class="text">Edit Profile</v-list-item-title>
            </v-list-item>
            <v-list-item
              @click="selectSection('manageAddress')"
              class="sidebar-item"
              :class="{ active: currentSection === 'manageAddress' }"
            >
              <v-list-item-title class="text">My Address</v-list-item-title>
            </v-list-item>
            <v-list-item
              @click="selectSection('orders')"
              class="sidebar-item"
              :class="{ active: currentSection === 'orders' }"
            >
              <v-list-item-title class="text">My Orders</v-list-item-title>
            </v-list-item>
            <v-list-item
              @click="selectSection('logout')"
              class="sidebar-item"
              :class="{ active: currentSection === 'logout' }"
            >
              <v-list-item-title class="text">Log Out</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-btn v-if="isMobileOrTablet" @click="dialog = true"> Open Menu </v-btn>

    <div class="main-content">
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import EditProfile from "./editProfile/editProfile.vue";
import ManageAddress from "./manageAddress/manageAddress.vue";
import Orders from "./orderHistory/orders.vue";
import { useRouter } from "vue-router";

const currentSection = ref("editProfile");
const router = useRouter();
const dialog = ref(false);
const isMobileOrTablet = ref(window.innerWidth <= 1024);

const updateDeviceType = () => {
  isMobileOrTablet.value = window.innerWidth <= 1024;
};

window.addEventListener("resize", updateDeviceType);

const logoutClick = () => {
  localStorage.clear();
  router.push({ path: "/login" });
};

const componentMap = {
  editProfile: EditProfile,
  manageAddress: ManageAddress,
  orders: Orders,
  logout: null,
};

const currentComponent = computed(() => {
  if (currentSection.value === "logout") {
    logoutClick();
    return null;
  }
  return componentMap[currentSection.value] || EditProfile;
});

const selectSection = (section) => {
  currentSection.value = section;
  dialog.value = false;
};
</script>

<style scoped>
.app-container {
  display: flex;
}

.profile-sidebar {
  width: 300px;
  border: 1px solid #ddd;
  display: flex;
  flex-direction: column;
}

.sidebar-title {
  border-top: 2px solid #f0f0f0;
  font-size: 24px;
  font-weight: 600;
  padding: 24px 4rem !important;
}

.sidebar-item {
  padding-left: 4rem !important;
  height: 10px !important;
  border-top: 2px solid #f0f0f0;
  border-bottom: 2px solid #f0f0f0;
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #fff;
}

.v-list-item--density-compact.v-list-item--three-line {
  min-height: 40px !important;
}

.sidebar-item:hover {
  background-color: #f0f0f0;
  color: #000 !important;
}

.sidebar-item.active {
  background-color: #ffff;
  color: #000 !important;
}

.sidebar-item .text {
  font-size: 20px;
  font-weight: 500;
}

.main-content {
  flex-grow: 1;
  overflow-y: auto;
}

@media (max-width: 1024px) {
  .profile-sidebar {
    display: none;
  }
}
</style>
