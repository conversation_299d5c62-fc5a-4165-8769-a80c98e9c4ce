<template>
  <div>
    <template v-if="isLoading">
      <v-row class="fill-height ma-0 align-center justify-center">
        <v-col cols="12" class="text-center">
          <v-progress-circular :size="70" :width="7" color="white" indeterminate></v-progress-circular>
        </v-col>
      </v-row>
    </template>

    <div v-if="!isLoading" class="address-container">
      <h2 class="mb-4">My Address</h2>
      <v-list-item v-if="addresses.length > 0" v-for="(address, index) in addresses" :key="index" class="address-item">
        <v-list-item-content>
          <v-row v-if="address.isEditing" class="edit-form">
            <v-col cols="12" md="12">
              <form @submit.prevent="validateAndSubmit(index)">
                <div class="input-group">
                  <CustomInput v-model="address.first_name" type="text" placeholder="First Name" :required="true"
                    :error="errors.first_name" />
                  <CustomInput v-model="address.last_name" type="text" placeholder="Last Name" :required="true"
                    :error="errors.last_name" />
                </div>
                <CustomInput v-model="address.phone" type="tel" placeholder="Phone Number" :required="true"
                  :error="errors.phone" />
                <CustomInput v-model="address.address_line_one" type="text" placeholder="Address line 1"
                  :required="true" :error="errors.address_line_one" />
                <CustomInput v-model="address.address_line_two" type="text" placeholder="Address line 2" />
                <CustomInput v-model="address.address_line_three" type="text" placeholder="Address line 3" />
                <div class="input-group">
                  <CustomInput v-model="address.city" type="text" placeholder="City" :required="true"
                    :error="errors.city" />
                  <CustomInput v-model="address.pincode" type="text" placeholder="Pincode" :required="true"
                    :error="errors.pincode" />
                </div>
                <div class="input-group">
                  <CustomInput v-model="address.state" type="text" placeholder="State" :required="true"
                    :error="errors.state" />
                  <CustomInput v-model="address.country" type="text" placeholder="Country" :required="true"
                    :error="errors.country" />
                </div>
                <CustomInput v-model="address.email" type="email" placeholder="Email" :required="true"
                  :error="errors.email" />
                <div class="button-group">
                  <v-btn @click="saveEditedAddress(index)" color="primary" rounded outlined
                    class="black--text custom-button custom-outlined">
                    Save Address
                  </v-btn>
                  <v-btn @click="cancelEditAddress(index)" color="white" rounded outlined
                    class="black--text custom-button custom-outlined">
                    Cancel
                  </v-btn>
                </div>
              </form>
            </v-col>
          </v-row>

          <div v-if="!address.isEditing && address?.first_name.length" class="d-flex justify-space-between address-card"
            :class="{
              selected: selectedAddressId == address.address_id,
              hovered: hoveredAddressId == address.address_id
            }" @click="updateSelected(address.address_id)" @mouseover="hoveredAddressId = address.address_id"
            @mouseleave="hoveredAddressId = null">
            <div class="d-flex align-center" style="gap: 10px">
              <div>
                <h2>{{ address.first_name }}</h2>
                <p>{{ address.address_line_one }}</p>
                <p>{{ address.state }}, {{ address.country }}</p>
                <p>{{ address.pincode }}</p>
              </div>
            </div>

            <v-icon v-if="!address.isEditing" @click.stop="editAddress(index)" color="#eee" size="18">mdi-pencil</v-icon>
          </div>
        </v-list-item-content>
      </v-list-item>

      <!-- Fixed: Apply same styling structure as existing addresses -->
      <div v-if="addingAddress" class="address-item">
        <div class="address-item-content">
          <form @submit.prevent="validateAndSubmit(index)">
            <div class="input-group">
              <CustomInput v-model="address.first_name" type="text" placeholder="First Name" :required="true"
                :error="errors.first_name" />
              <CustomInput v-model="address.last_name" type="text" placeholder="Last Name" :required="true"
                :error="errors.last_name" />
            </div>
            <CustomInput v-model="address.phone" type="tel" placeholder="Phone Number" :required="true"
              :error="errors.phone" />
            <CustomInput v-model="address.address_line_one" type="text" placeholder="Address line 1" :required="true"
              :error="errors.address_line_one" />
            <CustomInput v-model="address.address_line_two" type="text" placeholder="Address line 2" />
            <CustomInput v-model="address.address_line_three" type="text" placeholder="Address line 3" />
            <div class="input-group">
              <CustomInput v-model="address.city" type="text" placeholder="City" :required="true"
                :error="errors.city" />
              <CustomInput v-model="address.pincode" type="text" placeholder="Pincode" :required="true"
                :error="errors.pincode" />
            </div>
            <div class="input-group">
              <CustomInput v-model="address.state" type="text" placeholder="State" :required="true"
                :error="errors.state" />
              <CustomInput v-model="address.country" type="text" placeholder="Country" :required="true"
                :error="errors.country" />
            </div>
            <CustomInput v-model="address.email" type="email" placeholder="Email" :required="true"
              :error="errors.email" />
            <div class="button-group">
              <v-btn @click="saveNewAddress()" color="primary" rounded outlined
                class="black--text custom-button custom-outlined">
                Save Address
              </v-btn>
              <v-btn @click="cancelAddress()" color="white" rounded outlined
                class="black--text custom-button custom-outlined">
                Cancel
              </v-btn>
            </div>
          </form>
        </div>
      </div>

      <div v-if="!addingAddress" class="address-item">
        <div class="address-item-content text-center" @click="toggleAddingAddress" style="cursor: pointer;">
          <v-btn color="white"
            class="black--text custom-outlined add-address-btn">
            Add New Address
          </v-btn>
        </div>
      </div>

      <v-snackbar :color="snackbar.type ? 'success' : 'red'" rounded="pill" v-model="snackbar.show"
        :timeout="snackbar.timeout">
        {{ snackbar.message }}
      </v-snackbar>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import {
  getUserAllAddress,
  updateAddress,
  addAddress,
  getUserProfile,
  updateUserProfile,
  addUserProfile,
} from "@/services/userService";
import CustomInput from "@/components/CustomInput.vue";
import { useAppStore } from "@/stores/app";

const appStore = useAppStore();
let isLoading = ref(true);
const addresses = ref([]);
const selectedAddressId = ref(null);
const hoveredAddressId = ref(null);
const addingAddress = ref(false);
const address = ref({
  first_name: "",
  last_name: "",
  phone: "",
  address_line_one: "",
  address_line_two: "",
  address_line_three: "",
  city: "",
  pincode: "",
  state: "",
  country: "",
  email: "",
});

const snackbar = ref({
  show: false,
  timeout: 2000,
  message: "",
  type: true,
});

const userProfile = ref(null);
const errors = ref({});

function toggleAddingAddress() {
  addingAddress.value = !addingAddress.value;
  if (addingAddress.value) {
    resetForm();
  }
}

function resetForm() {
  address.value = {
    first_name: "",
    last_name: "",
    phone: "",
    address_line_one: "",
    address_line_two: "",
    address_line_three: "",
    city: "",
    pincode: "",
    state: "",
    country: "",
    email: "",
  };
  errors.value = {};
}

const selectedCurrency = appStore.currencyCode;

async function saveAddresses(type, address) {
  if (type === "new") {
    const resp = await addAddress({
      first_name: address.first_name,
      last_name: address.last_name,
      phone: address.phone,
      address_line_one: address.address_line_one,
      address_line_two: address.address_line_two,
      address_line_three: address.address_line_three,
      city: address.city,
      pincode: address.pincode,
      state: address.state,
      country: address.country,
      email: address.email,
    });
    if (resp.status) {
      loadAddresses();
    } else {
      snackbar.value.show = true;
      snackbar.value.type = false;
      snackbar.value.message = "Failed to add address";
    }
  } else {
    const resp = await updateAddress({
      id: address.address_id,
      first_name: address.first_name,
      last_name: address.last_name,
      phone: address.phone,
      address_line_one: address.address_line_one,
      address_line_two: address.address_line_two,
      address_line_three: address.address_line_three,
      city: address.city,
      pincode: address.pincode,
      state: address.state,
      country: address.country,
      email: address.email,
    });

    if (resp.status) {
      loadAddresses();
    } else {
      snackbar.value.show = true;
      snackbar.value.type = false;
      snackbar.value.message = "Failed to Update address";
    }
  }
}

async function updateSelected(index) {
  selectedAddressId.value = index;
  if (userProfile.value) {
    const resp = await updateUserProfile({
      default_address: index,
      id: userProfile.value.id,
      image: userProfile.value.image,
      name: userProfile.value.name,
      email: userProfile.value.email,
      phone: userProfile.value.phone,
    });

    if (resp.status) {
      snackbar.value.show = true;
      snackbar.value.type = true;
      snackbar.value.message = resp.message;
    } else {
      snackbar.value.show = true;
      snackbar.value.type = false;
      snackbar.value.message = "failed to update profile";
    }
  } else {
    const resp = await addUserProfile({
      default_address: index,
    });
    if (resp.status) {
      snackbar.value.show = true;
      snackbar.value.message = resp.message;
      snackbar.value.type = true;
    } else {
      snackbar.value.type = false;
      snackbar.value.show = true;
      snackbar.value.message = "failed to add profile";
    }
  }
}

async function loadAddresses() {
  isLoading.value = true;

  try {
    const resp = await getUserAllAddress();
    const profile = await getUserProfile();

    if (profile?.user[0]) {
      selectedAddressId.value = profile.user[0].default_address;
      userProfile.value = profile.user[0];

    } else {
      userProfile.value = null;
    }
    addresses.value = resp.address;
    isLoading.value = false;
  } catch (error) {
    console.error("Error in product detail API", error);
    isLoading.value = false;
  }
  console.log(selectedAddressId.value, userProfile.value, addresses.value, "selectedAddressId.value");
}



function validateForm(index) {
  errors.value = {};
  let isValid = true;

  const requiredFields = [
    "first_name",
    "phone",
    "address_line_one",
    "city",
    "pincode",
    "state",
    "country",
    "email",
  ];

  let data = null;
  if (typeof index === "number") {
    data = addresses.value[index];
  } else {
    data = index;
  }

  for (const field of requiredFields) {
    if (!data[field]) {
      errors.value[field] = `${field.replace("_", " ")} is required`;
      isValid = false;
    }
  }

  // Additional validation for email
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (data.email && !emailRegex.test(data.email)) {
    errors.value.email = "Invalid email format";
    isValid = false;
  }

  const phoneRegex = /^(\+91[\-\s]?)?[0]?(91)?[789]\d{9}$/;

  if (
    selectedCurrency === "INR"
      ? data.phone && !phoneRegex.test(data.phone)
      : false
  ) {
    errors.value.phone = "Invalid phone number";
    isValid = false;
  }

  const pincodeRegex = /^\d{6}$/;
  if (
    selectedCurrency === "INR"
      ? data.pincode && !pincodeRegex.test(data.pincode)
      : false
  ) {
    errors.value.pincode = "Invalid pincode (should be 6 digits)";
    isValid = false;
  }

  return isValid;
}

function editAddress(index) {
  addresses.value[index].isEditing = true;
}

function saveEditedAddress(index) {
  if (validateForm(index)) {
    addresses.value[index].isEditing = false;
    saveAddresses("edit", addresses.value[index]);
  }
}

const cancelEditAddress = (index) => {
  addresses.value[index].isEditing = false;
};

const cancelAddress = () => {
  addingAddress.value = false;
  resetForm();
};

const saveNewAddress = () => {
  if (validateForm(address.value)) {
    addresses.value.push({
      ...address.value,
      isEditing: false,
      selected: false,
    });
    saveAddresses("new", address.value);
    addingAddress.value = false;
    resetForm();
  }
};

onMounted(() => {
  loadAddresses();
});
</script>

<style scoped>
.address-card {
  border-radius: 8px;
  padding: 16px;
  transition: background-color 0.2s ease, color 0.2s ease;
  cursor: pointer;
  border: 1px solid #ddd;
}
.address-card.hovered {
  border: 1px solid #1e88e5;
}
.address-card.selected {
  color: white;
  border: 1px solid #1565c0;
}
.address-container {
  padding: 16px;
  border-radius: 8px;
  height: 100%;
}
.address-item {
  padding: 16px;
  background: #000;
  border-radius: 8px !important;
  box-sizing: border-box;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}
.address-item-content {
  width: 100%;
}
.edit-form {
  margin-top: 16px;
}
.input-group {
  display: flex;
  gap: 16px;
  margin-bottom: 16px; /* Add consistent spacing */
}
.button-group {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
}
.text-center {
  text-align: center;
}
.custom-button {
  width: 8rem !important;
  height: 35px !important;
  text-transform: none !important;
  font-weight: 500 !important;
  font-size: 16px !important;
  letter-spacing: normal !important;
}
.add-address-btn {
  width: auto !important;
  min-width: 10rem !important;
  background-color: transparent !important;
  color: #ccc !important;
}
.custom_upload {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 16px;
}
</style>
