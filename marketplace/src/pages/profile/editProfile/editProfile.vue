<template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular
          :size="70"
          :width="7"
          color="white"
          indeterminate
        ></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <div
    v-if="!isLoading"
    :class="isMobileOrTablet ? 'editProfileForMobandTab' : 'editProfile'"
  >
    <v-row justify="center">
      <v-col cols="12" md="6" class="">
        <div class="custom_upload">
          <v-avatar
            size="120"
            class="mb-4"
            :style="{
              border: user.image ? '2px solid black' : '10px solid black',
            }"
          >
            <v-img v-if="user.image" :src="user.image" />
            <svg
              v-else
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="#000"
            >
              <title>account</title>
              <path
                d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"
              />
            </svg>
          </v-avatar>
          <div style="cursor: pointer" @click="triggerFileInput">
            + Add Image
          </div>
          <v-file-input
            ref="fileInput"
            accept="image/*"
            show-size
            class="d-none"
            @change="uploadImage"
          ></v-file-input>
        </div>
        <div class="d-flex align-center" style="margin-bottom: 1rem; gap: 10px">
          <h4>Name</h4>
          <CustomInput
            v-model="user.name"
            placeholder="Name"
            name="name"
            prepend-inner-icon="mdi-lock"
            type="text"
            custom-class="mb-6"
            specific
          />
        </div>
        <div class="d-flex align-center" style="margin-bottom: 1rem; gap: 10px">
          <h4>Phone</h4>
          <CustomInput
            v-model="user.phone"
            placeholder="Phone"
            name="phone"
            prepend-inner-icon="mdi-lock"
            type="text"
            custom-class="mb-6"
            specific
          />
        </div>
        <div class="d-flex align-center" style="margin-bottom: 1rem; gap: 10px">
          <h4>Email</h4>
          <CustomInput
            v-model="user.email"
            placeholder="Email"
            name="email"
            prepend-inner-icon="mdi-lock"
            type="text"
            custom-class="mb-6"
            specific
          />
        </div>
        <div
          style="
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
          "
        >
          <v-btn
            @click="saveProfile"
            color="black"
            rounded
            outlined
            class="black--text custom-button custom-outlined"
          >
            Save
          </v-btn>
        </div>
      </v-col>
    </v-row>
    <v-snackbar
      :color="snackbar.type ? 'success' : 'red'"
      rounded="pill"
      v-model="snackbar.show"
      :timeout="snackbar.timeout"
    >
      {{ snackbar.message }}
    </v-snackbar>
  </div>
</template>

<script setup>
import CustomInput from "@/components/CustomInput.vue";
import { onBeforeMount, ref } from "vue";
import {
  getUserProfile,
  addUserProfile,
  updateUserProfile,
  getUserToken,
} from "../../../services/userService";
import axios from "axios";

const isMobileOrTablet = ref(window.innerWidth <= 1024);

const user = ref({
  image: "",
  name: "",
  phone: "",
  email: "",
  defaultAddress: "",
});

const snackbar = ref({
  show: false,
  timeout: 2000,
  message: "",
  type: false,
});
const isLoading = ref(true);
const dbdata = ref(null);

const fileInput = ref(null);

function triggerFileInput() {
  fileInput.value?.click();
}

function uploadImage(event) {
  const file = event.target.files[0];
  if (file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      user.value.image = e.target.result;
      uploadingToImgApi(file);
    };
    reader.readAsDataURL(file);
  }
}
const uploadingToImgApi = async (file) => {
  isLoading.value = true;

  const formData = new FormData();
  formData.append("file", file);

  try {
    const token = getUserToken();
    const res = await axios({
      method: "POST",
      url: import.meta.env.VITE_BASE_URL + `/api/updateUserProfilePic`,
      data: formData,
      headers: {
        authorization: token,
      },
    });

    if (res.data.status) {
      isLoading.value = false;
      snackbar.value.show = true;
      snackbar.value.type = true;
      snackbar.value.message = res.data.message;
    } else {
      isLoading.value = false;
      snackbar.value.show = true;
      snackbar.value.type = false;
      snackbar.value.message = "Failed to update profile";
    }
  } catch (error) {
    console.error("Error in sending file", error);
  }
};

async function saveProfile() {
  isLoading.value = true;

  if (dbdata.value) {
    // update profile
    const resp = await updateUserProfile({
      name: user.value.name,
      phone: user.value.phone,
      email: user.value.email,
      default_address: user.value.defaultAddress,
    });

    if (resp.status) {
      isLoading.value = false;
      snackbar.value.show = true;
      snackbar.value.type = true;
      snackbar.value.message = resp.message;
    } else {
      isLoading.value = false;
      snackbar.value.show = true;
      snackbar.value.type = false;
      snackbar.value.message = "failed to update profile";
    }
  } else {
    // insert profile
    const resp = await addUserProfile({
      name: user.value.name,
      phone: user.value.phone,
      email: user.value.email,
      default_address: user.value.defaultAddress,
    });

    if (resp.status) {
      isLoading.value = false;
      snackbar.value.show = true;
      snackbar.value.type = true;
      snackbar.value.message = resp.message;
    } else {
      isLoading.value = false;
      snackbar.value.show = true;
      snackbar.value.type = false;
      snackbar.value.message = "failed to add profile";
    }
  }
}

onBeforeMount(async () => {
  isLoading.value = true;
  try {
    const userProfile = await getUserProfile();

    if (userProfile?.user?.[0]) {
      isLoading.value = false;
      dbdata.value = true;
      user.value.image = userProfile.user[0].image || null;
      user.value.name = userProfile.user[0].name || "";
      user.value.phone = userProfile.user[0].phone || "";
      user.value.email = userProfile.user[0].email || "";
      user.value.defaultAddress = userProfile.user[0].defaultAddress || null;
    } else {
      isLoading.value = false;
      dbdata.value = false;
    }
  } catch (error) {
    isLoading.value = false;
    dbdata.value = false;
  }
});
</script>

<style scoped>
.editProfile {
  background-color: white;
  color: black;
  max-width: 50vw;
  padding: 35px;
}

.editProfileForMobandTab {
  background-color: white;
  color: black;
  padding: 35px;
}

.custom-outlined {
  width: 50px;
  border: 1px solid black !important;
}

.custom-button {
  width: 8rem !important;
  height: 35px !important;
  text-transform: none !important;
  font-weight: 500 !important;
  font-size: 16px !important;
  letter-spacing: normal !important;
}

.custom_upload {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 16px;
}
</style>
