<template>
  <template v-if="isLoading">
    <v-row class="main-container ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular
          :size="70"
          :width="7"
          color="white"
          indeterminate
        ></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <v-container v-else>
    <p class="text-left text-h4 ml-5 mt-6">{{ title }}</p>
    <v-row>
      <v-col cols="12" sm="8" md="8" lg="8">
        <div class="d-flex ma-2">
          <div class="player">
            <div class="player__sizer">
              <video ref="videoPlayer" controls></video>
            </div>
          </div>
        </div>
      </v-col>
      <v-col cols="12" sm="4" md="4" lg="4">
        <h4 class="mb-4 text-white">Chapters</h4>
        <v-text-field
          v-model="search"
          prepend-inner-icon="mdi-magnify"
          label="Search"
          single-line
          hide-details
          filled
          dense
          dark
          class="mb-4"
        ></v-text-field>
        <v-expansion-panels flat>
          <v-expansion-panel
            v-for="(chapter, index) in filteredChapters"
            :key="chapter.id"
            :class="[
              'bg-transparent',
              isCurrentChapter(index) ? 'selected-chapter' : ''
            ]"
          >
            <v-expansion-panel-title 
              :class="[
                'text-white',
                isCurrentChapter(index) ? 'selected-chapter-title' : ''
              ]"
            >
              <div class="d-flex align-center">
                <v-icon 
                  v-if="isCurrentChapter(index)"
                  icon="mdi-play-circle"
                  color="primary"
                  class="mr-3"
                  size="small"
                ></v-icon>
                <span>{{ index + 1 }}. {{ chapter.name }}</span>
              </div>
            </v-expansion-panel-title>
            <v-expansion-panel-text>
              <template
                v-for="(play, index1) in chapter.playList"
                :key="play.id"
              >
                <v-row
                  @click="
                    playVideo(
                      play.streamingUrl,
                      play.name,
                      index,
                      index1
                    )
                  "
                  class="flex-nowrap video-item"
                  :class="[
                    play.access == 1 || data.purchased
                      ? 'black-text'
                      : 'grey-text',
                    isCurrentVideo(index, index1) ? 'selected-video' : '',
                    (play.access == 1 || data.purchased) ? 'clickable' : 'locked'
                  ]"
                  no-gutters
                >
                  <v-col cols="1" class="flex-grow-0 flex-shrink-0">
                    <v-icon
                      :icon="
                        isCurrentVideo(index, index1) ? 'mdi-pause' :
                        play.access == 1 || data.purchased
                          ? 'mdi-play'
                          : 'mdi-lock'
                      "
                      :color="isCurrentVideo(index, index1) ? 'primary' : ''"
                    ></v-icon>
                  </v-col>
                  <v-col
                    cols="8"
                    class="text-left flex-grow-1 flex-shrink-0"
                    style="min-width: 120px; max-width: 100%"
                  >
                    <div class="text-subtitle-1" :class="isCurrentVideo(index, index1) ? 'current-title' : ''">
                      {{ index + 1 + "." + (index1 + 1) + " " + play.name }}
                    </div>
                  </v-col>
                  <v-col
                    cols="2"
                    class="text-no-wrap text-right flex-grow-0 flex-shrink-1"
                  >
                    <div class="text-subtitle-1">
                      {{ getDurationString(play.duration) }}
                    </div>
                  </v-col>
                </v-row>
              </template>
            </v-expansion-panel-text>
          </v-expansion-panel>
        </v-expansion-panels>
        <v-btn
          v-if="data.chapters.length > displayedChapters"
          @click="loadMore"
          block
          text
          class="mt-4 text-white"
        >
          More
          <v-icon right>mdi-chevron-down</v-icon>
        </v-btn>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import {
  getDurationString,
  sendDataToParent,
} from "@/helper/common";
import { getCourseContent } from "@/services/courseService";
import { getUserToken, setUserToken } from "@/services/userService";
import { onBeforeMount, ref, computed, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import Hls from "hls.js";

const router = useRouter();

const data = ref(null);
let courseId;
const chapterIndex = ref(0);
const playListIndex = ref(0);
const currentChapterIndex = ref(0);
const currentPlayListIndex = ref(0);
const videoUrl = ref(null);
const isLoading = ref(true);
const videoPlayer = ref(null);
const title = ref();
const search = ref("");
const displayedChapters = ref(10);
const error = ref(null);
const hls = ref(null);

const initializePlayer = (url) => {
  if (!videoPlayer.value || !url) {
    console.error("Video player element or URL not available");
    return;
  }

  if (Hls.isSupported()) {
    if (hls.value) {
      hls.value.destroy();
    }

    hls.value = new Hls({
      xhrSetup: (xhr) => {
        xhr.withCredentials = true;
      },
    });

    hls.value.loadSource(url);
    hls.value.attachMedia(videoPlayer.value);

    hls.value.on(Hls.Events.MANIFEST_PARSED, () => {
      console.log("HLS manifest parsed successfully");
    });

    hls.value.on(Hls.Events.ERROR, (event, data) => {
      console.error("HLS error:", data);
      error.value = `Video playback error: ${data.details}`;
    });
  } else if (videoPlayer.value.canPlayType("application/vnd.apple.mpegurl")) {
    videoPlayer.value.src = url;
    videoPlayer.value.addEventListener("error", (e) => {
      error.value = `Video playback error: ${e.message || "Unknown error"}`;
    });
  } else {
    error.value = "HLS is not supported in this browser.";
  }
};

const playVideo = (url, name, chapterIdx, playListIdx) => {
  title.value = name;
  currentChapterIndex.value = chapterIdx;
  currentPlayListIndex.value = playListIdx;
  initializePlayer(url);
};

const isCurrentVideo = (chapterIdx, playListIdx) => {
  return currentChapterIndex.value === chapterIdx && currentPlayListIndex.value === playListIdx;
};

const isCurrentChapter = (chapterIdx) => {
  return currentChapterIndex.value === chapterIdx;
};

const setInitialVideo = () => {
  if (data.value?.chapters?.length) {
    let chapter = data.value.chapters[chapterIndex.value];
    if (chapter?.playList?.length) {
      let play = chapter.playList[playListIndex.value];
      if (play) {
        currentChapterIndex.value = chapterIndex.value;
        currentPlayListIndex.value = playListIndex.value;
        playVideo(play.streamingUrl, play.name, chapterIndex.value, playListIndex.value);
      }
    }
  }
};

const getCourseData = async () => {
  try {
    data.value = await getCourseContent(courseId);
    isLoading.value = false;
    await nextTick();
    setInitialVideo();
  } catch (error) {
    isLoading.value = false;
    console.error(error);
  }
};

const filteredChapters = computed(() => {
  if (!data.value?.chapters) return [];
  return data.value.chapters
    .filter((chapter) =>
      chapter.name.toLowerCase().includes(search.value.toLowerCase())
    )
    .slice(0, displayedChapters.value);
});

const loadMore = () => {
  displayedChapters.value += 10;
};

onBeforeMount(() => {
  const route = useRoute();
  courseId = route.params.id ? route.params.id : 1;
  playListIndex.value = route.query.playListIndex
    ? parseInt(route.query.playListIndex)
    : 0;
  chapterIndex.value = route.query.chapterIndex 
    ? parseInt(route.query.chapterIndex) 
    : 0;

  if (route.query.token) {
    setUserToken(route.query.token);
    getCourseData();
  } else if (getUserToken()) {
    getCourseData();
  } else if (!getUserToken()) {
    sendDataToParent({ message: "Login Needed" });
    router.push({
      path: "/login",
      query: {
        toSend: "/course",
        id: courseId,
      },
    });
  }
});
</script>

<style scoped>

.main-container{
  min-height: calc(100vh - 100px);
}

.player {
  width: 100%;
}

.player video {
  position: absolute;
  width: 100%;
  height: auto;
  left: 0;
  top: 0;
}

.player__sizer {
  position: relative;
  width: 100%;
  padding-top: 56.25%;
}

/* Selected chapter styling */
.selected-chapter {
  background: rgba(33, 150, 243, 0.05) !important;
  border-radius: 8px !important;
  margin-bottom: 8px !important;
}

.selected-chapter-title {
  color: #2196F3 !important;
  font-weight: 500 !important;
}

.selected-chapter::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #2196F3;
  border-radius: 2px 0 0 2px;
}

/* Remove the pulse animation */
@keyframes chapterPulse {
  0%, 100% {
    opacity: 1;
  }
}

/* Ensure selected chapter stays expanded and visible */
.selected-chapter .v-expansion-panel-title {
  background: transparent !important;
  border-radius: 8px !important;
}

.selected-chapter .v-expansion-panel-text {
  background: transparent !important;
  border-radius: 0 0 8px 8px !important;
}

/* Video item styling */
.video-item {
  padding: 8px 12px;
  margin: 4px 0;
  border-radius: 4px;
  transition: all 0.3s ease;
  cursor: pointer;
  border-left: 3px solid transparent;
}

.video-item.clickable:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.video-item.locked {
  cursor: not-allowed;
  opacity: 0.6;
}

.video-item.locked:hover {
  transform: none;
  background-color: transparent;
}

/* Selected video styling */
.selected-video {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.2), rgba(33, 150, 243, 0.1));
  border-left: 3px solid #2196F3 !important;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.selected-video .current-title {
  color: #2196F3 !important;
  font-weight: 600;
}

/* Playing indicator animation */
.selected-video::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(45deg, #2196F3, #1976D2);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* Dark theme adjustments */
.black-text {
  color: rgba(255, 255, 255, 0.87) !important;
}

.grey-text {
  color: rgba(255, 255, 255, 0.4) !important;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .video-item {
    padding: 6px 8px;
  }
}
</style>