<template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular :size="70" :width="7" color="white" indeterminate></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <div v-show="!isLoading" class="video-wrapper">
    <div class="video-container">
      <video ref="videoPlayer" class="video-player" controls controlsList="nodownload" @play="isPlaying = true"
        @pause="isPlaying = false" @ended="isPlaying = false"></video>

      <div v-if="!isPlaying" class="play-button" @click="playVideo">
        <v-icon x-large color="white" style="font-size: 50px">mdi-play</v-icon>
      </div>

      <div v-if="error" class="error-message">{{ error }}</div>
    </div>
  </div>
</template>

<script setup>
import { getVideoContent } from "@/services/artistService";
import { onBeforeMount, ref, onBeforeUnmount } from "vue";
import { useRoute } from "vue-router";
import Hls from "hls.js";

const isLoading = ref(true);
const route = useRoute();
const videoId = route.params.id;
const data = ref(null);
const error = ref(null);

const videoPlayer = ref(null);
const isPlaying = ref(false);
const hls = ref(null);

const playVideo = () => {
  if (videoPlayer.value) {
    videoPlayer.value.play();
  }
};


const initializePlayer = (url) => {
  if (Hls.isSupported()) {
    if (hls.value) {
      hls.value.destroy();
    }

    hls.value = new Hls({
      xhrSetup: (xhr) => {
        xhr.withCredentials = true;
      },
    });

    hls.value.loadSource(url);
    hls.value.attachMedia(videoPlayer.value);

    hls.value.on(Hls.Events.MANIFEST_PARSED, () => {
      isLoading.value = false;
    });

    hls.value.on(Hls.Events.ERROR, (event, data) => {
      console.error("HLS error:", data);
      error.value = `Video playback error: ${data.details}`;
      isLoading.value = false;
    });
  } else if (videoPlayer.value.canPlayType("application/vnd.apple.mpegurl")) {
    videoPlayer.value.src = url;

    videoPlayer.value.addEventListener("loadedmetadata", () => {
      isLoading.value = false;
    });

    videoPlayer.value.addEventListener("error", (e) => {
      error.value = `Video playback error: ${e.message || "Unknown error"}`;
      isLoading.value = false;
    });
  } else {
    error.value = "HLS is not supported in this browser.";
    isLoading.value = false;
  }
};

onBeforeMount(async () => {
  if (!videoId) {
    error.value = "Video ID is missing";
    isLoading.value = false;
    return;
  }

  try {
    const resp = await getVideoContent(videoId);
    data.value = resp;

    const videoUrl = resp.hls_url || resp.video_stream_url;

    if (!videoUrl) {
      error.value = "Video URL not found";
      isLoading.value = false;
      return;
    }

    initializePlayer(`${videoUrl}`);
  } catch (error) {
    console.error("Error in getting video", error);
    error.value = `Error loading video: ${error.message || "Unknown error"}`;
    isLoading.value = false;
  }
});

onBeforeUnmount(() => {
  if (hls.value) {
    hls.value.destroy();
  }
});
</script>

<style scoped>
.video-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.video-container {
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 1200px;
  max-height: 100vh;
  padding: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.video-player {
  width: 100%;
  height: 100%;
  max-height: calc(100vh - 20px);
  border-radius: 8px;
  object-fit: contain;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.play-button:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.error-message {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(220, 53, 69, 0.8);
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  max-width: 80%;
  text-align: center;
}
</style>