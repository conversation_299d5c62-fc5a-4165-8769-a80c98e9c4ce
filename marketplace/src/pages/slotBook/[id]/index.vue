<template>
  <v-container fluid class="pa-0" style="min-height: calc(100vh - 80px);">
    <template v-if="store.isLoading">
      <v-row class="progress-loader ma-0 align-center justify-center">
        <v-col cols="12" class="text-center">
          <v-progress-circular :size="70" :width="7" color="white" indeterminate>
          </v-progress-circular>
          <v-card-text class="white--text text-center">
            Loading, please wait...
          </v-card-text>
        </v-col>
      </v-row>
    </template>
    <template v-else>
      <v-row class="fill-height ma-0 align-center justify-center">
        <v-col cols="12" class="text-center">
          <p class="text-center text-h5" v-if="store.artistDetail.request_status == 'REQUESTED'">
            We have received your request and we will get back to you shortly
          </p>
          <p class="text-center text-h5" v-if="store.artistDetail.request_status == 'REJECTED'">
            We regret to inform you, your request is declined
          </p>
        </v-col>
      </v-row>
      <v-row class="ma-0" v-if="
        !store.artistDetail.request_status ||
        store.artistDetail.request_status == 'ACCEPTED'
      ">
        <v-col cols="12" md="6" :order="store.artistDetail.metadata ? '1' : '2'" order-md="1">
          <v-card color="transparent" flat class="d-flex flex-column align-center">
            <div class="video-container" v-if="
              store.artistDetail.metadata && store.artistDetail.metadata.video
            ">
              <video ref="videoPlayer" class="video-player" controls controlsList="nodownload" @play="isPlaying = true"
                @pause="isPlaying = false" @ended="isPlaying = false"></video>
              <div v-if="!isPlaying" class="play-button" @click="playVideo">
                <v-icon x-large color="white" style="font-size: 50px">mdi-play</v-icon>
              </div>
            </div>
            <div v-if="!(store.artistDetail.metadata && store.artistDetail.metadata.video) && store.artistDetail.image">
              <img :src="store.artistDetail.image" style="width: 100%; height: 65vh" />
            </div>
            <template v-if="!isLoggedIn">
              <v-btn color="white" dark rounded class="black--text mt-6" @click="login">
                Login
              </v-btn>
            </template>
            <template v-else>
              <template v-if="store.artistDetail.metadata.type == 1">
                <v-btn v-if="!store.artistDetail.eligibile" color="white" dark rounded class="black--text mt-6"
                  @click="bookNow()">
                  Book Now
                </v-btn>
                <template v-if="
                  store.artistDetail.request_status == 'ACCEPTED' ||
                  store.artistDetail.eligibile
                ">
                  <v-btn color="white" dark rounded class="black--text mt-6" @click="submitForm">
                    Select Slot
                    <v-icon right>mdi-chevron-right</v-icon>
                  </v-btn>
                </template>
              </template>

              <template v-if="store.artistDetail.metadata.type == 2">
                <v-btn v-if="store.artistDetail.eligibile == false" color="white" dark rounded class="black--text mt-6"
                  @click="bookNow(9)">
                  Individual Collaboration
                </v-btn>
                <template v-if="
                  (store.artistDetail.request_status == 'ACCEPTED' ||
                    store.artistDetail.eligibile) && (store.artistDetail.bookId == 9)
                ">
                  <v-btn color="white" dark rounded class="black--text mt-6" @click="submitForm(9)">
                    Individual Collaboration Select Slot
                    <v-icon right>mdi-chevron-right</v-icon>
                  </v-btn>
                </template>
                <v-btn color="white" dark rounded class="black--text mt-6" v-if="
                  (store.artistDetail.eligibile && store.artistDetail.bookId == 9)
                  || (!store.artistDetail.eligibile && store.artistDetail.bookId == 9)
                  || (!store.artistDetail.eligibile && store.artistDetail.bookId == 10)
                " @click="bookNow(10)">
                  Group Collaboration
                </v-btn>
                <v-btn color="white" dark rounded class="black--text mt-6" v-if="
                  (store.artistDetail.request_status == 'ACCEPTED' ||
                    store.artistDetail.eligibile) && (artistId == 10)
                " @click="checkout(10)">
                  Group Collaboration Payment
                </v-btn>
              </template>

              <template v-if="store.artistDetail.metadata.type == 3">
                <v-btn color="white" dark rounded class="black--text mt-6" @click="handleArtistAction()">
                  Apply
                </v-btn>
              </template>
            </template>
          </v-card>
        </v-col>
        <v-col cols="12" md="6" :order="store.artistDetail.metadata && store.artistDetail.metadata.video
          ? '2'
          : '1'
          " order-md="2">
          <v-card color="transparent" flat>
            <v-card-title class="white--text text-h5 mb-2 text-wrap">
              {{ store.artistDetail.name }}
            </v-card-title>
            <v-divider class="mb-4" light></v-divider>
            <v-card-text class="white--text"
              v-html="artistId === '11' && CurrencyCode === 'USD' ? updatedHtml : store.artistDetail.description">
            </v-card-text>

          </v-card>
        </v-col>
      </v-row>
    </template>

    <Snackbar ref="snackbarRef" />
  </v-container>
</template>


<script setup>
import { ref, onBeforeMount, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useSlotStore } from "@/stores/slot";
import { getUserToken, setUserToken } from "@/services/userService";
import { sendDataToParent } from "@/helper/common";
import Snackbar from "@/components/Snackbar.vue";
import Hls from "hls.js";
import { useAppStore } from "@/stores/app";

const appStore = useAppStore();
const router = useRouter();
const route = useRoute();
const artistId = route.params.id ? route.params.id : 1;
const store = useSlotStore();
const videoPlayer = ref(null);
const isPlaying = ref(false);
const hls = ref(null);
const error = ref(null);
const snackbarRef = ref(null);
const isLoading = ref(true);
const CurrencyCode = appStore.currencyCode;
const isLoggedIn = ref(!!getUserToken()); // Check if user is logged in based on token

const updatedHtml = computed(() => {
  let description = store.artistDetail.description;

  description = description.replace(
    /Indian Students : Tuesdays and Thursdays : 6:30 - 8:00 am IST/g,
    'Foreign Students : Wednesday and Fridays : 1:00 - 2:30 am GMT'
  );

  description = description.replace(
    /For Indian Students : INR 36,000\/- plus 18% GST/g,
    'Foreign Students : USD 700'
  );

  return description;
})

const initializePlayer = (url) => {
  if (Hls.isSupported()) {
    if (hls.value) {
      hls.value.destroy();
    }

    hls.value = new Hls({
      xhrSetup: (xhr) => {
        xhr.withCredentials = true;
      },
    });

    hls.value.loadSource(url);
    hls.value.attachMedia(videoPlayer.value);

    hls.value.on(Hls.Events.MANIFEST_PARSED, () => {
      isLoading.value = false;
    });

    hls.value.on(Hls.Events.ERROR, (event, data) => {
      console.error("HLS error:", data);
      error.value = `Video playback error: ${data.details}`;
      isLoading.value = false;
    });
  } else if (videoPlayer.value.canPlayType("application/vnd.apple.mpegurl")) {
    videoPlayer.value.src = url;
    videoPlayer.value.addEventListener("loadedmetadata", () => {
      isLoading.value = false;
    });
    videoPlayer.value.addEventListener("error", (e) => {
      error.value = `Video playback error: ${e.message || "Unknown error"}`;
      isLoading.value = false;
    });
  } else {
    error.value = "HLS is not supported in this browser.";
    isLoading.value = false;
  }
};

const login = () => {
  sendDataToParent({ message: "Login Needed" });
  router.push({
    path: "/login",
    query: {
      toSend: `/slotBook/${artistId}`,
    },
  });
};

const bookNow = (id) => {
  let toPath = `/slot-form/` + (id ? id : artistId);
  router.push({
    path: toPath,
  });
};

// New function for handling Type 3 "Apply" button
const handleArtistAction = () => {
  if (artistId == 11 && store.artistDetail.eligibile === true) {
    checkoutType3(artistId);
  } else {
    applyNow();
  }
};

const applyNow = () => {
  router.push({
    path: `/slot-form-2/${artistId}`
  });
};

const submitForm = async () => {
  registerUser();
};

const registerUser = async () => {
  try {
    router.push({
      path: `/slot-selection/${artistId}`
    });
  } catch (error) {
    console.error(error);
     setTimeout(() => {
    snackbarRef?.value?.showSnackbar(
      `error in getting available slots "${error?.message}"`,
      "red"
    );}, 0);
  }
};

const checkoutType3 = async (id) => {
  let productId = id ? id : artistId;
  let toPath = `/singleCheckout/${productId}`;
  // let toPath = `/meetCheckout/${productId}`;
  router.push({
    path: toPath,
    query: {
      productId: productId,
      image: store.artistDetail.metadata.image,
      name: store.artistDetail.name,
      artist_name: store.artistDetail.artist_name,
      price: store.artistDetail.metadata.product[CurrencyCode === "INR" ? 0 : 1].price,
      tax: store.artistDetail.metadata.product[CurrencyCode === "INR" ? 0 : 1].tax,
      currency: CurrencyCode === "INR" ? "₹" : "$",
      image: store.artistDetail.image,
      quantity: 1,
      type: 2,
      slotTime: null,
      slotDate: null
    },
  });
};

const checkout = async (id) => {
  let productId = id ? id : artistId;
  let toPath = `/singleCheckout/${productId}`;
  // let toPath = `/meetCheckout/${productId}`;

  router.push({
    path: toPath,
    query: {
      productId: productId,
      image: store.artistDetail.metadata.image,
      name: store.artistDetail.name,
      price: store.artistDetail.metadata.product[1].price,
      tax: store.artistDetail.metadata.product[1].tax,
      currency: CurrencyCode === "INR" ? "₹" : "$",
      quantity: 1,
      type: 2,
      slotTime: null,
      slotDate: null
    },
  });
};

const playVideo = () => {
  if (videoPlayer.value) {
    videoPlayer.value.play();
  }
};

onBeforeMount(async () => {
  if (route.query.token) {
    setUserToken(route.query.token);
    isLoggedIn.value = true;
    await store.getPopupArtist(artistId);
  } else if (getUserToken()) {
    isLoggedIn.value = true;
    await store.getPopupArtist(artistId);
  } else {
    isLoggedIn.value = false;
    await store.getPopupArtistOutLogin(artistId);
  }
  if (
    store.artistDetail.metadata &&
    store.artistDetail.metadata.video_stream_url
  ) {
    await nextTick();
    initializePlayer(store.artistDetail.metadata.video_stream_url);
  }
});
</script>

<style scoped>
.progress-loader {
  min-height: calc(100vh - 80px);
}

.dark-input {
  background-color: transparent;
  border: 0.2px solid white;
  border-radius: 25px;
  color: rgba(255, 255, 255, 0.9);
  padding: 12px 20px;
  font-size: 16px;
  width: 100%;
  max-width: 500px;
  outline: none;
}

.dark-input::placeholder {
  color: white;
}

.dark-input:focus {
  border-color: white;
  box-shadow: 0 0 0 1px white;
}

.video-container {
  position: relative;
  width: 100%;
}

.video-player {
  width: 100%;
  border-radius: 8px;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.play-button:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.error-text {
  color: red;
  font-size: 14px;
}
</style>