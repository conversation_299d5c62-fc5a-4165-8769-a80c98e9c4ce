<template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular :size="70" :width="7" color="white" indeterminate></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <v-container v-else>
    <ProductNFT v-if="!productDesc.is_internal && productDesc.is_external" :productImage="selectedImage"
      :htmlContent="productDesc.description" />
    <template v-if="productDesc.is_internal">
      <v-row>
        <v-col cols="12">
          <v-btn icon @click="goBack">
            <v-icon>mdi-arrow-left</v-icon>
          </v-btn>
          <span class="text-h6 ml-2">Products</span>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12" md="6">
          <div class="image-container">
            <v-card class="transparent-card">
              <v-img v-if="selectedImage.imageURL" :src="selectedImage.imageURL" width="400" cover
                class="centered-image">
                <template v-slot:placeholder>
                  <v-row class="fill-height ma-0" align="center" justify="center">
                    <v-progress-circular indeterminate color="grey lighten-5"></v-progress-circular>
                  </v-row>
                </template>
              </v-img>
              <video v-else-if="selectedImage.video" width="400" controls class="centered-image">
                <source :src="selectedImage.video" type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            </v-card>
          </div>
          <v-sheet class="mt-4 transparent-card" v-if="imageList.length > 1">
            <v-row class="overflow-x-auto flex-nowrap">
              <v-col v-for="image in imageList" :key="image.id" cols="auto" class="flex-grow-0 flex-shrink-0">
                <v-img :src="image.thumbnailURL" height="100" width="80" class="ma-2" cover
                  @click="selectImage(image.id)" :class="{ 'selected-image': image.isSelected }"></v-img>
              </v-col>
            </v-row>
          </v-sheet>
        </v-col>

        <v-col cols="12" md="6">
          <h2 class="text-h5 mb-2">{{ productDesc.brand_name }}</h2>
          <h3 class="text-h6 mb-2">{{ productDesc.title }}</h3>
          <v-divider :thickness="1" class="mb-4 border-opacity-100" dark></v-divider>

          <p class="text-h5 mt-4">
            {{
              CurrencyCode === productDesc.currency
                ? productDesc.currency_symbol
                : CurrencyCode === "INR"
                  ? `₹`
                  : isGlobalAvailable
                    ? `$`
                    : "-"
            }}
            {{ `${formatIndianCurrency(selectedCurrency.price)}` }}
          </p>

          <v-row align="center" class="mt-4" v-if="productDesc.varaiant_option.length">
            <v-col cols="6">
              <span class="text-body-1">Select Variation:</span>
            </v-col>
            <v-col cols="6" class="text-right" v-if="productDesc.size_chart">
              <v-btn variant="text" @click="openSizeChart" density="comfortable" class="pa-0">
                Size Chart
              </v-btn>
            </v-col>
          </v-row>

          <v-row class="mt-2 mb-2" v-for="attr in productDesc.varaiant_option" :key="attr">
            <v-col cols="12">
              <v-btn-toggle v-model="selectedValue[attr]" mandatory class="d-flex justify-start">
                <v-btn @click="attrSelected" v-for="attr in dropDownValues[attr]" :key="attr" :value="attr"
                  class="me-2 rounded-pill" variant="outlined" color="primary">
                  {{ attr }}
                </v-btn>
              </v-btn-toggle>
            </v-col>
          </v-row>
          <v-row v-if="isVariationError" class="mt-2">
            <v-col>
              <p class="red--text">
                Please select all product variations before proceeding.
              </p>
            </v-col>
          </v-row>
          <v-row v-if="!productDesc.is_out_of_stock || !isEnable" class="mt-2">
            <v-col>
              <v-btn block color="white" :disabled="CurrencyCode === 'INR'
                ? !isIndiaAvailable
                : !isGlobalAvailable
                " class="add-to-bag-btn mb-2" height="50" @click="updateCart">
                <span class="button-text">Add To Bag</span>
                <v-avatar color="black" size="36" class="shopping-icon">
                  <v-icon icon="mdi-shopping" color="white"></v-icon>
                </v-avatar>
              </v-btn>
            </v-col>
            <v-col>
              <v-btn block height="50" color="black" class="white--text rounded-xl" :disabled="CurrencyCode === 'INR'
                ? !isIndiaAvailable
                : !isGlobalAvailable
                " @click="buyNow">
                {{ "Buy Now" }}
              </v-btn>
            </v-col>
          </v-row>

          <div class="d-flex align-center mb-2">
            <div class="mr-2">Available:</div>
            <img :src="india_flag" alt="In Stock" class="availability-icon mr-1"
              :class="{ 'greyed-out': !isIndiaAvailable }" />
            <img :src="globe_flag" alt="Global Shipping" class="availability-icon"
              :class="{ 'greyed-out': !isGlobalAvailable }" />
          </div>
          <v-divider class="my-4"></v-divider>

          <h4 class="text-h6 mb-2">Product Description</h4>

          <div v-html="productDesc.description"></div>
          <v-divider class="my-4" v-if="productDesc.metadata"></v-divider>
        </v-col>
      </v-row>
      <div v-html="productDesc.metadata"></div>
    </template>
  </v-container>
  <v-dialog v-model="isSizeChart" max-width="100%">
    <v-card>
      <v-img class="ma-2 pa-2" :src="productDesc.size_chart" max-height="90vh" contain>
        <v-btn icon @click="closeSizeChart" class="close-button">
          <v-icon>mdi-close</v-icon>
        </v-btn></v-img>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { formatIndianCurrency, sendDataToParent } from "@/helper/common";
import { getProductById, updateCartAPI } from "@/services/productService";
import { ref, onBeforeMount, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import india_flag from "@/assets/india_flag.webp";
import globe_flag from "@/assets/global.webp";
import { getUserToken, setUserToken } from "@/services/userService";
import ProductNFT from "@/components/ProductNFT.vue";
import { useAppStore } from "@/stores/app";

const appStore = useAppStore();
const router = useRouter();
const route = useRoute();
const CurrencyCode = appStore.currencyCode;

let isLoading = ref(true);
const productId = route.params.id;
let imageList = ref([]);
let selectedImage = ref(null);
let productDesc = ref(null);
let dropDownValues = ref({});
let isSizeChart = ref(false);
let selectedValue = ref({});
let selectedVariation = {};
let selectedCurrency = ref(null);
const isEnable = ref(false);
let quantity = ref(1);
let isVariationError = ref(false);

const isIndiaAvailable = computed(() => {
  if (productDesc.value.countries) {
    return productDesc.value.countries.some(
      (country) => country.country_id === 1
    );
  } else {
    return false;
  }
});

const isGlobalAvailable = computed(() => {
  if (productDesc.value.countries) {
    return productDesc.value.countries.some(
      (country) => country.country_id === 2
    );
  } else {
    return false;
  }
});

const goBack = () => {
  router.back();
};

const openSizeChart = () => {
  isSizeChart.value = true;
};

const closeSizeChart = () => {
  isSizeChart.value = false;
};

const selectImage = (id) => {
  const selectedItem = (selectedImage.value = imageList.value.find(
    (img) => img.id === id
  ));
  selectedImage.value = selectedItem;
  imageList.value.forEach((el, index) => {
    if (el.id == id) {
      imageList.value[index].isSelected = true;
    } else {
      imageList.value[index].isSelected = false;
    }
  });
};

const updateCart = async () => {
  try {
    const variantLength = productDesc.value.varaiant_option.length;
    if (productDesc.value.varaiant_option.length) {
      const selectedVariantLength = Object.keys(selectedValue.value).length;
      if (variantLength != selectedVariantLength) {
        isVariationError.value = true;
        return null;
      }
    }
    let id = selectedVariation.id;
    if (!id) {
      id = productDesc.value.variants[0].id;
    }
    await updateCartAPI({
      productItemId: id,
      quantity: quantity.value,
      currency: CurrencyCode === "INR" ? 1 : 2,
    });
    router.push({
      path: "/cart",
    });
  } catch (error) {
    console.error("Error in Update Cart", error);
  }
};

const attrSelected = () => {
  let tempArray = [];
  selectedVariation = null;
  isVariationError.value = false;
  productDesc.value.varaiant_option.forEach((el) => {
    tempArray.push(selectedValue.value[el]);
  });

  if (tempArray.includes(null) || tempArray.includes(undefined)) {
    isEnable.value = false;
    return;
  }
  let filteredData = productDesc.value.variants;
  productDesc.value.varaiant_option.forEach((key) => {
    filteredData = filteredData.filter((el) => {
      if (el[key] == selectedValue.value[key]) {
        return el;
      }
    });
  });
  selectedImage.value = filteredData[0];
  isEnable.value = filteredData[0].isStock;
  selectedVariation = filteredData[0];
};

const selectVariant = () => {
  if (productDesc.value.variants.length == 1) {
    selectedVariation = productDesc.value.variants[0];
  }
};

const buyNow = () => {
  const variantLength = productDesc.value.varaiant_option.length;
  if (productDesc.value.varaiant_option.length) {
    const selectedVariantLength = Object.keys(selectedValue.value).length;
    if (variantLength != selectedVariantLength) {
      isVariationError.value = true;
      return null;
    }
  }

  router.push({
    path: `/singleCheckout/${selectedVariation.id}`,

    query: {
      productId: selectedVariation.id,
      brand_name: productDesc.value.brand_name,
      id: selectedVariation.product_id,
      image: selectedVariation.imageURL,
      name: productDesc.value.title,
      tax: selectedCurrency.value.gst,
      price: selectedCurrency.value.price,
      currency: CurrencyCode === "INR" ? "₹" : "$",
      quantity: quantity.value,
      type: 1,
    },
  });
};

onBeforeMount(async () => {
  if (!productId) {
    return;
  }
  if (route.query.token) {
    setUserToken(route.query.token);
  } else if (!getUserToken()) {
    sendDataToParent({ message: "Login Needed" });
    router.push({
      path: `/login`,
      query: {
        toSend: `/productDetails/${productId}`,
        query: {
          id: productId,
        },
      },
    });
    return;
  }
  try {
    const resp = await getProductById(productId);
    imageList.value = resp.imageList;
    productDesc.value = resp.productDetail;
    selectedCurrency.value = productDesc.value.currencies.find(
      (currency) => currency.currency_id === (CurrencyCode === "INR" ? 1 : 2)
    );
    selectedImage.value = resp.selectedImage;
    dropDownValues.value = resp.dropDownValues;
    if (Object.keys(resp.dropDownValues).length == 0) {
      isEnable.value = true;
    }
    selectVariant();
    isLoading.value = false;
  } catch (error) {
    console.error(error);
  }
});

</script>

<style scoped>
.add-to-bag-btn {
  border-radius: 30px !important;
  font-weight: bold;
  color: black !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 0 7px 0 20px !important;
  position: relative !important;
  overflow: hidden !important;
}

.button-text {
  position: absolute !important;
  left: 20px !important;
}

.shopping-icon {
  position: absolute !important;
  right: 7px !important;
}

.availability-icon {
  width: 30px;
  height: 30px;
  object-fit: contain;
}

.overflow-x-auto {
  overflow-x: auto;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.transparent-card {
  background-color: transparent !important;
  box-shadow: none !important;
}

.centered-image {
  margin: auto;
}

.selected-image {
  border: 2px solid #1976d2;
  /* You can change this color to match your theme */
  box-shadow: 0 0 5px rgba(25, 118, 210, 0.5);
}

.greyed-out {
  filter: grayscale(100%);
  opacity: 0.5;
}
</style>
