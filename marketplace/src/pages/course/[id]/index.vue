<template>
  <template v-if="isLoading">
    <v-row class="main-loader fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular :size="70" :width="7" color="white" indeterminate></v-progress-circular>
      </v-col>
    </v-row>
  </template>

  <v-container v-else-if="data && data.message !== 'Not Available'" fluid class="main-container">
    <!-- Course Overview Header -->
    <CourseHeader :data="courseHeaderData" />

    <!-- Course Description Section -->
    <section class="description-section">
      <v-card class="description-card" elevation="0">
        <v-card-title class="description-title">
          <h3>About This Course</h3>
        </v-card-title>
        <v-card-text class="description-content-wrapper">
          <div
            v-html="data[0]?.description"
            class="description-text"
            :class="{ 'clamped': !showFullDescription }"
          ></div>
          <v-btn
            v-if="data[0].description && data[0].description.length > 300"
            variant="text"
            color="primary"
            class="show-more-btn"
            @click="showFullDescription = !showFullDescription"
          >
            {{ showFullDescription ? 'Show Less' : 'Show More' }}
            <v-icon size="16" class="ml-1">
              {{ showFullDescription ? 'mdi-chevron-up' : 'mdi-chevron-down' }}
            </v-icon>
          </v-btn>
        </v-card-text>
      </v-card>
    </section>

    <!-- Course Levels Section -->
    <section class="levels-section" id="course-levels">
      <div class="section-header">
        <h3 class="section-title">Course Levels</h3>
        <p class="section-subtitle">Choose your learning path</p>
      </div>

      <div class="levels-grid">
        <v-card
          v-for="(level, levelIndex) in difficultyLevels"
          :key="level.id"
          class="level-card"
          elevation="0"
        >
          <!-- Level Header -->
          <v-card-title class="level-header">
            <div class="level-info">
              <img :src="level.author.image" alt="Author" class="author-avatar" />
              <div class="level-details">
                <div class="level-title">
                  <h4>{{ level.level }} Level</h4>
                </div>
                <div class="level-meta">
                  <span class="level-price">
                    <template v-if="level.price">
                      {{ CurrencyCode === "INR" ? "₹" : "$" }}{{ formatIndianCurrency(getCurrencyPrice(level?.currencies) || 0) }}
                    </template>
                    <template v-else>Free</template>
                  </span>
                  <v-chip v-if="level.purchased" size="small" color="primary">Purchased</v-chip>
                </div>
              </div>
            </div>

            <div class="action-buttons">
              <p>it is {{ level.NotInCart }}</p>
              <v-btn
                v-if="!level.purchased && level.NotInCart"
                @click="openOfferDrawer(level.id)"
                variant="outlined"
                class="white--text rounded-xl transparent-bg action-btn"
                height="36"
                size="small"
              >
                Add to Cart
              </v-btn>

              <v-btn
                v-if="!level.purchased"
                @click="buyLevel(level)"
                height="36"
                size="small"
                color="white"
                class="white--text rounded-xl action-btn"
              >
                Buy Now
              </v-btn>
             
              <v-btn
                v-else
                @click="accessLevel(level)"
                height="36"
                size="small"
                color="white"
                class="white--text rounded-xl action-btn"
              >
                Access Now
              </v-btn>
            </div>

          </v-card-title>

          <!-- Level Stats -->
          <v-card-text class="level-stats">
            <div class="stats-row">
              <div class="stat-item">
                <v-icon size="15" color="rgba(256, 256, 256, 0.7)">mdi-play-circle</v-icon>
                <span>{{ getTotalLessons(level) }} Lessons</span>
              </div>
              <div class="stat-item">
                <v-icon size="15" color="rgba(256, 256, 256, 0.7)">mdi-clock</v-icon>
                <span>{{ getDurationString(level.duration) }}</span>
              </div>
              <div class="stat-item">
                <v-icon size="15" color="rgba(256, 256, 256, 0.7)">mdi-translate</v-icon>
                <span>{{ level.language }}</span>
              </div>
            </div>
          </v-card-text>

          <!-- Chapters -->
          <v-expansion-panels 
            v-model="chapterPanels[levelIndex]" 
            class="chapters-panel"
            multiple
          >
            <v-expansion-panel
              v-for="(chapter, chapterIndex) in getVisibleChapters(level, levelIndex)"
              :key="chapter.id"
              class="chapter-item"
            >
              <v-expansion-panel-title class="chapter-title">
                <span class="chapter-number">{{ String(chapterIndex + 1).padStart(2, '0') }}</span>
                <span class="chapter-name">{{ chapter.name }}</span>
              </v-expansion-panel-title>
              <v-expansion-panel-text class="chapter-content">
                <div class="lessons-list">
                  <div
                    v-for="(play, playIndex) in chapter.playList"
                    :key="play?.id || playIndex"
                    @click="playVideo(level.id, chapterIndex, playIndex, play.filePath, level.purchased || play.access == 1)"
                    class="lesson-item"
                    :class="{ 'lesson-locked': !(play.access == 1 || level.purchased) }"
                  >
                    <div class="lesson-icon">
                      <v-icon size="18" :color="play.access == 1 || level.purchased ? 'primary' : 'grey'">
                        {{ play.access == 1 || level.purchased ? 'mdi-play' : 'mdi-lock' }}
                      </v-icon>
                    </div>
                    <div class="lesson-info">
                      <span class="lesson-number">{{ chapterIndex + 1 }}.{{ playIndex + 1 }}</span>
                      <span class="lesson-name">{{ play.name || 'Untitled Lesson' }}</span>
                    </div>
                    <div class="lesson-duration">
                      {{ getDurationString(play.duration || 0) }}
                    </div>
                  </div>
                </div>
              </v-expansion-panel-text>
            </v-expansion-panel>
          </v-expansion-panels>
          <div class="view-more-container text-center my-2">
          <v-btn
            v-if="level.chapters.length > (chaptersVisible[levelIndex] || 10)"
            variant="text"
            size="small"
            color="primary"
            @click="toggleViewMore(levelIndex, level.chapters.length)"
          >
            View More
          </v-btn>
          <v-btn
            v-else-if="level.chapters.length > 10"
            variant="text"
            size="small"
            color="primary"
            @click="toggleViewLess(levelIndex)"
          >
            View Less
          </v-btn>
        </div>
        </v-card>
      </div>
    </section>
  </v-container>

  <!-- Not Available State -->
  <div v-else class="not-available-state">
    <v-icon size="64" color="grey-lighten-1">mdi-book-off</v-icon>
    <h3 class="mt-4 mb-2">Course Not Available</h3>
    <p class="text-grey-lighten-1 mb-6">The course you're looking for doesn't exist or has been removed.</p>
    <v-btn color="primary" variant="elevated" @click="$router.push('/')">
      Browse Courses
    </v-btn>
  </div>

  <CoursesDiscountPopup
  v-model:open="showOfferDialog"
  :courses="courseList"
  :initialSelectedCourseId="selectedCourseId"
  @update:open="showOfferDialog = $event"
  @select="handleCourseSelection"
  @full-suite="handleFullSuite"
/>
</template>

<script setup>
import {
  sendDataToParent,
  getDurationString,
  formatIndianCurrency,
  sortCourseLevels,
} from "@/helper/common";
import { getCourseContent } from "@/services/courseService";
import { ref, computed, onBeforeMount, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import dummyImage from "@/assets/image_not_available.jpg";
import { getUserToken, setUserToken } from "@/services/userService";
import { useAppStore } from "@/stores/app";
import CourseHeader from "@/components/CourseHeader.vue";
import { getProductIdByItemId } from "@/services/paidVideoService";
import CoursesDiscountPopup from "@/components/courseDiscountPopup.vue";
import { getAllProducts, updateCartAPI } from "@/services/productService";

const appStore = useAppStore();
const router = useRouter();
const route = useRoute();
const courseId = route.params.id;


const showOfferDialog = ref(false);

const courseList = ref([]);

const selectedCourseId = ref(null);

const openOfferDrawer = async(levelId) => {
  selectedCourseId.value = levelId;
  await callCourses();
};

const callCourses = async()=>{
  const updates = {};
    updates.artistId = [8];
    updates.categoryId = [8]
    const result = await getAllProducts(updates.artistId, updates.categoryId, null, true);
    if(result) {
    courseList.value = result;
    showOfferDialog.value = true;
    } else {
      console.error("Failed to fetch courses.");
    }
}

let isLoading = ref(true);
let data = ref(null);
const showFullDescription = ref(false);
const chapterPanels = ref([]);

const CurrencyCode = appStore.currencyCode;
const difficultyLevels = computed(() => {
  return data.value && data.value ? sortCourseLevels(data.value) : [];
});


const chaptersVisible = ref({});

watch(difficultyLevels, (newLevels) => {
  if (Array.isArray(newLevels)) {
    chapterPanels.value = newLevels.map(() => []);
    chaptersVisible.value = Object.fromEntries(newLevels.map((level, index) => [index, 10]));
  }
}, { immediate: true });

const toggleViewMore = (levelIndex, totalChapters) => {
  const current = chaptersVisible.value[levelIndex];
  const next = current + 10;
  chaptersVisible.value[levelIndex] = Math.min(next, totalChapters);
};

const toggleViewLess = (levelIndex) => {
  chaptersVisible.value[levelIndex] = 10;
};

const getVisibleChapters = (level, index) => {
  return level.chapters.slice(0, chaptersVisible.value[index] || 10);
};

function getCurrencyPrice(currencies) {
  const currency = currencies.find(c => c.currency === CurrencyCode)
  return currency ? currency.price : 0
}

const getCourseData = async () => {
  try {
    data.value = await getCourseContent({
      courseId: courseId,
      courselevelavail: false,
      CurrencyCode
    });
    isLoading.value = false;
  } catch (error) {
    isLoading.value = false;
    console.error(error);
  }
};

const courseHeaderData = computed(() => {
  if (!difficultyLevels.value || difficultyLevels.value.length === 0) return {};
  
  const levels = difficultyLevels.value;
  const firstLevel = levels[0];
  
  // Calculate aggregated data across all levels
  const totalLessons = levels.reduce((total, level) => {
    return total + getTotalLessons(level);
  }, 0);
  
  const totalDuration = levels.reduce((total, level) => {
    if (level.duration) {
      return {
        hours: (total.hours || 0) + (level.duration.hours || 0),
        minutes: (total.minutes || 0) + (level.duration.minutes || 0),
        seconds: (total.seconds || 0) + (level.duration.seconds || 0)
      };
    }
    return total;
  }, { hours: 0, minutes: 0, seconds: 0 });
  
  // Normalize duration (convert seconds to minutes, minutes to hours)
  const normalizedDuration = normalizeDuration(totalDuration);
  
  const totalPrice = levels.reduce((total, level) => {
  // Find the matching currency
  const matchingCurrency = level.currencies.find(
    (c) => c.currency === CurrencyCode
  );

  // Use the matching price if found, else fallback to 0
  const price = matchingCurrency ? matchingCurrency.price : 0;

  return total + price;
}, 0);
  
  // Find the most recent update date
  const lastUpdated = levels.reduce((latest, level) => {
    if (level.updated) {
      const levelDate = new Date(level.updated);
      const latestDate = latest ? new Date(latest) : new Date(0);
      return levelDate > latestDate ? level.updated : latest;
    }
    return latest;
  }, null);
  
  // Get unique languages
  const languages = [...new Set(levels.map(level => level.language).filter(Boolean))];
  const languageDisplay = languages.length > 0 ? languages.join(', ') : firstLevel.language;
  
  return {
    id: courseId,
    levelId: firstLevel.id,
    image: firstLevel.image || dummyImage,
    authorName: firstLevel.author?.name || "",
    artistId : firstLevel.artist_id,
    title: firstLevel.title,
    price: totalPrice,
    currency: 
     (CurrencyCode === "INR" ? "₹" : "$"),
    duration: normalizedDuration,
    language: languageDisplay,
    updated: lastUpdated,
    isPurchased: levels.some(level => level.purchased),
    tax: firstLevel.tax,
    isSeries: levels.length > 1,
    levelCount: levels.length,
    totalLessons: totalLessons,
  };
});

// Helper function to normalize duration
const normalizeDuration = (duration) => {
  let { hours = 0, minutes = 0, seconds = 0 } = duration;
  
  // Convert seconds to minutes
  if (seconds >= 60) {
    minutes += Math.floor(seconds / 60);
    seconds = seconds % 60;
  }
  
  // Convert minutes to hours
  if (minutes >= 60) {
    hours += Math.floor(minutes / 60);
    minutes = minutes % 60;
  }
  
  return { hours, minutes, seconds };
};

const getTotalLessons = (level) => {
  if (!level || !level.chapters) return 0;
  return level.chapters.reduce((total, chapter) => total + chapter.playList.length, 0);
};

const buyLevel = (level) => {
  router.push({
    path: `/singleCheckout/${level.id}`,
    query: {
      productId: level.id,
      image: level.image || dummyImage,
      name: `${level.title} - ${level.level} Level`,
      tax: level.tax,
      price: level.price,
      currency: (level.currency === "INR" ? "₹" : "$"),
      quantity: 1,
      type: 3,
      courseIdAvail: true, 
    },
  });
};

const accessLevel = (level) => {
  router.push({
    path: `/course-video/${level.id}`,
    query: {
      courselevelavail: true,
    },
  });
};

const handleCourseSelection = (selected) => {
  updateCart(selected);
};

const handleFullSuite = () => {
  const items = [
    {product_id:5},
    {product_id:6},
    {product_id:7},
    {product_id:8},
    {product_id:9},
    {product_id:10},
    {product_id:11},
    {product_id:12},
    {product_id:13},
  ];
  updateCart(items);
};

const updateCart = async (items) => {
  try {
    await Promise.all(items.map(async (item) => {
      console.log(item, item.product_id);
      
      const productId = await getProductIdByItemId(item.product_id, 2);
      const payload = {
        productItemId: productId,
        quantity: 1,
        currency: CurrencyCode === "INR" ? 1 : 2,
      };

      if (!getUserToken()) {
        const encodedPayload = encodeURIComponent(JSON.stringify(payload));
        router.push({
          path: "/login",
          query: {
            toSend: "/cart",
            action: "updateCart",
            payload: encodedPayload,
          },
        });
        return;
      }

      console.log(payload, "payload");
      await updateCartAPI(payload);
    }));

    // After all updates, redirect to cart
    router.push({ path: "/cart" });
  } catch (error) {
    console.error("Error in Update Cart", error);
  }
};

const playVideo = (courseId, chapterIndex, playListIndex, streamingUrl, isPurchased) => {
  if (streamingUrl && isPurchased) {
    router.push({
      path: `/course-video/${courseId}`,
      query: {
        chapterIndex: chapterIndex,
        playListIndex: playListIndex,
        courselevelavail: true,
      },
    });
  } else if (!isPurchased) {
    console.log("Purchase required to access this content");
  }
};

onBeforeMount(async () => {

  if (!courseId) {
    return null;
  }
  if (route.query.token) {
    setUserToken(route.query.token);
  } else if (!getUserToken()) {
    sendDataToParent({ message: "Login Needed" });
    router.push({
      path: `/login`,
      query: {
        toSend: `/course/${courseId}`,
        query: {},
      },
    });
    return;
  }
  getCourseData();
});
</script>

<style scoped>

.main-loader {
  min-height: calc(100vh - 80px);
}

.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

/* Description Section */
.description-section {
  margin: 2rem 0;
}

.description-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  overflow: hidden;
}

.description-title {
  padding: 1.5rem 2rem 0;
  color: white;
  font-weight: 600;
}

.description-content-wrapper {
  padding: 0 2rem 1.5rem;
}

.description-text {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.7;
  margin-bottom: 1rem;
}

.description-text.clamped {
  max-height: 200px;
  overflow: hidden;
  position: relative;
}

.description-text.clamped::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(transparent, rgba(13, 13, 13, 0.9));
}

.show-more-btn {
  text-transform: none;
  font-weight: 500;
  padding: 0;
  min-width: auto;
}

/* Levels Section */
.levels-section {
  margin: 5rem 0;
}

.section-header {
  margin-bottom: 2rem;
  margin-left: 1rem;
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.25rem;
}

.section-subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.levels-grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: 1fr;
}

.level-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.level-card:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Level Header */
.level-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.level-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.author-avatar {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.level-details {
  flex: 1;
}

.level-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.level-title h4 {
  font-size: 20px;
  font-weight: 600;
  color: #fff;
}

.chapter-level{
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: rgba(33, 150, 243, 0.2);
  color: #2196F3;
  border-radius: 8px;
  font-size: 14px;
  border: none;
  padding: 15px 20px;
}

.level-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.level-price {
  font-size: 1.2rem;
  font-weight: 700;
  color: #fff;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.action-btn {
  border-radius: 12px;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  padding: 0 20px;
}

.action-btn:hover {
  background-color: rgba(255, 255, 255, 0.9) !important;
  color: #000;
}

/* Level Stats */
.level-stats {
  padding: 1rem 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.stats-row {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
}

/* Chapters */
.chapters-panel {
  background: transparent;
}

.chapter-item {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.chapter-item:last-child {
  border-bottom: none;
}

.chapter-title {
  padding: 1rem 2rem;
  color: white;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.chapter-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(33, 150, 243, 0.2);
  color: #2196F3;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
}

.chapter-name {
  flex: 1;
}

.chapter-content {
  padding: 0 2rem 1rem;
}

/* Lessons */
.lessons-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.lesson-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid transparent;
}

.lesson-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.lesson-item.lesson-locked {
  opacity: 0.5;
  cursor: not-allowed;
}

.lesson-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
}

.lesson-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.lesson-number {
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
  font-size: 0.9rem;
  min-width: 2.5rem;
}

.lesson-name {
  color: #aaa;
  font-weight: 500;
  font-size: 15px;
}

.lesson-duration {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  font-weight: 500;
}

/* Not Available State */
.not-available-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-container {
    padding: 0 0.5rem;
  }
  
  .description-section {
    margin: 1.5rem 0;
  }
  
  .description-card {
    border-radius: 12px;
  }
  
  .description-title {
    padding: 1rem 1.5rem 0;
  }
  
  .description-content-wrapper {
    padding: 0 1.5rem 1rem;
  }
  
  .description-text {
    font-size: 14px;
    line-height: 1.6;
  }
  
  .description-text.clamped {
    max-height: 150px;
  }
  
  .section-title {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
  }
  
  .section-subtitle {
    font-size: 14px;
  }
  
  .levels-section {
    margin: 3rem 0;
  }
  
  .levels-grid {
    gap: 1.5rem;
  }
  
  .level-card {
    border-radius: 16px;
  }
  
  .level-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .level-info {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
  
  .author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 10px;
  }
  
  .level-title h4 {
    font-size: 18px;
  }
  
  .level-meta {
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .level-price {
    font-size: 1.1rem;
  }
  
  .action-buttons {
    width: 100%;
    justify-content: center;
    gap: 0.5rem;
  }
  
  .action-btn {
    font-size: 11px;
    padding: 0 16px;
    height: 32px;
    flex: 1;
    min-width: 120px;
  }
  
  .level-stats {
    padding: 0.75rem 1rem;
  }
  
  .stats-row {
    gap: 0.75rem;
  }
  
  .stat-item {
    font-size: 12px;
    justify-content: center;
  }
  
  .chapter-title {
    gap: 0.5rem;
  }
  
  .chapter-number {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }
  
  .chapter-content {
    padding: 0 1rem 0.75rem;
  }
  
  .lesson-item {
    padding: 0.5rem 0.75rem;
    gap: 0.75rem;
  }
  
  .lesson-icon {
    width: 28px;
    height: 28px;
  }
  
  .lesson-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .lesson-number {
    font-size: 0.8rem;
    min-width: 2rem;
  }
  
  .lesson-name {
    font-size: 14px;
    line-height: 1.4;
  }
  
  .lesson-duration {
    font-size: 12px;
  }
  
  .view-more-container {
    margin: 1rem 0;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .main-container {
    padding: 0 0.25rem;
  }
  
  .description-title {
    font-size: 18px;
    padding: 0.75rem 1rem 0;
  }
  
  .description-content-wrapper {
    padding: 0 1rem 0.75rem;
  }

  .show-more-btn{
    font-size: 12px;
  }

  .section-title {
    font-size: 1.5rem;
  }
  
  .section-subtitle {
    font-size: 13px;
  }
  
  .levels-section {
    margin: 2rem 0;
  }
  
  .level-header {
    padding: 0.75rem;
  }
  
  .author-avatar {
    width: 45px;
    height: 45px;
  }
  
  .level-title h4 {
    font-size: 16px;
  }
  
  .level-price {
    font-size: 1rem;
  }
  
  .action-buttons {
    gap: 0.25rem;
  }
  
  .action-btn {
    font-size: 10px;
    padding: 0 12px;
    height: 30px;
    min-width: 100px;
  }
  
  .level-stats {
    padding: 1rem 1.5rem;
  }
  
  .stat-item {
    font-size: 11px;
  }
  
  .chapter-title {
    padding: 1rem 1.5rem;
  }

  .chapter-name{
    font-size: 13px;
  }
  
  .chapter-number {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
  }
  
  .chapter-content {
    padding: 0 0.75rem 0.5rem;
  }
  
  .lesson-item {
    padding: 0.4rem 0.5rem;
    gap: 0.5rem;
  }
  
  .lesson-icon {
    width: 24px;
    height: 24px;
  }
  
  .lesson-number {
    font-size: 0.75rem;
    min-width: 1.8rem;
  }
  
  .lesson-name {
    font-size: 13px;
  }
  
  .lesson-duration {
    font-size: 11px;
  }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .main-container {
    padding: 0 1rem;
  }
  
  .level-header {
    flex-direction: row;
    align-items: center;
  }
  
  .level-info {
    flex-direction: row;
    text-align: left;
  }
  
  .stats-row {
    flex-direction: row;
    gap: 1rem;
  }
  
  .lesson-info {
    flex-direction: row;
    align-items: center;
  }
  
  .action-buttons {
    width: auto;
    justify-content: flex-end;
  }
  
  .action-btn {
    flex: none;
    min-width: auto;
  }
}</style>