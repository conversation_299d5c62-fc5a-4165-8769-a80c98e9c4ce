<template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular :size="70" :width="7" color="white" indeterminate></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <v-container v-else>
    <CourseHeader :data="headerData" />
    <v-row>
      <v-col>
        <CourseTable :chapters="data.chapters" :id="data.id" :purchased="data.purchased" />
      </v-col>
      <v-col cols="12" md="6" sm="12" lg="6">
        <h4>Description</h4>
        <div v-html="data.description"></div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import CourseHeader from "@/components/CourseHeader.vue";
import CourseTable from "@/components/CourseTable.vue";
import { sendDataToParent } from "@/helper/common";
import { getCourseContent } from "@/services/courseService";
import { ref, onBeforeMount } from "vue";
import { useRouter, useRoute } from "vue-router";
import dummyImage from "@/assets/image_not_available.jpg";
import { getUserToken, setUserToken } from "@/services/userService";
import { useAppStore } from "@/stores/app";

const appStore = useAppStore();
const router = useRouter();
const route = useRoute();
const courseId = route.params.id;
let isLoading = ref(true);

let data = ref(null);
let headerData = {};

const CurrencyCode = appStore.currencyCode;

const getCourseData = async () => {
  try {
    data.value = await getCourseContent(courseId);

    const selectedCurrency = data.value.currencies.find(
      (currency) => currency.currency_id === (CurrencyCode === "INR" ? 1 : 2)
    );
    headerData = {
      authorImage: data.value.author.image,
      authorName: data.value.author.name,
      title: data.value.title,
      isPurchased: data.value.purchased,
      duration: data.value.duration,
      language: data.value.language,
      updated: data.value.updated,

      // Purchase data
      id: data.value.id,
      image: data.value.image || dummyImage,
      tax: selectedCurrency.gst,
      type: 3,
      price: selectedCurrency.price,
      currency: CurrencyCode === "INR" ? "₹" : "$",
      quantity: 1,
    };
    isLoading.value = false;
  } catch (error) {
    isLoading.value = true;
    console.error(error);
  }
};


onBeforeMount(async () => {
  if (!courseId) {
    return null;
  }
  if (route.query.token) {
    setUserToken(route.query.token);
  } else if (!getUserToken()) {
    sendDataToParent({ message: "Login Needed" });
    router.push({
      path: `/login`,
      query: {
        toSend: `/course/${courseId}`,
        query: {},
      },
    });
    return;
  }
  getCourseData();
});
</script>

<style></style>
