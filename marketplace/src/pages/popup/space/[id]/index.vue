<template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular
          :size="70"
          :width="7"
          color="white"
          indeterminate
        ></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <template v-else>
    <v-row
      class="fill-height ma-0 align-center justify-center"
      v-if="type == 1"
    >
      <v-col cols="12" class="text-center">
        <v-container>
          {{ data.text }}
        </v-container>
      </v-col>
    </v-row>
    <TextVideo
      :text="data.text"
      :video="data.video_stream_url"
      v-if="type == 2"
    />
    <OnlyImage :images="data.images" v-if="type == 3"></OnlyImage>
    <OnlyVideo :video="data.video_stream_url" v-if="type == 4" class="ma-5" />
    <TextAudio :audioSrc="data.audioLink" :text="data.text" v-if="type == 5" />
    <TextAudioImage
      :audioSrc="data.audioLink"
      :images="data.images"
      :text="data.text"
      v-if="type == 6"
    />
    <TextImage
      :images="data.images"
      :text="data.text"
      v-if="type == 7"
    />
  </template>
</template>

<script setup>
import OnlyVideo from "@/components/popup/OnlyVideo.vue";
import OnlyImage from "@/components/popup/OnlyImage.vue";
import TextAudio from "@/components/popup/TextAudio.vue";
import TextAudioImage from "@/components/popup/TextAudioImage.vue";
import TextVideo from "@/components/popup/TextVideo.vue";
import TextImage from "@/components/popup/TextImage.vue"; // New component
import { getPopupContent } from "@/services/artistService";
import { ref, onBeforeMount } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();

const isLoading = ref(true);
const popupId = route.params.id;
const data = ref();
const type = ref(0);

const assignValueType = (data) => {
  if (
    data.text &&
    !data.video &&
    !data.audioLink &&
    (!data.images || data.images.length === 0)
  ) {
    return (type.value = 1); // Only text
  } else if (
    data.text &&
    data.video &&
    (!data.images || data.images.length === 0) &&
    !data.audioLink
  ) {
    return (type.value = 2); // Text and video
  } else if (
    data.images &&
    data.images.length > 0 &&
    !data.text &&
    !data.video &&
    !data.audioLink
  ) {
    return (type.value = 3); // Only images
  } else if (
    data.video &&
    !data.text &&
    !data.audioLink &&
    (!data.images || data.images.length === 0)
  ) {
    return (type.value = 4); // Only video
  } else if (
    data.text &&
    data.audioLink &&
    !data.video &&
    (!data.images || data.images.length === 0)
  ) {
    return (type.value = 5); // Text and audio
  } else if (
    data.text &&
    data.audioLink &&
    data.images &&
    data.images.length > 0 &&
    !data.video
  ) {
    return (type.value = 6); // Text, images, and audio
  } else if (
    data.text &&
    data.images &&
    data.images.length > 0 &&
    !data.video &&
    !data.audioLink
  ) {
    return (type.value = 7); // Text and images
  }
  return (type.value = 0);
};

onBeforeMount(async () => {
  if (!popupId) {
    return;
  }
  try {
    const resp = await getPopupContent(popupId);
    assignValueType(resp);
    data.value = resp;
    isLoading.value = false;
  } catch (error) {
    console.error("Error in getting popup content", error);
  }
});
</script>