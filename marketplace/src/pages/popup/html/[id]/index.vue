<template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular
          :size="70"
          :width="7"
          color="white"
          indeterminate
        ></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <template v-else>
    <div v-html="htmlContent"></div>
  </template>
</template>
<script setup>
import { getHTMLPopupContent } from "@/services/artistService";
import { ref, onBeforeMount, onMounted } from "vue";

const route = useRoute();

const isLoading = ref(true);
const popupId = route.params.id;
const htmlContent = ref();

onBeforeMount(async () => {
  if (!popupId) {
    return;
  }
  try {
    const resp = await getHTMLPopupContent(popupId);
    htmlContent.value = resp.htmlContent;
    isLoading.value = false;
  } catch (error) {
    console.error("Error in getting popup content", error)
  }
});
</script>

<style scoped>
</style>
