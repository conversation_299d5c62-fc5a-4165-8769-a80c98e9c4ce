<template>
    <template v-if="isLoading">
        <v-row class="fill-height ma-0 align-center justify-center">
            <v-col cols="12" class="text-center">
                <v-progress-circular :size="70" :width="7" color="white" indeterminate></v-progress-circular>
            </v-col>
        </v-row>
    </template>
    <Snackbar ref="snackbarRef" />
</template>

<script setup>
import { ref, onBeforeMount, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getOrderType } from "@/helper/common";
import {
    orderNowinitMeetPaypal,
} from "@/services/productService";
import { getUserAddress } from "@/services/userService";
import { cancelMeetBook, } from "@/services/saleAssistService";
import { callingArtistDataViaSource, cancelCalendlyEvent, startCalendlyPayment } from "@/services/calendly";
import Snackbar from "@/components/Snackbar.vue";
import { useAppStore } from "@/stores/app";

const appStore = useAppStore();
const snackbarRef = ref(null);
const router = useRouter();
const route = useRoute();
let isLoading = ref(true);
let address = ref({
    first_name: "",
    last_name: "",
    country: "",
    address_line_one: "",
    address_line_two: "",
    address_line_three: "",
    city: "",
    state: "",
    pincode: "",
    phone: "",
    email: "",
});
let assignedTo = "";
let eventTypeUUID = "";
let artistMeetId = "";
let eventTypeName = "";
let eventTime = "";
let eventDate = "";
let inviteeUUID = "";
let inviteeFullName = "";
let inviteeEmail = "";

const respaddress = ref(null);
const selectedCurrency = appStore.currencyCode;

let paymentDeclined = ref(false);

let orderId = null;
let mTxnId = null;

function callback(response) {
    if (response === "USER_CANCEL") {
        paymentDeclined.value = true;
        orderCancel();
        return;
    } else if (response === "CONCLUDED") {
        router.push({
            path: `/orderDetail/${orderId}`,
            query: {
                order_type: getOrderType(2),
            },
        });
        return;
    }
}

function callbackForPayglocal(response) {
    if (response.status === "CUSTOMER_CANCELLED") {
        paymentDeclined.value = true;
        orderCancel();
        return;
    } else if (response.status === "SENT_FOR_CAPTURE") {
        router.push({
            path: `/orderDetail/${orderId}`,
            query: {
                order_type: getOrderType(2),
            },
        });
        return;
    }
}

const orderCancel = async () => {
    const cancelResp = await cancelCalendlyEvent({
        "source_id": eventTypeUUID,
        "email_id": inviteeEmail,
    })

    if (cancelResp.status) {
        router.push({
            path: "/"
        })
    } else {
        snackbarRef?.value?.showSnackbar(
            `error in canceling payment "${error?.message}"`,
            "red"
        );
    }
    // return;
};

const openPayment = (tokenUrl) => {
    try {
        window.PhonePeCheckout.transact({
            tokenUrl,
            callback,
            type: "IFRAME",
        });
    } catch (error) {
        console.error("Error in opening payment", error);
         setTimeout(() => {
        snackbarRef?.value?.showSnackbar(
            `error in opening payment "${error?.message}"`,
            "red"
        );
        }, 0);
    }
};

const openPaymentForPayGlocal = (tokenUrl) => {
    try {
        window.PGPay.launchPayment({
            redirectUrl: tokenUrl
        },
            callbackForPayglocal
        );
    } catch (error) {
        console.error("Error in opening payment", error);
         setTimeout(() => {
        snackbarRef.value.showSnackbar(
            `error in opening payment 2"${error?.message}"`,
            "red"
        );}, 0);
    }
};

const paymentMethodNum = {
    phonepe: 1,
    paypal: 2,
    payglocal: 3,
}

const meetOrderPlace = async (paymentMethod) => {
    try {
        let response;
        // if (isAddressChanged.value) {
        //     response = await orderNowinitMeetPaypal({
        //         artistMeetId: singleProduct.value.artistMeetId,
        //         slotTime: singleProduct.value.slotTime,
        //         slotDate: singleProduct.value.slotDate,
        //         formId: singleProduct.value.id,
        //         address: address.value,
        //         coupon: appliedCoupon.value,
        //         currency: selectedCurrency === "INR" ? 1 : 2,
        //         payment_method: paymentMethodNum[paymentMethod],
        //     });
        // } else {

        // response = await orderNowinitMeetPaypal({
        //     artistMeetId: artistMeetId,
        //     slotTime: eventTime,
        //     slotDate: eventDate,
        //     formId: eventTypeUUID,
        //     address_id: address.value.address_id || 3,
        //     // address_id: address.value.id,
        //     // coupon: appliedCoupon.value,
        //     coupon: null,
        //     currency: selectedCurrency === "INR" ? 1 : 2,
        //     payment_method: paymentMethodNum[paymentMethod],
        // });

        response = await startCalendlyPayment({
            "event_id": eventTypeUUID,
        })

        if (response) {
            if (paymentMethod === "phonepe") {
                orderId = response.selectedPayment.id;
                mTxnId = response.selectedPayment.order_id;
                const url = response.selectedPayment.url;
                isLoading.value = false;
                openPayment(url);
            }

            if (paymentMethod === "payglocal") {
                let url = response.selectedPayment.url;
                orderId = response.selectedPayment.id;
                mTxnId = response.selectedPayment.order_id;
                isLoading.value = false;
                openPaymentForPayGlocal(url);
            }
        } else {
            console.error("Invalid API response from orderNowinitMeet");
        }
    } catch (error) {
        console.error("Error in orderNowinitMeet API", error);
         setTimeout(() => {
        snackbarRef?.value?.showSnackbar(
            `error in Calendly payment "${error?.message}"`,
            "red"
        );}, 0);
    }
};

const fetchCart = async () => {
    await fetchAddress();

    if (route.query) {
        assignedTo = route.query.assigned_to;
        eventTypeUUID = route.query.event_type_uuid;
        eventTypeName = route.query.event_type_name;
        inviteeUUID = route.query.invitee_uuid;
        inviteeFullName = route.query.invitee_full_name;
        inviteeEmail = route.query.invitee_email;
    }

    if (route.query.event_start_time) {
        const eventStartTime = route.query.event_start_time;

        const formattedDate = computed(() => {
            if (!eventStartTime) return '';
            const datePart = eventStartTime.split('T')[0];
            const [year, month, day] = datePart.split('-');
            return `${day}/${month}/${year}`;
        });

        const formattedTime = computed(() => {
            if (!eventStartTime) return '';
            const timePart = eventStartTime.split('T')[1].split('+')[0];
            let [hour, minute] = timePart.split(':');
            hour = parseInt(hour);
            const ampm = hour >= 12 ? 'PM' : 'AM';
            hour = hour % 12 || 12;
            return `${hour}:${minute} ${ampm}`;
        });

        eventTime = formattedTime.value;
        eventDate = formattedDate.value;
    }
    // if (eventTypeUUID) {
    //     try {
    //         const response = await callingArtistDataViaSource(eventTypeUUID);
    //         if (response) {
    //             artistMeetId = response.data.artsit_id;
    //         }
    //     } catch (e) {
    //         console.error("fetchCart", e);
    //     }
    // }

    await validateAndSubmit();

};

const fetchAddress = async () => {
    try {
        const resp = await getUserAddress();
        if (resp.address) {

            address.value = resp.address;
            respaddress.value = structuredClone(resp.address);
            address.value.country = "India";
            respaddress.value.country = "India";
        } else {
            respaddress.value = null;
            address.value = {
                first_name: "",
                last_name: "",
                country: "",
                address_line_one: "",
                address_line_two: "",
                address_line_three: "",
                city: "",
                state: "",
                pincode: "",
                phone: "",
                email: "",
            };
        }
    } catch (error) {
        console.error("Error in getUserAddress API", error);
         setTimeout(() => {
        snackbarRef?.value?.showSnackbar(
            `error in get User Address "${error?.message}"`,
            "red"
        );}, 0);
    }
};

const validateAndSubmit = async () => {
    await meetOrderPlace(selectedCurrency === "INR" ? "phonepe" : "payglocal");
};

onBeforeMount(async () => {
    await fetchCart();

});
</script>

<style scoped>
.dark-input {
    background-color: transparent;
    border: 0.2px solid white;
    border-radius: 25px;
    color: rgba(255, 255, 255, 0.9);
    padding: 12px 20px;
    font-size: 16px;
    width: 100%;
    outline: none;
}

.dark-input::placeholder {
    color: white;
}

.dark-input:focus {
    border-color: white;
    box-shadow: 0 0 0 1px white;
}

.error-input {
    border-color: red;
    box-shadow: 0 0 0 1px red;
}

.error-message {
    color: red;
    font-size: 12px;
    margin-top: 4px;
    padding-left: 20px;
}

.full-width-table {
    width: 100%;
    border-collapse: collapse;
    color: white;
}

.full-width-table td {
    padding: 8px 0;
}

.full-width-table tr:last-child td {
    padding-top: 16px;
    font-weight: bold;
}
</style>