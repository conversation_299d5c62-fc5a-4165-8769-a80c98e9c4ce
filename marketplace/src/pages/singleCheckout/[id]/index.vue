<template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular :size="70" :width="7" color="white" indeterminate></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <v-container v-else class="py-8">
    <v-row class="ma-0">
      <v-col cols="12" md="8" class="pr-md-6">
        <v-card class="mb-6 transparent  pa-4" flat>
          <h2 class="text-h5 grey--text text--lighten-2 mb-2">Your Order</h2>

          <div v-if="singleProduct" class="pa-3 rounded-lg"
            style="background-color: rgba(255, 255, 255, 0.04); border: 1px solid rgba(255, 255, 255, 0.08);">
            <v-row no-gutters>
              <v-col cols="4" class="d-flex align-center justify-center">
                <v-img :src="singleProduct.image" height="150" contain class="rounded-lg"></v-img>
              </v-col>

              <v-col cols="8" class="px-3 py-2 d-flex flex-column justify-space-between">
                <div>
                  <div class="text-body-2 font-weight-medium grey--text text--lighten-1">
                    {{ singleProduct.brand_name }}
                  </div>
                  <div class="text-body-1 font-weight-bold white--text">
                    {{ singleProduct.name }}
                  </div>
                </div>

                <div class="d-flex justify-space-between align-center mt-3">
                  <span class="text-body-2 white--text">Qty: {{ singleProduct.quantity }}</span>
                  <span class="text-body-2 white--text">
                    {{ currencySymbol }} {{ formatIndianCurrency(selectedCurrency === "INR" ?getTotal(): getSubtotal) }}
                  </span>
                </div>
              </v-col>
            </v-row>
          </div>
        </v-card>


        <v-card class="mb-6 transparent" flat>
          <h3 class="text-h6 white--text mb-2 pa-4">Shipping Address</h3>
          <v-card class="pa-4 rounded-lg  ma-4 mt-0" style="background: rgba(255, 255, 255, 0.05);">
            <div class="shipping-address-container"
              style="display: flex; justify-content: space-between; flex-wrap: wrap; gap: 16px;">
              <div class="address-details" style="flex: 1 1 65%; color: white;">
                <div class="text-h6">{{ address.first_name }} {{ address.last_name }}</div>
                <div>{{ address.address_line_one }}</div>
                <div v-if="address.address_line_two">{{ address.address_line_two }}</div>
                <div v-if="address.address_line_three">{{ address.address_line_three }}</div>
                <div>
                  {{ address.city }}, {{ address.state }}, {{ address.country }} - {{ address.pincode }}
                </div>
                <div>{{ address.phone }}</div>
                <div>{{ address.email }}</div>
              </div>

              <!-- Action Buttons -->
              <div class="address-actions"
                style="display: flex; flex-direction: column; align-items: flex-end; justify-content: space-between; padding: 10px; gap: 8px;">
                <button @click="openEditDialog(address)" class="action-button">
                  Edit
                </button>
                <button @click="showAddressPopup = true" class="action-button">
                  <v-icon size="15" class="mr-2">mdi-home-map-marker</v-icon>
                  Choose Another
                </button>
              </div>
            </div>
          </v-card>
        </v-card>
      </v-col>

      <!-- Order Summary Section -->
      <v-col cols="12" md="4">
        <v-card class="pa-6 rounded-lg" style="background: rgba(255, 255, 255, 0.05);">
          <h3 class="text-h6 white--text mb-4">Order Summary</h3>

          <!-- Price Breakdown -->
          <div class="mb-6">
            <div v-if="costBfrCoupon" class="d-flex justify-space-between mb-2">
              <span class="white--text">Cart Total:</span>
              <span class="white--text">{{ currencySymbol }} {{ formatIndianCurrency(costBfrCoupon) }}</span>
            </div>
            <div v-if="discountValue" class="d-flex justify-space-between mb-2">
              <span class="white--text">Coupon Discount:</span>
              <span class="white--text">-{{ currencySymbol }} {{ formatIndianCurrency(discountValue) }}</span>
            </div>
            <div class="d-flex justify-space-between mb-2">
              <span class="white--text">Subtotal:</span>
              <span class="white--text">{{ currencySymbol }} {{ formatIndianCurrency(getSubtotal) }}</span>
            </div>
            <div v-if="getTax > 0" class="d-flex justify-space-between mb-2">
              <span class="white--text">Tax:</span>
              <span class="white--text">{{ currencySymbol }} {{ formatIndianCurrency(getTax) }}</span>
            </div>
            <div class="d-flex justify-space-between mb-2">
              <span class="white--text">Shipping:</span>
              <span class="white--text">Free</span>
            </div>
            <div v-if="selectedPaymentMethod === 'paypal'">
              <div class="d-flex justify-space-between mb-2">
                <span class="white--text">PayPal Fee (10%):</span>
                <span class="white--text">
                  {{ currencySymbol }} {{ formatIndianCurrency(getPaypalFee()) }}
                </span>
              </div>
            </div>
            <!-- Coupon Section -->
            <div class="mb-6 d-flex align-center justify-space-between flex-wrap" style="width: 100%;">
              <div class="d-flex align-center mb-2" style="width: 40%;" v-if="appliedCoupon">
                <span class="text-subtitle-1 font-weight-medium white--text">Coupon Code:</span>
              </div>
              <div v-if="couponDetail" class="ml-3 text-error text-body-2">
                {{ couponDetail }}
              </div>

              <div class="d-flex align-center justify-end" :style="{ width: appliedCoupon ? '50%' : '100%' }">
                <template v-if="appliedCoupon">
                  <v-chip class="ma-1" color="green lighten-2" text-color="white" close label style="font-weight: 500"
                    @click:close="removeCoupon" closable>
                    {{ appliedCoupon }}
                  </v-chip>
                </template>

                <template v-else>
                  <div style="display: flex; align-items: center; width: 100%;">
                    <input v-model="coupon" type="text" placeholder="Enter coupon" style="
                        border: 1px solid rgba(255, 255, 255, 0.3);
                        outline: none;
                        font-size: 14px;
                        padding: 6px 10px;
                        width: 100%;
                        background: transparent;
                        color: white;
                        border-right: none;
                        border-radius: 6px 0 0 6px;
                        height: 38px;
                      " />

                    <v-btn :disabled="coupon.length === 0" color="primary" class="text-capitalize" elevation="1"
                      @click="applyCoupon" style="
                        border-radius: 0 6px 6px 0;
                        min-width: 100px;
                        height: 38px;
                      ">
                      Apply
                    </v-btn>
                  </div>
                </template>
              </div>
            </div>

            <v-divider class="my-4" dark></v-divider>
            <div class="d-flex justify-space-between">
              <span class="text-h6 white--text">Total:</span>
              <span class="text-h6 white--text">{{ currencySymbol }} {{ formatIndianCurrency(getTotal()) }}</span>
            </div>
          </div>

          <!-- Payment Methods -->
          <v-container fluid>
            <div v-if="errors.pincode !== ''">{{ errors.pincode }}</div>
            <div style="display: flex; flex-direction: column; gap: 10px">
              <div v-if="selectedCurrency === 'USD'" style="display: flex; flex-direction: column; gap: 10px">
                <p>Select Payment Method:</p>
                <label style="display: flex; gap: 8px">
                  <input type="radio" v-model="selectedPaymentMethod" value="paypal"
                    @change="validateAndSubmit('paypal')" :disabled="loadingForPayment"/>
                  <span style="display: flex; align-items: center; gap: 8px">
                    <PaypalSvg />
                    Paypal<small style="color: white; font-size:0.8rem;">(10% extra fees)</small>
                  </span>
                </label>
                <label style="display: flex; gap: 8px">
                  <input type="radio" v-model="selectedPaymentMethod" value="payglocal" :disabled="loadingForPayment"/>
                  <span style="display: flex; align-items: center; gap: 7px">
                    <PglocalSvg />
                    International Credit/Debit Cards
                  </span>
                </label>
              </div>

              <div v-if="loadingForPayment && selectedPaymentMethod === 'paypal' && !PayPalButtonRendered">
                <div v-if="loadingForPayment && !PayPalButtonRendered"
                  style="text-align: center; padding: 6px; background-color: white; color: black;">
                    Loading...
                </div>
              </div>
              

              <v-btn v-if="selectedPaymentMethod === 'payglocal'" @click="validateAndSubmit('payglocal')" color="white"
                class="border" style="border-radius: 0; padding: 0; min-width: 0; height: auto;">
                <img src="@/assets/payglocal-logo.png" alt="PayGlocal" style="height: 32.5px; object-fit: contain;" />
              </v-btn>

            </div>
            <v-btn v-if="selectedCurrency === 'INR' && !PhonepeValue" color="white" class="m-2"
              @click="validateAndSubmit('phonepe')" style="display: flex;
                align-items: center;
                justify-content: center;
                padding: 10px 20px;
                background-color: white;
                color: black;
                font-weight: bold;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
                width: 100%;">
              <span style="display: flex; align-items: center; gap: 8px">
                <PhonepeSvg />
                PHONEPE
              </span>
            </v-btn>
            <div id="paypal-button-container" v-show="selectedPaymentMethod === 'paypal'" style="margin-top: 10px;"></div>
          </v-container>
        </v-card>
      </v-col>
    </v-row>

    <!-- Address Edit Dialog -->
    <v-dialog v-model="editAdress" max-width="600px">
      <v-card class="elevation-12 black pa-6">
        <v-card-title class="d-flex justify-space-between align-center mb-4">
          <span class="text-h5">Edit Address</span>
          <v-btn icon @click="editAdress = false" class="white--text">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <form @submit.prevent="saveAddress">
          <div class="d-flex mb-4">
            <div class="mr-2 flex-grow-1">
              <input v-model="tempAdd.first_name" type="text"
                :class="['dark-input', { 'error-input': errors.first_name }]" placeholder="First Name" required />
              <div v-if="errors.first_name" class="error-message">
                {{ errors.first_name }}
              </div>
            </div>
            <div class="ml-2 flex-grow-1">
              <input v-model="tempAdd.last_name" type="text"
                :class="['dark-input', { 'error-input': errors.last_name }]" placeholder="Last Name" required />
              <div v-if="errors.last_name" class="error-message">
                {{ errors.last_name }}
              </div>
            </div>
          </div>
          <div class="mb-4">
            <input v-model="tempAdd.phone" type="tel" :class="['dark-input', { 'error-input': errors.phone }]"
              placeholder="Phone Number" required />
            <div v-if="errors.phone" class="error-message">
              {{ errors.phone }}
            </div>
          </div>
          <div class="mb-4">
            <input v-model="tempAdd.address_line_one" type="text" :class="[
              'dark-input',
              { 'error-input': errors.address_line_one },
            ]" placeholder="Address line 1" required />
            <div v-if="errors.address_line_one" class="error-message">
              {{ errors.address_line_one }}
            </div>
          </div>
          <input v-model="tempAdd.address_line_two" type="text" class="dark-input mb-4" placeholder="Address line 2" />
          <input v-model="tempAdd.address_line_three" type="text" class="dark-input mb-4"
            placeholder="Address line 3" />
          <div class="d-flex mb-4">
            <div class="mr-2 flex-grow-1">
              <input v-model="tempAdd.city" type="text" :class="['dark-input', { 'error-input': errors.city }]"
                placeholder="City" required />
              <div v-if="errors.city" class="error-message">
                {{ errors.city }}
              </div>
            </div>
            <div class="ml-2 flex-grow-1">
              <input v-model="tempAdd.pincode" type="text" :class="['dark-input', { 'error-input': errors.pincode }]"
                placeholder="Pincode" required />
              <div v-if="errors.pincode" class="error-message">
                {{ errors.pincode }}
              </div>
            </div>
          </div>
          <div class="d-flex mb-4">
            <div class="mr-2 flex-grow-1">
              <input v-model="tempAdd.state" type="text" :class="['dark-input', { 'error-input': errors.state }]"
                placeholder="State" required />
              <div v-if="errors.state" class="error-message">
                {{ errors.state }}
              </div>
            </div>
            <div class="ml-2 flex-grow-1">
              <input v-model="tempAdd.country" type="text" :class="['dark-input', { 'error-input': errors.country }]"
                placeholder="Country" required />
              <div v-if="errors.country" class="error-message">
                {{ errors.country }}
              </div>
            </div>
          </div>
          <div class="mb-4">
            <input v-model="tempAdd.email" type="email" :class="['dark-input', { 'error-input': errors.email }]"
              placeholder="Email" required />
            <div v-if="errors.email" class="error-message">
              {{ errors.email }}
            </div>
          </div>
          <v-btn type="submit" class="white--text" color="primary" block>
            Save Address
          </v-btn>
        </form>

      </v-card>
    </v-dialog>

    <!-- Address Selection Dialog -->
    <v-dialog v-model="showAddressPopup" max-width="600px">
      <v-card class="elevation-12 pa-6">
        <ManageAddress />
        <v-btn @click="handleSaveAddress" class="white--text" color="primary" block>
          Save
        </v-btn>
      </v-card>
    </v-dialog>

    <!-- Calendly Confirmation Dialog -->
    <v-dialog v-model="popupShowConfirm" v-if="scheduling_URL" max-width="500">
      <v-card class="pa-6">
        <v-toolbar dark color="transparent">
          <v-btn icon dark @click="popupShowConfirm = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="text-p text-center">
          You will be redirected to Calendly, please fill in the required details and submit. After submitting your
          details, you will be redirected to Artisteverse to complete your payment.
          <br /><br />
          Your slot will be confirmed only after the payment is successfully processed on Artisteverse.
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn variant="flat" text="OK" @click="closeDialog" color="surface-variant"></v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Calendly Popup -->
    <v-dialog v-model="popupShow" transition="dialog-bottom-transition" fullscreen v-if="scheduling_URL">
      <v-toolbar dark color="transparent">
        <v-btn icon dark @click="toggle">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card title="Schedule The Meeting">
        <CalendlyPopup :url="scheduling_URL" />
      </v-card>
    </v-dialog>

    <Snackbar ref="snackbarRef" />
  </v-container>
</template>

<script setup>
import { ref, onBeforeMount, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { formatIndianCurrency, getOrderType } from "@/helper/common";
import {
  cancelOrder,
  cartSingle,
  orderNowCoursePaypal,
  orderNowinitMeetPaypal,
  orderNowVideoPaypal,
  singleOrderPlace,
} from "@/services/productService";
import { getUserAddress } from "@/services/userService";
import { cancelVideoBuy, } from "@/services/paidVideoService";
import { cancelCourseBuy } from "@/services/courseService";
import { cancelMeetBook, } from "@/services/saleAssistService";
import CalendlyPopup from "@/pages/calendlyPopup.vue";
import { startCalendlyCheckout } from "@/services/calendly";
import Snackbar from "@/components/Snackbar.vue";
import { useAppStore } from "@/stores/app";
import ManageAddress from "@/pages/profile/manageAddress/manageAddress.vue";
import PglocalSvg from "@/assets/PayglocalSvg.vue";
import PaypalSvg from "@/assets/PaypalSvg.vue";
import PhonepeSvg from "@/assets/PhonePeSvg.vue";

const appStore = useAppStore();
const snackbarRef = ref(null);
const router = useRouter();
const selectedPaymentMethod = ref("")
const route = useRoute();
const editAdress = ref(false);
let isLoading = ref(true);
let address = ref({
  first_name: "",
  last_name: "",
  country: "",
  address_line_one: "",
  address_line_two: "",
  address_line_three: "",
  city: "",
  state: "",
  pincode: "",
  phone: "",
  email: "",
});

let tempAdd = ref(JSON.parse(JSON.stringify(address.value)));

const openEditDialog = (address) => {
  tempAdd.value = { ...address }
  editAdress.value = true
}

const respaddress = ref(null);
let costBfrCoupon = ref(null);
let discountValue = ref(null);
const selectedCurrency = appStore.currencyCode;
const PhonepeValue = ref(false);
const loadingForPayment = ref(false);
const PaypalValue = ref(false);

const orderCache = {
  response: null,
  paymentMethod: null,
};

const saveAddress = () => {
  if (validateForm(tempAdd)) {
    address.value = { ...tempAdd.value };
    editAdress.value = false;
  }
};

const processOrderResponse = (response, paymentMethod) => {
  const { selectedPayment } = response.data;

  orderId = selectedPayment.id;
  mTxnId = selectedPayment.order_id;

  switch (paymentMethod) {
    case "phonepe":
      isLoading.value = false;
      openPayment(selectedPayment.url);
      break;
    case "paypal":
      if (!PayPalButtonRendered.value) {
        setupPayPalButton(selectedPayment.order_id);
        payPalButtonRenderSet();
      }
      break;
    case "payglocal":
      openPaymentForPayGlocal(selectedPayment.url);
      break;
    default:
      console.error(`Unknown payment method: ${paymentMethod}`);
  }
};

const showAddressPopup = ref(false)

function handleSaveAddress() {
  showAddressPopup.value = false
  setTimeout(() => {
    window.location.reload()
  }, 300)
}

let currencySymbol = ref("₹");
let tax = ref(0);
let paymentDeclined = ref(false);

let orderId = null;
let mTxnId = null;
let singleProduct = ref(null);
let errors = ref({});
let items = ref(null);
const couponDetail = ref("");
let scheduling_URL = ref("");
let popupShow = ref(false);
let popupShowConfirm = ref(false);
const PayPalButtonRendered = ref(false);
const isAddressFormReady = ref(false);

const payPalButtonRenderSet = () => {
  PayPalButtonRendered.value = true;
}

const closeDialog = () => {
  popupShowConfirm.value = false;
  toggle();
};

const toggle = async () => {
  let response;
  if (isAddressChanged.value) {

    response = startCalendlyCheckout({
      address: address.value,
      "artistMeetId": singleProduct.value.artistMeetId,
      "currency": selectedCurrency === "INR" ? 1 : 2,
      "payment_method": paymentMethodNum[selectedCurrency === "INR" ? "phonepe" : "payglocal"],
      "email_id": address.value.email,
      "coupon": appliedCoupon.value,
    })
  } else {
    response = startCalendlyCheckout({
      address_id: address.value.address_id,
      "artistMeetId": singleProduct.value.artistMeetId,
      "currency": selectedCurrency === "INR" ? 1 : 2,
      "payment_method": paymentMethodNum[selectedCurrency === "INR" ? "phonepe" : "payglocal"],
      "email_id": address.value.email,
      "coupon": appliedCoupon.value,
    })
  }

  if (response) {
    popupShow.value = !popupShow.value;
  }
};

function callback(response) {
  if (response === "USER_CANCEL") {
    paymentDeclined.value = true;
    orderCancel();
    return;
  } else if (response === "CONCLUDED") {
    router.push({
      path: `/orderDetail/${orderId}`,
      query: {
        order_type: getOrderType(singleProduct.value.type),
      },
    });
    return;
  }
}

function callbackForPayglocal(response) {
  if (response.status === "CUSTOMER_CANCELLED") {
    paymentDeclined.value = true;
    orderCancel();
    return;
  } else if (response.status === "SENT_FOR_CAPTURE") {
    router.push({
      path: `/orderDetail/${orderId}`,
      query: {
        order_type: getOrderType(singleProduct.value.type),
      },
    });
    return;
  }
}
const coupon = ref("");
const appliedCoupon = ref(null);

const properid = () => {
  if (singleProduct.value.type == "2") {
    return singleProduct.value.artistMeetId;
  } else if (singleProduct.value.type == "1") {
    return singleProduct.value.productId;
  } else if (singleProduct.value.type == "4") {
    return singleProduct.value.artistMeetId;
  } else if (singleProduct.value.type == "3") {
    return singleProduct.value.artistMeetId;
  }
};
const applyCoupon = async () => {
  isLoading.value = true;
  if (coupon.value !== "") {
    appliedCoupon.value = coupon.value.toUpperCase();
    coupon.value = "";

    try {
      const resp = await cartSingle(
        selectedCurrency === "USD" ? "currency=2" : "currency=1",
        `coupon=${appliedCoupon.value}`,
        `productItemId=${properid()}`,
        `type=${singleProduct.value.type}`
      );

      if (resp.length > 0) {
        items.value = resp;
        couponDetail.value = "";
        isLoading.value = false;
        orderCache.response = null;
        orderCache.paymentMethod = null;
        selectedPaymentMethod.value = "";
        orderId = null
        mTxnId = null
        PayPalButtonRendered.value = false;

      } else {
        items.value = null;
        couponDetail.value = "Invalid Coupon";
        appliedCoupon.value = null;
        isLoading.value = false;
        setTimeout(() => {
        snackbarRef?.value?.showSnackbar(
          `Invalid Coupon`,
          "red"
        );
      }, 0);
      }
    } catch (error) {
      console.error("error while applying coupon", error);
      items.value = null;
      couponDetail.value = "Invalid Coupon";
      appliedCoupon.value = null;
      setTimeout(() => {
        snackbarRef?.value?.showSnackbar(
          `error in applying coupon "${error?.message} || error"`,
          "red"
        );
      }, 0);
      isLoading.value = false;
    }
  }
};


const removeCoupon = () => {
  appliedCoupon.value = null;
  costBfrCoupon.value = null;
  discountValue.value = null;
  items.value = null;
  selectedPaymentMethod.value = "";
  orderCache.response = null;
  orderCache.paymentMethod = null;
  orderId = null
  mTxnId = null
  PayPalButtonRendered.value = false;
};

const orderCancel = () => {
  if (singleProduct.value.type == 1) {
    cancelOrder({
      mTxnId,
    });
    return;
  }

  if (singleProduct.value.type == 2) {
    cancelMeetBook({
      mTxnId,
    });
    return;
  }

  if (singleProduct.value.type == 3) {
    cancelCourseBuy({
      mTxnId,
    });
    return;
  }

  if (singleProduct.value.type == 4) {
    cancelVideoBuy({
      mTxnId,
    });
    return;
  }
};

const openPayment = (tokenUrl) => {
  try {
    window.PhonePeCheckout.transact({
      tokenUrl,
      callback,
      type: "IFRAME",
    });
  } catch (error) {
    console.error("Error in opening payment", error);
  }
};

const openPaymentForPayGlocal = (tokenUrl) => {
  try {
    window.PGPay.launchPayment({
      redirectUrl: tokenUrl
    },
      callbackForPayglocal
    );
  } catch (error) {
    console.error("Error in opening payment", error);
  }
};

function callbackPaypal(response) {
  if (response === "USER_CANCEL") {
    paymentDeclined.value = true;
    cancelOrder({
      mTxnId,
    });
    return;
  } else if (response === "successfully") {
    router.push({
      path: `/orderDetail/${orderId}`,
      query: {
        order_type: getOrderType(singleProduct.value.type),
      },
    });
    return;
  }
}

const setupPayPalButton = (orderID) => {
  if (!window.paypal) {
    console.error("PayPal SDK not loaded");
    return;
  }

  window.paypal
    .Buttons({
      style: {
        shape: "rect",
        borderRadius: 0,
        color: "white",
        layout: "vertical",
        label: "paypal"
      },

      createOrder: () => {
        return orderID;
      },
      onApprove: async (data, actions) => {
        const order = await actions.order.capture();
        console.log("Order successfully processed:", order);
        callbackPaypal("successfully");
      },
      onCancel: (data) => {
        console.log("Order cancelled:", data);
        callbackPaypal("USER_CANCEL");
      },
      onError: (err) => {
        console.error("PayPal error:", err);
      },
    })
    .render("#paypal-button-container");
  loadingForPayment.value = false;
  PaypalValue.value = true;
};

const paymentMethodNum = {
  phonepe: 1,
  paypal: 2,
  payglocal: 3,
}


const goodsOrderPlace = async (paymentMethod) => {
  try {
    let response;
    if (orderCache.response && orderCache.paymentMethod === paymentMethod) {
      return
    }

    if (isAddressChanged.value) {
      response = await singleOrderPlace({
        address: address.value,
        productItemId: singleProduct.value.id,
        quantity: singleProduct.value.quantity,
        currency: selectedCurrency === "INR" ? 1 : 2,
        coupon: appliedCoupon.value,
        payment_method: paymentMethodNum[paymentMethod],
      });
    } else {
      response = await singleOrderPlace({
        address_id: address.value.address_id,
        productItemId: singleProduct.value.id,
        quantity: singleProduct.value.quantity,
        currency: selectedCurrency === "INR" ? 1 : 2,
        coupon: appliedCoupon.value,
        payment_method: paymentMethodNum[paymentMethod],
      });
    }

    orderCache.response = response;
    orderCache.paymentMethod = paymentMethod;
    processOrderResponse(response, paymentMethod);

    if (response && response.data) {
      if (paymentMethod === "phonepe") {
        let url = response.data.selectedPayment.url;
        orderId = response.data.selectedPayment.id;
        mTxnId = response.data.selectedPayment.order_id;
        isLoading.value = false;
        openPayment(url);
      }

      if (paymentMethod === "paypal" && !PayPalButtonRendered.value) {
        orderId = response.data.selectedPayment.id;
        mTxnId = response.data.selectedPayment.order_id
        setupPayPalButton(response.data.selectedPayment.order_id);
        payPalButtonRenderSet();
      }

      if (paymentMethod === "payglocal") {
        let url = response.data.selectedPayment.url;
        orderId = response.data.selectedPayment.id;
        mTxnId = response.data.selectedPayment.order_id;
        isLoading.value = false;
        openPaymentForPayGlocal(url);
      }
    } else {
      console.error("Invalid API response from singleOrderPlace");
      snackbarRef?.value?.showSnackbar(
        `Invalid API response from orderNowCourse`,
        "red"
      );
    }
  } catch (error) {
    console.error("Error in singleOrderPlace API", error);

     setTimeout(() => {
    snackbarRef?.value?.showSnackbar(
      `error in placing goods order "${error?.message}"`,
      "red"
    );}, 0);
  }
}


const paidVideoOrderPlace = async (paymentMethod) => {
  try {
    let response;
    if (orderCache.response && orderCache.paymentMethod === paymentMethod) {
      return
    }

    if (isAddressChanged.value) {
      response = await orderNowVideoPaypal({
        videoId: singleProduct.value.id,
        address: address.value,
        currency: selectedCurrency === "INR" ? 1 : 2,
        coupon: appliedCoupon.value,
        payment_method: paymentMethodNum[paymentMethod],
      });
    } else {
      response = await orderNowVideoPaypal({
        videoId: singleProduct.value.id,
        address_id: address.value.address_id,
        currency: selectedCurrency === "INR" ? 1 : 2,
        coupon: appliedCoupon.value,
        payment_method: paymentMethodNum[paymentMethod],
      });
    }

    orderCache.response = response;
    orderCache.paymentMethod = paymentMethod;
    processOrderResponse(response, paymentMethod);

    if (response && response.data) {
      if (paymentMethod === "phonepe") {
        let url = response.data.selectedPayment.url;
        orderId = response.data.selectedPayment.id;
        mTxnId = response.data.selectedPayment.order_id;
        isLoading.value = false;
        openPayment(url);
      }

      if (paymentMethod === "paypal" && !PayPalButtonRendered.value) {
        orderId = response.data.selectedPayment.id;
        mTxnId = response.data.selectedPayment.order_id
        setupPayPalButton(response.data.selectedPayment.order_id);
        payPalButtonRenderSet();
      }

      if (paymentMethod === "payglocal") {
        let url = response.data.selectedPayment.url;
        orderId = response.data.selectedPayment.id;
        mTxnId = response.data.selectedPayment.order_id;
        isLoading.value = false;
        openPaymentForPayGlocal(url);
      }
    } else {
      console.error("Invalid API response from orderNowVideo");
      snackbarRef?.value.showSnackbar(
        `Invalid API response from orderNowCourse`,
        "red"
      );
    }
  } catch (error) {
    console.error("Error in orderNowVideo API", error);
     setTimeout(() => {
    snackbarRef?.value?.showSnackbar(
      `error in placing video order "${error?.message}"`,
      "red"
    );}, 0);
  }
};

const courseOrderPlace = async (paymentMethod) => {
  try {
    let response;
    if (orderCache.response && orderCache.paymentMethod === paymentMethod) {
      return
    }

    if (isAddressChanged.value) {
      response = await orderNowCoursePaypal({
        courseId: singleProduct.value.id,
        address: address.value,
        currency: selectedCurrency === "INR" ? 1 : 2,
        coupon: appliedCoupon.value,
        payment_method: paymentMethodNum[paymentMethod],
      });
    } else {
      response = await orderNowCoursePaypal({
        courseId: singleProduct.value.id,
        address_id: address.value.address_id,
        currency: selectedCurrency === "INR" ? 1 : 2,
        coupon: appliedCoupon.value,
        payment_method: paymentMethodNum[paymentMethod],
      });
    }

    orderCache.response = response;
    orderCache.paymentMethod = paymentMethod;
    processOrderResponse(response, paymentMethod);

    if (response && response.data) {
      if (paymentMethod === "phonepe") {
        let url = response.data.selectedPayment.url;
        orderId = response.data.selectedPayment.id;
        mTxnId = response.data.selectedPayment.order_id;
        isLoading.value = false;
        openPayment(url);
      }

      if (paymentMethod === "paypal" && !PayPalButtonRendered.value) {
        orderId = response.data.selectedPayment.id;
        mTxnId = response.data.selectedPayment.order_id
        setupPayPalButton(response.data.selectedPayment.order_id);
        payPalButtonRenderSet();
      }

      if (paymentMethod === "payglocal") {
        let url = response.data.selectedPayment.url;
        orderId = response.data.selectedPayment.id;
        mTxnId = response.data.selectedPayment.order_id;
        isLoading.value = false;
        openPaymentForPayGlocal(url);
      }
    } else {
      console.error("Invalid API response from orderNowCourse");
      snackbarRef?.value?.showSnackbar(
        `Invalid API response from orderNowCourse`,
        "red"
      );
    }
  } catch (error) {
    console.error("Error in orderNowCourse API", error);
     setTimeout(() => {
    snackbarRef?.value?.showSnackbar(
      `error in placing course order "${error?.message}"`,
      "red"
    );}, 0);
  }

};

const meetOrderPlace = async (paymentMethod) => {
  try {
    let response;
    if (orderCache.response && orderCache.paymentMethod === paymentMethod) {
      return
    }

    if (isAddressChanged.value) {
      response = await orderNowinitMeetPaypal({
        artistMeetId: singleProduct.value.artistMeetId,
        slotTime: singleProduct.value.slotTime,
        slotDate: singleProduct.value.slotDate,
        formId: singleProduct.value.id,
        address: address.value,
        coupon: appliedCoupon.value,
        currency: selectedCurrency === "INR" ? 1 : 2,
        payment_method: paymentMethodNum[paymentMethod],
      });
    } else {
      response = await orderNowinitMeetPaypal({
        artistMeetId: singleProduct.value.artistMeetId,
        slotTime: singleProduct.value.slotTime,
        slotDate: singleProduct.value.slotDate,
        formId: singleProduct.value.id,
        address_id: address.value.address_id,
        coupon: appliedCoupon.value,
        currency: selectedCurrency === "INR" ? 1 : 2,
        payment_method: paymentMethodNum[paymentMethod],
      });
    }

    orderCache.response = response;
    orderCache.paymentMethod = paymentMethod;
    processOrderResponse(response, paymentMethod);

    if (response && response.data) {
      if (paymentMethod === "phonepe") {
        orderId = response.data.selectedPayment.id;
        mTxnId = response.data.selectedPayment.order_id;
        const url = response.data.selectedPayment.url;
        isLoading.value = false;
        openPayment(url);
      }

      if (paymentMethod === "paypal" && !PayPalButtonRendered.value) {
        orderId = response.data.selectedPayment.id;
        mTxnId = response.data.selectedPayment.order_id
        setupPayPalButton(response.data.selectedPayment.order_id);
        payPalButtonRenderSet();
      }

      if (paymentMethod === "payglocal") {
        let url = response.data.selectedPayment.url;
        orderId = response.data.selectedPayment.id;
        mTxnId = response.data.selectedPayment.order_id;
        isLoading.value = false;
        openPaymentForPayGlocal(url);
      }
    } else {
      console.error("Invalid API response from orderNowinitMeet");
      snackbarRef?.value?.showSnackbar(
        `Invalid API response from orderNowCourse`,
        "red"
      );
    }
  } catch (error) {
    console.error("Error in orderNowinitMeet API", error);
     setTimeout(() => {
    snackbarRef?.value?.showSnackbar(
      `error in placing meet order "${error?.message}"`,
      "red"
    );}, 0);
  }
};

const getSubtotal = computed(() => {
  const priceIncludingTax =
    appliedCoupon.value && items?.value?.length > 0
      ? items?.value[0]?.price
      : singleProduct.value.price;

  const taxRate = singleProduct.value.tax;
  const taxcheck = 1 + taxRate / 100;
  const subtotal = priceIncludingTax / taxcheck;

  if (appliedCoupon.value && items.value?.[0].couponValid) {
    costBfrCoupon.value = singleProduct.value.price / taxcheck;
    discountValue.value =
      singleProduct.value.price / taxcheck - items.value[0].price / taxcheck;
  } else {
    discountValue.value = null;
  }

  return subtotal;
});

const getBaseTotal = () => {
  return items.value ? items.value
    .map(el => el.price_per_qty)
    .reduce((sum, v) => sum + v, 0) : singleProduct.value.price;
}

const getPaypalFee = () => {
  return selectedPaymentMethod.value === 'paypal'
    ? getBaseTotal() * 0.10
    : 0
}
const getTax = computed(() => {
  return (
    (appliedCoupon.value && items?.value?.length > 0
      ? items?.value[0]?.price
      : singleProduct.value.price) - getSubtotal.value
  );
});

const getTotal = () => {
  return Number(getSubtotal.value) + (selectedCurrency === "INR" ? Number(getTax.value) : 0) + + getPaypalFee()
}


const getFinTotal = computed(() => {
  if (appliedCoupon?.value && items?.value?.length > 0) {
    return items.value[0].price;
  }
  return singleProduct.value.price;
});

const fetchCart = async () => {
  if (route.query.productId) {
    singleProduct.value = {
      id: route.query.productId,
      productId: route.query.id,
      image: route.query.image,
      name: route.query.name,
      price: Number(route.query.price),
      tax: Number(route.query.tax),
      currency: route.query.currency,
      quantity: Number(route.query.quantity),
      type: Number(route.query.type),
      slotTime: route.query.slotTime,
      slotDate: route.query.slotDate,
      artistMeetId: route.params.id,
      brand_name: route.query.brand_name,
    };

    if (singleProduct.value.type == 2) {
      scheduling_URL.value = route.query.scheduledURL;
      // popupShow.value = true;
    }
    currencySymbol.value = singleProduct.value.currency;
    tax.value = singleProduct.value.tax;
    isLoading.value = false;
  }
};

const fetchAddress = async () => {
  try {
    const resp = await getUserAddress();
    if (resp.address) {
      address.value = resp.address;
      respaddress.value = structuredClone(resp.address);
      address.value.country = "India";
      respaddress.value.country = "India";
    } else {
      respaddress.value = null;
      address.value = {
        first_name: "",
        last_name: "",
        country: "",
        address_line_one: "",
        address_line_two: "",
        address_line_three: "",
        city: "",
        state: "",
        pincode: "",
        phone: "",
        email: "",
      };
    }
    isAddressFormReady.value = true;
  } catch (error) {
    console.error("Error in product detail API", error);
     setTimeout(() => {
    snackbarRef?.value?.showSnackbar(
      `error in getting address "${error?.message}"`,
      "red"
    );}, 0);
  }
};

const validateForm = (tempAdd) => {
  errors.value = {};
  let isValid = true;

  const requiredFields = [
    "first_name",
    "phone",
    "address_line_one",
    "city",
    "pincode",
    "state",
    "country",
    "email",
  ];

  for (const field of requiredFields) {
    if (!tempAdd.value[field]) {
      errors.value[field] = `${field.replace("_", " ")} is required`;
      isValid = false;
    }
  }

  // Email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (tempAdd.value.email && !emailRegex.test(tempAdd.value.email)) {
    errors.value.email = "Invalid email format";
    isValid = false;
  }

  // Phone validation for INR
  const phoneRegex = /^(\+91[\-\s]?)?[0]?(91)?\d{10}$/;
  if (
    selectedCurrency === "INR"
      ? tempAdd.value.phone && !phoneRegex.test(tempAdd.value.phone)
      : false
  ) {
    errors.value.phone = "Invalid phone number";
    isValid = false;
  }

  // Pincode validation for INR
  const pincodeRegex = /^\d{6}$/;
  if (
    selectedCurrency === "INR"
      ? tempAdd.value.pincode && !pincodeRegex.test(tempAdd.value.pincode)
      : false
  ) {
    errors.value.pincode = "Invalid pincode (should be 6 digits)";
    isValid = false;
  }

  return isValid;
};

const isAddressChanged = computed(() => {
  return JSON.stringify(respaddress.value) !== JSON.stringify(address.value);
});

const validateAndSubmit = async (paymentMethod) => {
  if (validateForm(address)) {
    loadingForPayment.value = true;

    if (singleProduct.value.type == 1) {
      await goodsOrderPlace(paymentMethod);
    }

    if (singleProduct.value.type == 2) {
      await meetOrderPlace(paymentMethod);
    }

    if (singleProduct.value.type == 3) {
      await courseOrderPlace(paymentMethod);
    }

    if (singleProduct.value.type == 4) {
      await paidVideoOrderPlace(paymentMethod);
    }
    loadingForPayment.value = false;

  } else {
    console.error("Form validation failed");
    snackbarRef?.value?.showSnackbar(
      `Form validation failed`,
      "red"
    );
  }
};

onBeforeMount(async () => {
  await fetchCart();
  await fetchAddress();
});
</script>

<style scoped>
.action-button {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: right;
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: white;
}

.dark-input {
  width: 100%;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: white;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.dark-input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.08);
}

.error-input {
  border-color: #ff5252;
}

.error-message {
  color: #ff5252;
  font-size: 12px;
  margin-top: 4px;
}

.product-card {
  transition: all 0.3s ease;
}

.product-card:hover {
  background: rgba(255, 255, 255, 0.08) !important;
  transform: translateY(-2px);
}

.payment-methods {
  margin-top: 24px;
}

.payment-methods .v-btn {
  text-transform: none;
  letter-spacing: normal;
  font-weight: 500;
  transition: all 0.3s ease;
}

.payment-methods .v-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.v-card {
  transition: all 0.3s ease;
}

.v-card:hover {
  transform: translateY(-2px);
}

input[type="radio"]{
  cursor: pointer;
}

.text-error {
  color: #ff5252;
}

/* Custom scrollbar for the page */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
</style>
