<template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular :size="70" :width="7" color="white" indeterminate></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <v-container v-else class="py-8">
    <v-row class="ma-0">
      <v-col cols="12" md="8" class="pr-md-6">
        <v-card class="mb-6 transparent  pa-4" flat>
          <h2 class="text-h5 grey--text text--lighten-2 mb-2 px-4">Your Order</h2>

          <div v-if="singleProduct" class="pa-3 rounded-lg"
            style="background-color: rgba(255, 255, 255, 0.04); border: 1px solid rgba(255, 255, 255, 0.08);">
            <v-row no-gutters>
              <v-col cols="4" class="d-flex align-center justify-center">
                <v-img :src="singleProduct.image" height="150" contain class="rounded-lg"></v-img>
              </v-col>

              <v-col cols="8" class="px-3 py-2 d-flex flex-column justify-space-between">
                <div>
                  <div class="text-body-2 font-weight-medium grey--text text--lighten-1">
                    {{ singleProduct.artist_name }}
                  </div>
                  <div class="text-body-1 font-weight-bold white--text">
                    {{ singleProduct.name }}
                  </div>
                </div>

                <div class="d-flex justify-space-between align-center mt-3">
                  <span class="text-body-2 white--text">Qty: {{ singleProduct.quantity }}</span>
                  <span class="text-body-2 white--text">
                    {{ currencySymbol }} {{ formatIndianCurrency(getTotal()) }}
                  </span>
                </div>
              </v-col>
            </v-row>
          </div>
        </v-card>


        <v-card class="mb-6 transparent" flat>
          <h3 class="text-h6 white--text mb-2 pa-4">Shipping Address</h3>
          <v-card class="pa-4 rounded-lg  ma-4 mt-0" style="background: rgba(255, 255, 255, 0.05);">
            <div class="shipping-address-container"
              style="display: flex; justify-content: space-between; flex-wrap: wrap; gap: 16px;">
              <div class="address-details" style="flex: 1 1 65%; color: white;">
                <div class="text-h6">{{ address.first_name }} {{ address.last_name }}</div>
                <div>{{ address.address_line_one }}</div>
                <div v-if="address.address_line_two">{{ address.address_line_two }}</div>
                <div v-if="address.address_line_three">{{ address.address_line_three }}</div>
                <div>
                  {{ address.city }}, {{ address.state }}, {{ address.country }} - {{ address.pincode }}
                </div>
                <div>{{ address.phone }}</div>
                <div>{{ address.email }}</div>
              </div>

              <!-- Action Buttons -->
              <div class="address-actions"
                style="display: flex; flex-direction: column; align-items: flex-end; justify-content: space-between; padding: 10px; gap: 8px;">
                <button @click="openEditDialog(address)" class="action-button">
                  Edit
                </button>
                <button @click="showAddressPopup = true" class="action-button">
                  <v-icon size="15" class="mr-2">mdi-home-map-marker</v-icon>
                  Choose Another
                </button>
              </div>
            </div>
          </v-card>
        </v-card>
      </v-col>

      <!-- Order Summary Section -->
      <v-col cols="12" md="4">
        <v-card class="pa-6 rounded-lg" style="background: rgba(255, 255, 255, 0.05);">
          <h3 class="text-h6 white--text mb-4">Order Summary</h3>

          <!-- Price Breakdown -->
          <div class="mb-6">
            <div v-if="costBfrCoupon" class="d-flex justify-space-between mb-2">
              <span class="white--text">Cart Total:</span>
              <span class="white--text">{{ currencySymbol }} {{ formatIndianCurrency(costBfrCoupon) }}</span>
            </div>
            <div v-if="discountValue" class="d-flex justify-space-between mb-2">
              <span class="white--text">Coupon Discount:</span>
              <span class="white--text">-{{ currencySymbol }} {{ formatIndianCurrency(discountValue) }}</span>
            </div>
            <div class="d-flex justify-space-between mb-2">
              <span class="white--text">Subtotal:</span>
              <span class="white--text">{{ currencySymbol }} {{ formatIndianCurrency(getSubtotal) }}</span>
            </div>
            <div v-if="getTax > 0" class="d-flex justify-space-between mb-2">
              <span class="white--text">Tax:</span>
              <span class="white--text">{{ currencySymbol }} {{ formatIndianCurrency(getTax) }}</span>
            </div>
            <div class="d-flex justify-space-between mb-2">
              <span class="white--text">Shipping:</span>
              <span class="white--text">Free</span>
            </div>
            <div v-if="selectedPaymentMethod === 'paypal'">
              <div class="d-flex justify-space-between mb-2">
                <span class="white--text">PayPal Fee (10%):</span>
                <span class="white--text">
                  {{ currencySymbol }} {{ formatIndianCurrency(getPaypalFee()) }}
                </span>
              </div>
            </div>
            <!-- Coupon Section -->
            <div class="mb-6 d-flex align-center justify-space-between flex-wrap" style="width: 100%;">
              <div class="d-flex align-center mb-2" style="width: 40%;" v-if="appliedCoupon">
                <span class="text-subtitle-1 font-weight-medium white--text" style="width: 50%;">Coupon Code:</span>
              </div>
              <div v-if="couponDetail" class="ml-3 text-error text-body-2">
                {{ couponDetail }}
              </div>

              <div class="d-flex align-center justify-end" :style="{ width: appliedCoupon ? '50%' : '100%' }">
                <template v-if="appliedCoupon">
                  <v-chip class="ma-1" color="green lighten-2" text-color="white" close label style="font-weight: 500"
                    @click:close="removeCoupon" closable>
                    {{ appliedCoupon }}
                  </v-chip>
                </template>

                <template v-else>
                  <div style="display: flex; align-items: center; width: 100%;">
                    <input v-model="coupon" type="text" placeholder="Enter coupon" style="
                        border: 1px solid rgba(255, 255, 255, 0.3);
                        outline: none;
                        font-size: 14px;
                        padding: 6px 10px;
                        width: 100%;
                        background: transparent;
                        color: white;
                        border-right: none;
                        border-radius: 6px 0 0 6px;
                        height: 38px;
                      " />

                    <v-btn :disabled="coupon.length === 0" color="primary" class="text-capitalize" elevation="1"
                      @click="applyCoupon" style="
                        border-radius: 0 6px 6px 0;
                        min-width: 100px;
                        height: 38px;
                      ">
                      Apply
                    </v-btn>
                  </div>
                </template>
              </div>
            </div>

            <v-divider class="my-4" dark></v-divider>
            <div class="d-flex justify-space-between">
              <span class="text-h6 white--text">Total:</span>
              <span class="text-h6 white--text">{{ currencySymbol }} {{ formatIndianCurrency(getFinTotal) }}</span>
            </div>
          </div>

          <!-- Payment Methods -->
          <v-container fluid>
            <div v-if="errors.pincode !== ''">{{ errors.pincode }}</div>
            <div style="display: flex; flex-direction: column; gap: 10px">
              <div v-if="selectedCurrency === 'USD'" style="display: flex; flex-direction: column; gap: 10px">
                <p>Select Payment Method:</p>
                <label style="display: flex; gap: 8px">
                  <input type="radio" v-model="selectedPaymentMethod" value="paypal"
                    @change="validateAndSubmit('paypal')" />
                  <span style="display: flex; align-items: center; gap: 8px">
                    <svg width="40" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="24" cy="24" r="20" fill="#0070BA" />
                      <path
                        d="M32.3305 18.0977C32.3082 18.24 32.2828 18.3856 32.2542 18.5351C31.2704 23.5861 27.9046 25.331 23.606 25.331H21.4173C20.8916 25.331 20.4486 25.7127 20.3667 26.2313L19.2461 33.3381L18.9288 35.3527C18.8755 35.693 19.1379 36 19.4815 36H23.3634C23.8231 36 24.2136 35.666 24.286 35.2127L24.3241 35.0154L25.055 30.3772L25.1019 30.1227C25.1735 29.6678 25.5648 29.3338 26.0245 29.3338H26.6051C30.3661 29.3338 33.3103 27.8068 34.1708 23.388C34.5303 21.5421 34.3442 20.0008 33.393 18.9168C33.1051 18.59 32.748 18.3188 32.3305 18.0977Z"
                        fill="white" fill-opacity="0.6" />
                      <path
                        d="M31.3009 17.6871C31.1506 17.6434 30.9955 17.6036 30.8364 17.5678C30.6766 17.5328 30.5127 17.5018 30.3441 17.4748C29.754 17.3793 29.1074 17.334 28.4147 17.334H22.5676C22.4237 17.334 22.2869 17.3666 22.1644 17.4254C21.8948 17.5551 21.6944 17.8104 21.6459 18.1229L20.402 26.0013L20.3662 26.2311C20.4481 25.7126 20.8911 25.3308 21.4168 25.3308H23.6055C27.9041 25.3308 31.2699 23.5851 32.2537 18.5349C32.2831 18.3854 32.3078 18.2398 32.33 18.0975C32.0811 17.9655 31.8115 17.8525 31.5212 17.7563C31.4496 17.7324 31.3757 17.7094 31.3009 17.6871Z"
                        fill="white" fill-opacity="0.8" />
                      <path
                        d="M21.6461 18.1231C21.6946 17.8105 21.895 17.5552 22.1646 17.4264C22.2879 17.3675 22.4239 17.3349 22.5678 17.3349H28.4149C29.1077 17.3349 29.7542 17.3803 30.3444 17.4757C30.513 17.5027 30.6768 17.5338 30.8367 17.5687C30.9957 17.6045 31.1508 17.6443 31.3011 17.688C31.3759 17.7103 31.4498 17.7334 31.5222 17.7564C31.8125 17.8527 32.0821 17.9664 32.331 18.0976C32.6237 16.231 32.3287 14.9601 31.3194 13.8093C30.2068 12.5424 28.1986 12 25.629 12H18.169C17.6441 12 17.1963 12.3817 17.1152 12.9011L14.0079 32.5969C13.9467 32.9866 14.2473 33.3381 14.6402 33.3381H19.2458L20.4022 26.0014L21.6461 18.1231Z"
                        fill="white" />
                    </svg>
                    Paypal<small style="color: white; font-size:0.8rem;">(10% extra fees)</small>
                  </span>
                </label>
                <label style="display: flex; gap: 8px">
                  <input type="radio" v-model="selectedPaymentMethod" value="payglocal" />
                  <span style="display: flex; align-items: center; gap: 7px">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40"
                      viewBox="0 0 300 200">
                      <image
                        xlink:href="data:image/png;base64,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"
                        x="0" y="0" width="200" height="200" />
                    </svg>
                    International Credit/Debit Cards
                  </span>
                </label>
              </div>

              <div v-if="loadingForPayment && selectedPaymentMethod === 'paypal' && !PayPalButtonRendered">
                <div v-if="loadingForPayment && !PayPalButtonRendered"
                  style="text-align: center; padding: 20px; background-color: white; color: black;">
                  processing...
                </div>
              </div>

              <v-btn v-if="selectedPaymentMethod === 'payglocal'" @click="validateAndSubmit('payglocal')" color="white"
                class="border" style="border-radius: 0; padding: 0; min-width: 0; height: auto;">
                <img src="@/assets/payglocal-logo.png" alt="PayGlocal" style="height: 32.5px; object-fit: contain;" />
              </v-btn>

            </div>
            <v-btn v-if="selectedCurrency === 'INR' && !PhonepeValue" color="white" class="m-2"
              @click="validateAndSubmit('phonepe')" style="display: flex;
                align-items: center;
                justify-content: center;
                padding: 10px 20px;
                background-color: white;
                color: black;
                font-weight: bold;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
                width: 100%;">
              <span style="display: flex; align-items: center; gap: 8px">
                <svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" clip-rule="evenodd"
                  stroke-linejoin="round" stroke-miterlimit="2" style="width: 24px; height: 24px">
                  <circle cx="-25.926" cy="41.954" r="29.873" fill="#5f259f"
                    transform="rotate(-76.714 -48.435 5.641) scale(8.56802)" />
                  <path
                    d="M372.164 189.203c0-10.008-8.576-18.593-18.584-18.593h-34.323l-78.638-90.084c-7.154-8.577-18.592-11.439-30.03-8.577l-27.17 8.577c-4.292 1.43-5.723 7.154-2.862 10.007l85.8 81.508H136.236c-4.293 0-7.154 2.861-7.154 7.154v14.292c0 10.016 8.585 18.592 18.592 18.592h20.015v68.639c0 51.476 27.17 81.499 72.931 81.499 14.292 0 25.739-1.431 40.03-7.146v45.753c0 12.87 10.016 22.886 22.885 22.886h20.015c4.293 0 8.577-4.293 8.577-8.586V210.648h32.893c4.292 0 7.145-2.861 7.145-7.145v-14.3zM280.65 312.17c-8.576 4.292-20.015 5.723-28.591 5.723-22.886 0-34.324-11.438-34.324-37.176v-68.639h62.915v100.092z"
                    fill="#fff" fill-rule="nonzero" />
                </svg>
                PHONEPE
              </span>
            </v-btn>
            <div id="paypal-button-container" v-show="selectedPaymentMethod === 'paypal'"></div>
          </v-container>
        </v-card>
      </v-col>
    </v-row>

    <!-- Address Edit Dialog -->
    <v-dialog v-model="editAdress" max-width="600px">
      <v-card class="elevation-12 black pa-6">
        <v-card-title class="d-flex justify-space-between align-center mb-4">
          <span class="text-h5">Edit Address</span>
          <v-btn icon @click="editAdress = false" class="white--text">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <form @submit.prevent="saveAddress">
          <div class="d-flex mb-4">
            <div class="mr-2 flex-grow-1">
              <input v-model="tempAdd.first_name" type="text"
                :class="['dark-input', { 'error-input': errors.first_name }]" placeholder="First Name" required />
              <div v-if="errors.first_name" class="error-message">
                {{ errors.first_name }}
              </div>
            </div>
            <div class="ml-2 flex-grow-1">
              <input v-model="tempAdd.last_name" type="text"
                :class="['dark-input', { 'error-input': errors.last_name }]" placeholder="Last Name" required />
              <div v-if="errors.last_name" class="error-message">
                {{ errors.last_name }}
              </div>
            </div>
          </div>
          <div class="mb-4">
            <input v-model="tempAdd.phone" type="tel" :class="['dark-input', { 'error-input': errors.phone }]"
              placeholder="Phone Number" required />
            <div v-if="errors.phone" class="error-message">
              {{ errors.phone }}
            </div>
          </div>
          <div class="mb-4">
            <input v-model="tempAdd.address_line_one" type="text" :class="[
              'dark-input',
              { 'error-input': errors.address_line_one },
            ]" placeholder="Address line 1" required />
            <div v-if="errors.address_line_one" class="error-message">
              {{ errors.address_line_one }}
            </div>
          </div>
          <input v-model="tempAdd.address_line_two" type="text" class="dark-input mb-4" placeholder="Address line 2" />
          <input v-model="tempAdd.address_line_three" type="text" class="dark-input mb-4"
            placeholder="Address line 3" />
          <div class="d-flex mb-4">
            <div class="mr-2 flex-grow-1">
              <input v-model="tempAdd.city" type="text" :class="['dark-input', { 'error-input': errors.city }]"
                placeholder="City" required />
              <div v-if="errors.city" class="error-message">
                {{ errors.city }}
              </div>
            </div>
            <div class="ml-2 flex-grow-1">
              <input v-model="tempAdd.pincode" type="text" :class="['dark-input', { 'error-input': errors.pincode }]"
                placeholder="Pincode" required />
              <div v-if="errors.pincode" class="error-message">
                {{ errors.pincode }}
              </div>
            </div>
          </div>
          <div class="d-flex mb-4">
            <div class="mr-2 flex-grow-1">
              <input v-model="tempAdd.state" type="text" :class="['dark-input', { 'error-input': errors.state }]"
                placeholder="State" required />
              <div v-if="errors.state" class="error-message">
                {{ errors.state }}
              </div>
            </div>
            <div class="ml-2 flex-grow-1">
              <input v-model="tempAdd.country" type="text" :class="['dark-input', { 'error-input': errors.country }]"
                placeholder="Country" required />
              <div v-if="errors.country" class="error-message">
                {{ errors.country }}
              </div>
            </div>
          </div>
          <div class="mb-4">
            <input v-model="tempAdd.email" type="email" :class="['dark-input', { 'error-input': errors.email }]"
              placeholder="Email" required />
            <div v-if="errors.email" class="error-message">
              {{ errors.email }}
            </div>
          </div>
          <v-btn type="submit" class="white--text" color="primary" block>
            Save Address
          </v-btn>
        </form>

      </v-card>
    </v-dialog>

    <!-- Address Selection Dialog -->
    <v-dialog v-model="showAddressPopup" max-width="800px">
      <v-card class="elevation-12 pa-6">
        <ManageAddress />
        <v-btn @click="handleSaveAddress" class="white--text" color="primary" block>
          Save
        </v-btn>
      </v-card>
    </v-dialog>

    <!-- Calendly Confirmation Dialog -->
    <v-dialog v-model="popupShowConfirm" v-if="scheduling_URL" max-width="500">
      <v-card class="pa-6">
        <v-toolbar dark color="transparent">
          <v-btn icon dark @click="popupShowConfirm = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="text-p text-center">
          You will be redirected to Calendly, please fill in the required details and submit. After submitting your
          details, you will be redirected to Artisteverse to complete your payment.
          <br /><br />
          Your slot will be confirmed only after the payment is successfully processed on Artisteverse.
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn variant="flat" text="OK" @click="closeDialog" color="surface-variant"></v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Calendly Popup -->
    <v-dialog v-model="popupShow" transition="dialog-bottom-transition" fullscreen v-if="scheduling_URL">
      <v-toolbar dark color="transparent">
        <v-btn icon dark @click="toggle">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card title="Schedule The Meeting">
        <CalendlyPopup :url="scheduling_URL" />
      </v-card>
    </v-dialog>

    <Snackbar ref="snackbarRef" />
  </v-container>
</template>

<script setup>
import { ref, onBeforeMount, computed, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getOrderType, formatIndianCurrency } from "@/helper/common";
import { cartSingle, orderNowinitMeetPaypal } from "@/services/productService";
import { getUserAddress } from "@/services/userService";
// import { cancelMeetBook } from "@/services/saleAssistService";
import Snackbar from "@/components/Snackbar.vue";
import { useAppStore } from "@/stores/app";
import ManageAddress from "@/pages/profile/manageAddress/manageAddress.vue";

const appStore = useAppStore();
const snackbarRef = ref(null);
const router = useRouter();
const route = useRoute();
let isLoading = ref(true);
let address = ref({
  first_name: "",
  last_name: "",
  country: "",
  address_line_one: "",
  address_line_two: "",
  address_line_three: "",
  city: "",
  state: "",
  pincode: "",
  phone: "",
  email: "",
});
const respaddress = ref(null);
const selectedCurrency = appStore.currencyCode;
let singleProduct = ref(null);
let currencySymbol = ref("₹");
let tax = ref(0);
const appliedCoupon = ref(null);
let errors = ref({});
let costBfrCoupon = ref(null);
let discountValue = ref(null);
let paymentDeclined = ref(false);
let items = ref(null);
const couponDetail = ref("");
const coupon = ref("");
const selectedPaymentMethod = ref("")
const PhonepeValue = ref(false);
// const PaypalValue = ref(false);
const loadingForPayment = ref(false);
const PayPalButtonRendered = ref(false);
const isAddressFormReady = ref(false);

let orderId = null;
let mTxnId = null;
const showAddressPopup = ref(false)
const editAdress = ref(false);

function handleSaveAddress() {
  showAddressPopup.value = false
  setTimeout(() => {
    window.location.reload()
  }, 300)
}

const orderCache = {
  response: null,
  paymentMethod: null,
};

const payPalButtonRenderSet = () => {
  PayPalButtonRendered.value = true;
}

let tempAdd = ref(JSON.parse(JSON.stringify(address.value)));

const openEditDialog = (address) => {
  tempAdd.value = { ...address }
  editAdress.value = true
}

const validateForm = (tempAdd) => {
  errors.value = {};
  let isValid = true;

  const requiredFields = [
    "first_name",
    "phone",
    "address_line_one",
    "city",
    "pincode",
    "state",
    "country",
    "email",
  ];

  for (const field of requiredFields) {
    if (!tempAdd.value[field]) {
      errors.value[field] = `${field.replace("_", " ")} is required`;
      isValid = false;
    }
  }

  // Additional validation for email
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (tempAdd.value.email && !emailRegex.test(tempAdd.value.email)) {
    errors.value.email = "Invalid email format";
    isValid = false;
  }

  // Additional validation for phone (example: 10 digits)
  const phoneRegex = /^(\+91[\-\s]?)?[0]?(91)?[789]\d{9}$/;
  if (
    selectedCurrency === "INR"
      ? tempAdd.value.phone && !phoneRegex.test(tempAdd.value.phone)
      : false
  ) {
    errors.value.phone = "Invalid phone number";
    isValid = false;
  }

  // Additional validation for pincode (example: 6 digits)
  const pincodeRegex = /^\d{6}$/;
  if (
    selectedCurrency === "INR"
      ? tempAdd.value.pincode && !pincodeRegex.test(tempAdd.value.pincode)
      : false
  ) {
    errors.value.pincode = "Invalid pincode (should be 6 digits)";
    isValid = false;
  }

  return isValid;
};


const saveAddress = () => {
  if (validateForm(tempAdd)) {
    address.value = { ...tempAdd.value };
    editAdress.value = false;
  }
};

const processOrderResponse = (response, paymentMethod) => {
  const { selectedPayment } = response.data;

  orderId = selectedPayment.id;
  mTxnId = selectedPayment.order_id;

  switch (paymentMethod) {
    case "phonepe":
      isLoading.value = false;
      openPayment(selectedPayment.url);
      break;
    case "paypal":
      if (!PayPalButtonRendered.value) {
        setupPayPalButton(selectedPayment.order_id);
        payPalButtonRenderSet();
      }
      break;
    case "payglocal":
      openPaymentForPayGlocal(selectedPayment.url);
      break;
    default:
      console.error(`Unknown payment method: ${paymentMethod}`);
  }
};

const isAddressChanged = computed(() => {
  return JSON.stringify(respaddress.value) !== JSON.stringify(address.value);
});

// const getSubtotal = computed(() => {
//   const priceIncludingTax =
//     appliedCoupon.value && items?.value?.length > 0
//       ? items?.value[0]?.price
//       : singleProduct.value.price;

//   const taxRate = singleProduct.value.tax;
//   const taxcheck = 1 + taxRate / 100;

//   const subtotal = priceIncludingTax / taxcheck;

//   if (appliedCoupon.value && items?.value[0]?.couponValid) {
//     costBfrCoupon.value = singleProduct.value.price / taxcheck;
//     discountValue.value =
//       singleProduct.value.price / taxcheck - items.value[0].price / taxcheck;
//   } else {
//     discountValue.value = null;
//   }
//   return subtotal;
// });

const getSubtotal = computed(() => {
  const priceIncludingTax =
    appliedCoupon.value && items?.value?.length > 0
      ? items?.value[0]?.price
      : singleProduct.value.price;

  const taxRate = singleProduct.value.tax;
  const taxcheck = 1 + taxRate / 100;
  const subtotal = priceIncludingTax / taxcheck;

  if (appliedCoupon.value && items.value?.[0].couponValid) {
    costBfrCoupon.value = singleProduct.value.price / taxcheck;
    discountValue.value =
      singleProduct.value.price / taxcheck - items.value[0].price / taxcheck;
  } else {
    discountValue.value = null;
  }

  return subtotal;
});


const getBaseTotal = () => {
  return items.value
    .map(el => el.price_per_qty * el.cart_quantity)
    .reduce((sum, v) => sum + v, 0)
}

const getPaypalFee = () => {
  return selectedPaymentMethod.value === 'paypal'
    ? getBaseTotal() * 0.10
    : 0
}

const getTax = computed(() => {
  return (
    (appliedCoupon.value && items?.value?.length > 0
      ? items?.value[0]?.price
      : singleProduct.value.price) - getSubtotal.value
  );
});

const getTotal = () => {
  return getSubtotal.value + getPaypalFee()
}


const getFinTotal = computed(() => {
  if (appliedCoupon?.value && items?.value?.length > 0) {
    return items.value[0].price;
  }
  return singleProduct.value.price;
});

onMounted(async () => {
  const unwatch = watch(isAddressFormReady, async (ready) => {
    if (ready && selectedCurrency === "USD") {
      await validateAndSubmit('paypal');
      unwatch();
    }
  });
});

function callback(response) {
  if (response === "USER_CANCEL") {
    paymentDeclined.value = true;
    return;
  } else if (response === "CONCLUDED") {
    router.push({
      path: `/orderDetail/${orderId}`,
      query: {
        order_type: getOrderType(2),
      },
    });
    return;
  }
}

function callbackForPayglocal(response) {
  if (response.status === "CUSTOMER_CANCELLED") {
    paymentDeclined.value = true;
    return;
  } else if (response.status === "SENT_FOR_CAPTURE") {
    router.push({
      path: `/orderDetail/${orderId}`,
      query: {
        order_type: getOrderType(2),
      },
    });
    return;
  }
}

const properid = () => {
  if (singleProduct.value.type == "2") {
    return singleProduct.value.artistMeetId;
  } else if (singleProduct.value.type == "1") {
    return singleProduct.value.productId;
  } else if (singleProduct.value.type == "4") {
    return singleProduct.value.artistMeetId;
  } else if (singleProduct.value.type == "3") {
    return singleProduct.value.artistMeetId;
  }
};

const applyCoupon = async () => {
  if (coupon.value !== "") {
    appliedCoupon.value = coupon.value.toUpperCase();
    coupon.value = "";

    try {
      const resp = await cartSingle(
        selectedCurrency === "USD" ? "currency=2" : "currency=1",
        `coupon=${appliedCoupon.value}`,
        `productItemId=${properid()}`,
        `type=${singleProduct.value.type}`
      );

      if (resp.length > 0) {
        items.value = resp;
        couponDetail.value = "";
      } else {
        items.value = null;
        couponDetail.value = "Invalid Coupon";
        appliedCoupon.value = null;
      }
    } catch (error) {
      console.error("error while applying coupon", error);
      items.value = null;
      couponDetail.value = "Invalid Coupon";
      appliedCoupon.value = null;
      snackbarRef?.value?.showSnackbar(
        `error in applying coupon "${error?.message}"`,
        "red"
      );
    }
  }
};

const removeCoupon = () => {
  appliedCoupon.value = null;
  costBfrCoupon.value = null;
  discountValue.value = null;
};

const openPayment = (tokenUrl) => {
  try {
    window.PhonePeCheckout.transact({
      tokenUrl,
      callback,
      type: "IFRAME",
    });
  } catch (error) {
    console.error("Error in opening payment", error);
    snackbarRef?.value?.showSnackbar(
      `error in opening payment "${error?.message}"`,
      "red"
    );
  }
};

const openPaymentForPayGlocal = (tokenUrl) => {
  try {
    window.PGPay.launchPayment(
      {
        redirectUrl: tokenUrl,
      },
      callbackForPayglocal
    );
  } catch (error) {
    console.error("Error in opening payment", error);
    snackbarRef?.value?.showSnackbar(
      `error in opening payment 2"${error?.message}"`,
      "red"
    );
  }
};

const paymentMethodNum = {
  phonepe: 1,
  paypal: 2,
  payglocal: 3,
};

const meetOrderPlace = async (paymentMethod) => {
  try {
    let response;
    if (orderCache.response && orderCache.paymentMethod === paymentMethod) {
      return
    }

    if (isAddressChanged.value) {
      response = await orderNowinitMeetPaypal({
        artistMeetId: singleProduct.value.artistMeetId,
        address: address.value,
        coupon: appliedCoupon.value,
        currency: selectedCurrency === "INR" ? 1 : 2,
        payment_method: paymentMethodNum[paymentMethod],
      });
    } else {
      response = await orderNowinitMeetPaypal({
        artistMeetId: singleProduct.value.artistMeetId,
        address_id: address.value.address_id || 3,
        coupon: appliedCoupon.value,
        currency: selectedCurrency === "INR" ? 1 : 2,
        payment_method: paymentMethodNum[paymentMethod],
      });

      orderCache.response = response;
      orderCache.paymentMethod = paymentMethod;
      processOrderResponse(response, paymentMethod);

      if (response) {
        if (paymentMethod === "phonepe") {
          let url = response.data.selectedPayment.url;
          orderId = response.data.selectedPayment.id;
          mTxnId = response.data.selectedPayment.order_id;
          isLoading.value = false;
          openPayment(url);
        }

        if (paymentMethod === "paypal" && !PayPalButtonRendered.value) {
          orderId = response.data.selectedPayment.id;
          mTxnId = response.data.selectedPayment.order_id;
          setupPayPalButton(response.data.selectedPayment.order_id);
          payPalButtonRenderSet();
        }

        if (paymentMethod === "payglocal") {
          let url = response.data.selectedPayment.url;
          orderId = response.data.selectedPayment.id;
          mTxnId = response.data.selectedPayment.order_id;
          isLoading.value = false;
          openPaymentForPayGlocal(url);
        }
      } else {
        console.error("Invalid API response from orderNowinitMeet");
      }
    }
  } catch (error) {
    console.error("Error in orderNowinitMeet API", error);
    snackbarRef?.value?.showSnackbar(
      `error in meet payment "${error?.message}"`,
      "red"
    );
  }
};

const fetchCart = async () => {
  await fetchAddress();
  if (route.query.productId) {
    singleProduct.value = {
      id: route.query.productId,
      artist_name: route.query.artist_name,
      productId: route.query.id,
      image: route.query.image,
      name: route.query.name,
      price: Number(route.query.price),
      tax: Number(route.query.tax),
      currency: route.query.currency,
      quantity: Number(route.query.quantity),
      type: Number(route.query.type),
      slotTime: route.query.slotTime,
      slotDate: route.query.slotDate,
      artistMeetId: route.params.id,
    };
  }
  currencySymbol.value = singleProduct.value.currency;
  tax.value = singleProduct.value.tax;
  isLoading.value = false;
};

const fetchAddress = async () => {
  try {
    const resp = await getUserAddress();
    if (resp.address) {
      address.value = resp.address;
      respaddress.value = structuredClone(resp.address);
      address.value.country = "India";
      respaddress.value.country = "India";
    } else {
      respaddress.value = null;
      address.value = {
        first_name: "",
        last_name: "",
        country: "",
        address_line_one: "",
        address_line_two: "",
        address_line_three: "",
        city: "",
        state: "",
        pincode: "",
        phone: "",
        email: "",
      };
    }
    isAddressFormReady.value = true;
  } catch (error) {
    console.error("Error in product detail API", error);
    snackbarRef?.value?.showSnackbar(
      `error in getting address "${error?.message}"`,
      "red"
    );
  }
};

const validateAndSubmit = async () => {
  await meetOrderPlace(selectedCurrency === "INR" ? "phonepe" : "payglocal");
};

onBeforeMount(async () => {
  await fetchCart();
});
</script>

<style scoped>
.action-button {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: right;
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: white;
}

.dark-input {
  width: 100%;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: white;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.dark-input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.08);
}

input[type="radio"]{
  cursor: pointer;
}

.error-input {
  border-color: red;
  box-shadow: 0 0 0 1px red;
}

.error-message {
  color: red;
  font-size: 12px;
  margin-top: 4px;
  padding-left: 20px;
}

.full-width-table {
  width: 100%;
  border-collapse: collapse;
  color: white;
}

.full-width-table td {
  padding: 8px 0;
}

.full-width-table tr:last-child td {
  padding-top: 16px;
  font-weight: bold;
}
</style>