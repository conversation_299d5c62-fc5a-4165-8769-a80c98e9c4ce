<template>
  <v-snackbar v-model="snackbar" :timeout="snackBarTimeout">
    {{ snackbarText }}

    <template v-slot:actions>
      <v-btn color="blue" variant="text" @click="snackbar = false">
        Close
      </v-btn>
    </template>
  </v-snackbar>
  <ErrorAlert v-if="showAPIError" :message="errorMessage" />
  <v-overlay v-model="showAPISuccess">
    <v-dialog v-model="showAPISuccess" :scrim="false" persistent width="auto">
      <v-card color="primary">
        <v-card-text>
          Entering into metaverse
          <v-progress-linear
            indeterminate
            color="white"
            class="mb-0"
          ></v-progress-linear>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-overlay>
  <v-container class="fill-height" fluid>
    <v-row align="center" justify="center">
      <v-col cols="12" sm="8" md="4">
        <v-card class="elevation-12" color="transparent">
          <v-card-text class="text-center">
            <v-img
              :src="artistLogo"
              alt="AV Logo"
              contain
              height="100"
              class="mb-4"
            ></v-img>
          </v-card-text>
          <v-card-text>
            <v-form @submit.prevent="onSentOTP">
              <CustomInput
                v-model="email"
                placeholder="Email/ Phone Number"
                name="email"
                prepend-inner-icon="mdi-email"
                type="text"
                custom-class="mb-4"
              />
              <CustomInput
                v-model="password"
                placeholder="Password"
                name="password"
                prepend-inner-icon="mdi-lock"
                type="password"
                custom-class="mb-6"
              />
              <v-row justify="center">
                <v-col cols="12" sm="6" class="text-center">
                  <v-btn
                    type="submit"
                    color="white"
                    block
                    rounded
                    outlined
                    class="white--text custom-button custom-outlined"
                  >
                    Send OTP
                  </v-btn>
                </v-col>
              </v-row>
              <v-row class="mt-4">
                <v-col cols="12" class="text-center">
                  <v-btn
                    @click="goToLogin"
                    color="transparent"
                    text
                    class="white--text custom-text-button"
                  >
                    Already have an account? Login
                  </v-btn>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>

  <OTPPopup
    v-model="showOTPPopup"
    :email="email"
    @verify-otp="onOTPSubmit"
    @resend-otp="onResendOTP"
  />
</template>

<script setup>
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import artistLogo from "@/assets/artist_logo.png";
import { isValidUserNamePass } from "@/helper/common";
import { register, resendOTP, verifyOTP } from "@/services/userService";

const router = useRouter();
const route = useRoute();
const toSend = route.query.toSend || "/";
const paramToSend = route.query;
const email = ref("");
const password = ref("");
let showAPIError = ref(false);
let showAPISuccess = ref(false);
let errorMessage = ref("Something Went Wrong");
const showOTPPopup = ref(false);

let snackbar = ref(false);
let snackbarText = "OTP Sent successfully";
let snackBarTimeout = 1000;

const onSentOTP = async () => {
  if (isValidUserNamePass(email.value, password.value)) {
    try {
      await register({
        userName: email.value,
        password: password.value,
        client_id: 1,
      });
      showOTPPopup.value = true;
    } catch (error) {
      showAPIError.value = true;
      console.error("On Register", error);
      errorMessage.value = error?.message ? error.message : errorMessage.value;
      setTimeout(() => {
        showAPIError.value = false;
      }, 2000);
    }
  }
};

const onResendOTP = async () => {
  try {
    await resendOTP({
      userName: email.value,
    });
    snackbar.value = true;
  } catch (error) {
    showAPIError.value = true;
    console.error("On Resend OTP", error);
    errorMessage = error?.message ? error.message : errorMessage;
    setTimeout(() => {
      showAPIError.value = false;
    }, "2000");
  }
};

const afterLoginAction = () => {
  router.push({
    path: toSend,
    query: paramToSend,
  });
};

const onOTPSubmit = async (otp) => {
  try {
    await verifyOTP({
      userName: email.value,
      otp: otp,
      client_id: 1,
    });
    showAPISuccess.value = true;
    setTimeout(() => {
      showAPISuccess.value = false;
      afterLoginAction();
    }, 2000);
  } catch (error) {
    showAPIError.value = true;
    console.error("OTP Verification Error", error);
    errorMessage.value = error?.message
      ? error.message
      : "OTP verification failed";
    setTimeout(() => {
      showAPIError.value = false;
    }, 2000);
  }
};

const goToLogin = () => {
  router.push({
    path: "/login",
    query: paramToSend,
  });
};
</script>

<style scoped>
.custom-button {
  height: 48px !important;
  text-transform: none !important;
  font-weight: normal !important;
  font-size: 16px !important;
  letter-spacing: normal !important;
}

.custom-outlined {
  border: 1px solid white !important;
}

.custom-text-button {
  text-transform: none !important;
  font-weight: normal !important;
  font-size: 14px !important;
  letter-spacing: normal !important;
}

:deep(.v-btn__content) {
  opacity: 1 !important;
}
</style>
