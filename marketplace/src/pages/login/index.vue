<template>
  <ErrorAlert v-if="showAPIError" :message="errorMessage" />
  <v-container class="fill-height" fluid>
    <v-row align="center" justify="center">
      <v-col cols="12" sm="8" md="4">
        <v-card class="elevation-12" color="transparent">
          <v-card-text class="text-center">
            <v-img
              :src="artistLogo"
              alt="AV Logo"
              contain
              height="100"
              class="mb-4"
            ></v-img>
          </v-card-text>
          <v-card-text>
            <v-form @submit.prevent="handleLogin">
              <CustomInput
                v-model="email"
                placeholder="Email/ Phone Number"
                name="email"
                prepend-inner-icon="mdi-email"
                type="text"
                custom-class="mb-4"
              />
              <CustomInput
                v-model="password"
                placeholder="Password"
                name="password"
                prepend-inner-icon="mdi-lock"
                type="password"
                custom-class="mb-6"
              />
              <v-row>
                <v-col cols="6">
                  <v-btn
                    type="submit"
                    color="white"
                    block
                    rounded
                    class="black--text custom-button custom-outlined"
                  >
                    Log in
                  </v-btn>
                </v-col>
                <v-col cols="6">
                  <v-btn
                    @click="handleSignUp"
                    color="transparent"
                    block
                    rounded
                    outlined
                    class="white--text custom-button custom-outlined"
                  >
                    Sign up
                  </v-btn>
                </v-col>
              </v-row>
              <v-row class="mt-4">
                <v-col cols="12" class="text-center">
                  <v-btn
                    @click="forgotPassword"
                    color="transparent"
                    text
                    class="white--text custom-text-button"
                  >
                    Forgot Password?
                  </v-btn>
                </v-col>
              </v-row>
              <v-row class="mt-6">
                <v-col cols="12" class="text-center">
                  <div v-if="isGoogleEnable" class="d-inline-block">
                    <GoogleLogin :callback="googleCallBack" prompt auto-login />
                  </div>

                  <!-- FB Login  -->
                  <div>
                    <v-btn
                      class="custom-button custom-outlined"
                      color="white"
                      @click="loginWithFacebook"
                    >
                      <v-icon left>mdi-facebook</v-icon>
                      Login with Facebook
                    </v-btn>

                    <!-- X Login  -->
                  </div>
                  <div class="mt-2">
                    <v-btn
                      class="custom-button custom-outlined"
                      color="white"
                      @click="loginWithTwitter"
                    >
                      <v-icon left>mdi-twitter</v-icon>
                      Login with Twitter
                    </v-btn>
                  </div>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import artistLogo from "@/assets/artist_logo.png";
import { isValidUserNamePass } from "@/helper/common";
import {
  facebookLoginAPI,
  getUserToken,
  googleLoginAPI,
  login,
  setUserToken,
  twitterLoginAPI,
} from "@/services/userService";
import {
  auth,
  Facebookprovider,
  TwitterProvider,
  signInWithPopup,
} from "@/services/firebase";

const router = useRouter();
const route = useRoute();

const isGoogleEnable = ref(true);
const email = ref("");
const password = ref("");
let showAPIError = ref(false);
let errorMessage = ref("Something Went Wrong");
const toSend = route.query.toSend || "/";
const paramToSend = route.query;

const handleLogin = async () => {
  if (isValidUserNamePass(email.value, password.value)) {
    try {
      await login({
        userName: email.value,
        password: password.value,
        client_id: 1,
      });
      afterLoginAction();
    } catch (error) {
      showAPIError.value = true;
      console.error("On Submit", error);
      errorMessage.value = error?.message ? error.message : errorMessage.value;
      setTimeout(() => {
        showAPIError.value = false;
      }, 2000);
    }
  }
};

const handleSignUp = () => {
  router.push({
    path: "/register",
  });
};

const forgotPassword = () => {
  router.push({
    path: "/forgotPassword",
  });
};

const afterLoginAction = () => {
  router.push({
    path: toSend,
    query: paramToSend,
  });
};

const googleCallBack = async (response) => {
  try {
    await googleLoginAPI({
      token: response.credential,
      client_id: 1,
    });
    afterLoginAction();
  } catch (error) {
    showAPIError.value = true;
    console.error("On Google Login", error);
    errorMessage.value = error?.message ? error.message : errorMessage.value;
    setTimeout(() => {
      showAPIError.value = false;
    }, 2000);
  }
};

const checkUserToken = () => {
  if (route.query.token) {
    setUserToken(route.query.token);
    afterLoginAction();
  } else if (getUserToken()) {
    afterLoginAction();
  }
};

const isFbReady = ref(false);

const loginWithFacebook = async () => {
  try {
    const result = await signInWithPopup(auth, Facebookprovider);
    await facebookLoginAPI({
      token: result.user.accessToken,
      client_id: 1,
    });
    afterLoginAction();
  } catch (error) {
    console.error("Facebook Login Error:", error);
    showAPIError.value = true;
    setTimeout(() => {
      showAPIError.value = false;
    }, 2000);
    errorMessage.value = error?.message ? error.message : errorMessage.value;
  }
};

const loginWithTwitter = async () => {
  try {
    const result = await signInWithPopup(auth, TwitterProvider);
    await twitterLoginAPI({
      token: result.user.accessToken,
      client_id: 1,
    });
    afterLoginAction();
  } catch (error) {
    console.error("Twitter Login Error:", error);
    showAPIError.value = true;
    setTimeout(() => {
      showAPIError.value = false;
    }, 2000);
    errorMessage.value = error?.message ? error.message : errorMessage.value;
  }
};

onMounted(async () => {
  checkUserToken();
  const userAgent = navigator.userAgent || window.opera;
  if (userAgent.includes("FBAN") || userAgent.includes("FBAV")) {
    isGoogleEnable.value = false;
  } else if (userAgent.includes("Instagram")) {
    isGoogleEnable.value = false;
  } else if (userAgent.includes("LinkedIn")) {
    isGoogleEnable.value = false;
  } else {
    isGoogleEnable.value = true;
  }
});
</script>

<style scoped>
.custom-button {
  height: 48px !important;
  text-transform: none !important;
  font-weight: normal !important;
  font-size: 16px !important;
  letter-spacing: normal !important;
}

.custom-outlined {
  border: 1px solid white !important;
}

.custom-text-button {
  text-transform: none !important;
  font-weight: normal !important;
  font-size: 14px !important;
  letter-spacing: normal !important;
}

:deep(.v-btn__content) {
  opacity: 1 !important;
}
</style>
