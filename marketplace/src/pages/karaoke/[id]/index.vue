<template>
  <template v-if="appStore.isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular :size="70" :width="7" color="white" indeterminate></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <template v-else>
    <v-row>
      <v-col cols="12" lg="8" md="8" sm="8">
        <div class="video-container">

          <video v-if="appStore.selectedSong.video_url" ref="videoPlayer" :src="appStore.selectedSong.video_url"
            class="video-player" controls controlsList="nodownload noremoteplayback" @play="startRecording"
            @pause="pauseRecording" @ended="stopRecording" @timeupdate="handleTimeUpdate"></video>

          <div v-if="!isPlaying" class="play-button" @click="startVideo">
            <v-icon x-large color="white" style="font-size: 50px">mdi-play</v-icon>
          </div>
        </div>
        <template v-if="isEarphone && microphonePermission">
          <div class="d-flex justify-center align-center gap-4 mt-4">
            <v-icon v-if="
              videoPlaybackState === 'not-started' ||
              videoPlaybackState === 'paused'
            " icon="mdi-play-circle-outline" color="white" size="45" @click="startVideo"></v-icon>
            <v-icon v-if="videoPlaybackState === 'playing'" icon="mdi-pause-circle-outline" color="white" size="45"
              @click="pauseVideo"></v-icon>
            <v-icon v-if="
              (videoPlaybackState === 'playing' ||
                videoPlaybackState === 'paused') &&
              isStop
            " icon="mdi-stop-circle-outline" color="white" size="45" @click="stopVideo"></v-icon>
          </div>
          <v-card-text class="text-center">
            Clicking on Play Button will start the recording and stoping will
            save them
          </v-card-text>
        </template>
        <v-card-text v-else class="text-center">
          To record your voice, Please allow Mic and use earphones.
        </v-card-text>
      </v-col>
      <v-col cols="12" lg="4" md="4" sm="4" class="track-section">
        {{ artsitName }}
        <v-divider :thickness="1" class="mt-4 mr-10 border-opacity-100" dark></v-divider>
        <div v-for="item in appStore.karaokeList" :key="item.id" @click="selectSong(item)">
          <v-row align="center" :class="{ 'selected-record': appStore.selectedSong.id === item.id }"
            class="mt-2 song-title">
            <!-- Avatar Image -->
            <v-col cols="auto">
              <v-img :src="item.thumbnail_url" alt="Avatar" class="avatar" rounded />
            </v-col>
            <!-- Single Line Text Content -->
            <v-col>
              <div class="text-row">
                <span class="title">{{ item.name }}</span>
              </div>
            </v-col>
          </v-row>
        </div>
      </v-col>
    </v-row>
  </template>
</template>

<script setup>
import { onBeforeMount, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import axios from "axios";

import { getArtistName, sendDataToParent } from "@/helper/common";
import { getUserToken, setUserToken } from "@/services/userService";
import { useAppStore } from "@/stores/app";

const router = useRouter();
const route = useRoute();
const appStore = useAppStore();
const isPlaying = ref(false);

const artistId = route.params.id;

const microphonePermission = ref(null);
const isEarphone = ref(null);
const videoPlayer = ref(null);
const isStop = ref(false);
const videoPlaybackState = ref("not-started");
let mediaRecorder = null;
const minRecordTime = 5;
let audioChunks = [];
let artsitName = getArtistName(artistId);

const checkMicrophonePermission = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    microphonePermission.value = true;
    stream.getTracks().forEach((track) => track.stop());
  } catch (error) {
    microphonePermission.value = false;
  }
};

const detectMicrophones = async () => {
  try {
    const devices = await navigator.mediaDevices.enumerateDevices();

    const microphone = devices.filter((device) => device.kind === "audioinput");
    const uniqueMicrophone = [
      ...new Set(microphone.map((device) => device.groupId)),
    ];

    if (!uniqueMicrophone.length) {
      isEarphone.value = false;
    }

    if (uniqueMicrophone.length == 1) {
      isEarphone.value = true;
    }

    if (uniqueMicrophone.length >= 2) {
      isEarphone.value = true;
    }
  } catch (error) {
    isEarphone.value = false;
    console.error("Error in Getting Microphone", error);
  }
};

const selectSong = (item) => {
  appStore.selectSong(item);
  isStop.value = false;
  videoPlaybackState.value = "not-started";
};

const startRecording = async () => {
  isPlaying.value = true;
  try {
    // Handle If Recording is being Resumed
    if (mediaRecorder && mediaRecorder.state === "paused") {
      mediaRecorder.resume();
      videoPlaybackState.value = "playing";
      return null;
    }

    // Handle Recording Process
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

    mediaRecorder = new MediaRecorder(stream);
    audioChunks = [];
    mediaRecorder.ondataavailable = (e) => {
      if (e.data.size > 0) {
        audioChunks.push(e.data);
      }
    };

    mediaRecorder.onstop = () => {
      processRecording();
    };

    // Starts the recording
    mediaRecorder.start();
  } catch (error) {
    // User Rejects the min permission
    console.error(error);
  }
};

const stopRecording = () => {
  isPlaying.value = false;
  if (mediaRecorder && mediaRecorder.state !== "inactive") {
    mediaRecorder.stop();
  }
};

const pauseRecording = async () => {
  isPlaying.value = false;
  if (mediaRecorder && mediaRecorder.state === "recording") {
    mediaRecorder.pause();
    videoPlaybackState.value = "paused";

  }
};

const handleTimeUpdate = () => {
  if (isStop.value == true) {
    return;
  }

  if (videoPlayer.value.currentTime <= minRecordTime) {
    return;
  }
  isStop.value = true;
};

const startVideo = () => {
  if (videoPlayer.value) {
    if (videoPlayer.value.paused) {
      resumeVideo();
      return;
    }
    videoPlayer.value.play();
    videoPlaybackState.value = "playing";
  }
};

const pauseVideo = () => {
  if (videoPlayer.value) {
    videoPlayer.value.pause();
    videoPlaybackState.value = "paused";
  }
};

const resumeVideo = () => {
  if (videoPlayer.value) {
    videoPlayer.value.play();
    videoPlaybackState.value = "playing";
  }
};

const processRecording = async () => {
  const audioBlob = new Blob(audioChunks, { type: "audio/wav" });
  sendRecording(audioBlob);
  audioChunks = [];
};

const sendRecording = async (audioBlob) => {
  const formData = new FormData();
  formData.append("file", audioBlob, "recorded_audio.wav");
  formData.append("track", appStore.selectedSong.id);
  try {
    appStore.isLoading = true;
    const token = getUserToken();
    const res = await axios({
      method: "POST",
      url: import.meta.env.VITE_BASE_URL + "/api/process-karaoke",
      responseType: "blob",
      data: formData,
      headers: {
        authorization: token,
      },
    });

    const url = window.URL.createObjectURL(new Blob([res.data]));
    // Create a temporary link element
    const link = document.createElement("a");
    link.href = url;
    let name = `${appStore.selectedSong.name} - Karakoke, ${artsitName}.mp3`;
    link.setAttribute("download", name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    appStore.isLoading = false;
  } catch (error) {
    console.error("Error in sending Audio", error);
  }
};

const stopVideo = () => {
  if (videoPlayer.value) {
    videoPlayer.value.load();
    stopRecording();
    videoPlaybackState.value = "stopped";
  }
};

onBeforeMount(async () => {
  if (route.query.token) {
    setUserToken(route.query.token);
  } else if (!getUserToken()) {
    sendDataToParent({ message: "Login Needed" });
    router.push({
      path: `/login`,
      query: {
        toSend: `/karaoke/${artistId}`,
        query: {},
      },
    });
    return;
  }
  try {
    appStore.getKaraoke(artistId);
  } catch (error) { }
});

onMounted(() => {
  checkMicrophonePermission();
  detectMicrophones();
  navigator.mediaDevices.ondevicechange = detectMicrophones;
});
</script>

<style scoped>
/* video::-webkit-media-controls-volume-slider {
  display: none !important;
} */

.video-player::-webkit-media-controls-timeline {
  pointer-events: none;
}

.video-player::-moz-range-thumb {
  pointer-events: none;
}

.video-player::-webkit-media-controls-overflow-button {
  display: none;
}

.video-player::-webkit-media-controls-overflow-menu-button {
  display: none;
}

.video-player::-webkit-media-controls-volume-slider,
.video-player::-webkit-media-controls-mute-button {
  display: none;
}

.video-container {
  position: relative;
  width: 100%;
}

.video-player {
  width: 95%;
  border-radius: 8px;
  margin: 10px;
  border: 4px solid white;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.play-button:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.avatar {
  border-radius: 50%;
  margin-right: 10px;
  width: 40px;
  height: 40px;
}

.text-row {
  display: flex;
  align-items: center;
  white-space: nowrap;
  /* Prevent text wrapping to new lines */
}

.title {
  font-weight: bold;
  font-size: 16px;
  margin-right: 5px;
  /* Add some gap between title and subtitle */
}

@media (max-width: 600px) {
  .track-section {
    margin: 10px;
  }
}

.selected-record {
  background-color: midnightblue;
  margin-right: 10px;
}

.song-title:hover {
  cursor: pointer;
}
</style>
