<template>
    <div>
        <div ref="calendlyContainer" class="calendly-inline-widget" :data-url="props.url"
            style="min-width: 320px; height: 700px"></div>
    </div> <!-- Empty div since <PERSON><PERSON><PERSON> injects the widget dynamically -->
</template>

<script setup>
import { onMounted } from 'vue';
const props = defineProps({
    url: String,  // String prop
});

onMounted(() => {
    // Load Calendly script dynamically
    const script = document.createElement('script');
    script.src = 'https://assets.calendly.com/assets/external/widget.js';
    script.async = true;
    document.head.appendChild(script);

    // Ensure script is loaded before initializing the widget
    // script.onload = () => {
    //     Calendly.initBadgeWidget({
    //         url: props.url,
    //         text: 'Schedule time with me',
    //         color: '#0069ff',
    //         textColor: '#ffffff',
    //     });
    // };
});
</script>

<style>
/* Add Calendly widget styles */
@import url('https://assets.calendly.com/assets/external/widget.css');
</style>