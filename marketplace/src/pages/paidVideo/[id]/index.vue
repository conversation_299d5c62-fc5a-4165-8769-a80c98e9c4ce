<template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular :size="70" :width="7" color="white" indeterminate />
      </v-col>
    </v-row>
  </template>

  <v-container v-else-if="data && data.message !== 'not available'" fluid class="pa-0 main-container">
    <v-row v-if="data.video_stream_url">
      <v-col>
        <div class="video-container">
          <video ref="videoPlayer" :src="data.video_stream_url" class="video-player" controls controlsList="nodownload"
            @play="isPlaying = true" @pause="isPlaying = false" @ended="isPlaying = false"></video>
          <div v-if="!isPlaying" class="play-button" @click="playVideo">
            <v-icon x-large color="white" style="font-size: 50px">mdi-play</v-icon>
          </div>
        </div>
      </v-col>
    </v-row>
    <v-row class="ma-0" v-if="data.video_stream_url == null">
      <v-col v-if="
        (data.trailer_stream_url) &&
        data.video_stream_url == null
      " cols="12" md="6" order="1" order-md="1">
        <v-card color="transparent" flat class="d-flex flex-column align-center">
          <div class="video-container">
            <video ref="tVideoPlayer" :src="data.trailer_stream_url" class="video-player" controls
              controlsList="nodownload" @play="isPlaying = true" @pause="isPlaying = false"
              @ended="isPlaying = false"></video>
            <div v-if="!isPlaying" class="play-button" @click="playVideo">
              <v-icon x-large color="white" style="font-size: 50px">
                mdi-play
              </v-icon>
            </div>
          </div>
        </v-card>
      </v-col>
      <v-col cols="12" md="6" order="2" order-md="2">
        <v-card color="transparent" flat>
          <v-card-title class="white--text text-h4 mb-2">
            {{ data.title }}
          </v-card-title>
          <v-divider class="mb-4" light></v-divider>
          <v-card-text class="white--text">
            <div v-if="data.metadata.description" v-html="data.metadata.description"></div>
            <div class="text-h6">Validity : {{ data.metadata.validity }}</div>
            <div class="text-h6">
              {{
                CurrencyCode === data.currency
                  ? data.currency_symbol
                  : CurrencyCode === "INR"
                    ? `₹`
                    : `$`
              }}
              {{ `${formatIndianCurrency(selectedCurrency.price)}` }}
            </div>
          </v-card-text>
          <v-btn color="white" dark rounded class="black--text mt-6" @click="submitForm">
            Subscribe Now
          </v-btn>
        </v-card>
      </v-col>
      <v-col v-if="data.video_stream_url == null && data.trailer_stream_url == null" cols="12" md="6" order="2"
        order-md="2" class="d-flex align-center justify-center">
        <v-img :src="data.image" style="max-height: 650px; max-width: 100%" class="mx-auto"></v-img>
      </v-col>
    </v-row>
  </v-container>

  <div v-else class="flex flex-col justify-center items-center text-center" style="display: flex; flex-direction: column; align-items: center; height: calc(100dvh - 50px);">
    <v-icon size="48" color="gray">mdi-cart-outline</v-icon>
    <p class="text-lg mt-2">There is no such Video</p>
    <v-btn color="primary" class="mt-4" @click="$router.push('/')">
      Browse Products
    </v-btn>
  </div>
</template>

<script setup>
import { ref, onBeforeMount, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import Hls from "hls.js";
import {
  formatIndianCurrency,
  sendDataToParent,
} from "@/helper/common";
import { getPaidVideoDetail } from "@/services/paidVideoService";
import dummyImage from "@/assets/image_not_available.jpg";
import { getUserToken, setUserToken } from "@/services/userService";
import { useAppStore } from "@/stores/app";

const appStore = useAppStore();
const route = useRoute();
const router = useRouter();

const videoId = route.params.id ? route.params.id : 1;

const videoPlayer = ref(null);
const tVideoPlayer = ref(null);
const isPlaying = ref(false);
const isLoading = ref(true);
const data = ref(null);
let selectedCurrency = ref(null);
const hls = ref(null);
const error = ref(null);

const initializePlayer = (url) => {
  if (Hls.isSupported()) {
    if (hls.value) {
      hls.value.destroy();
    }

    hls.value = new Hls({
      xhrSetup: (xhr) => {
        xhr.withCredentials = true;
      },
    });

    hls.value.loadSource(url);

    if (data.value) {
      if (data.value.video_stream_url) {
        hls.value.attachMedia(videoPlayer.value);
      } else if (data.value.trailer_stream_url) {
        hls.value.attachMedia(tVideoPlayer.value);
      }
    }

    hls.value.on(Hls.Events.MANIFEST_PARSED, () => {
      isLoading.value = false;
    });

    hls.value.on(Hls.Events.ERROR, (event, data) => {
      console.error("HLS error:", data);
      error.value = `Video playback error: ${data.details}`;
      isLoading.value = false;
    });
  } else if (
    videoPlayer.value.canPlayType("application/vnd.apple.mpegurl") ||
    tVideoPlayer.value.canPlayType("application/vnd.apple.mpegurl")
  ) {
    if (data.value.video_stream_url) {
      videoPlayer.value.src = url;
      videoPlayer.value.addEventListener("loadedmetadata", () => {
        isLoading.value = false;
      });
      videoPlayer.value.addEventListener("error", (e) => {
        error.value = `Video playback error: ${e.message || "Unknown error"}`;
        isLoading.value = false;
      });
    } else if (data.value.trailer_stream_url) {
      tVideoPlayer.value.src = url;
      tVideoPlayer.value.addEventListener("loadedmetadata", () => {
        isLoading.value = false;
      });
      tVideoPlayer.value.addEventListener("error", (e) => {
        error.value = `Video playback error: ${e.message || "Unknown error"}`;
        isLoading.value = false;
      });
    }
  } else {
    error.value = "HLS is not supported in this browser.";
    isLoading.value = false;
  }
};

const CurrencyCode = appStore.currencyCode;

const submitForm = () => {
  router.push({
    path: `/singleCheckout/${data.value.id}`,
    query: {
      artist_id: data.value.artist_id,
      productId: data.value.id,
      image: data.value.image || dummyImage,
      name: data.value.title,
      tax: selectedCurrency.value.gst,
      price: selectedCurrency.value.price,
      currency: CurrencyCode === "INR" ? "₹" : "$",
      quantity: 1,
      type: 4,
    },
  });
};

const playVideo = () => {
  if (videoPlayer.value) {
    videoPlayer.value.play();
  } else if (tVideoPlayer.value) {
    tVideoPlayer.value.play();
  }
};

const setVideo = async (data) => {
  if (data.video_stream_url || data.trailer_stream_url) {
    await nextTick(); // Wait until DOM is updated
    initializePlayer(data.video_stream_url || data.trailer_stream_url);
  }
};

const getDetails = async (id) => {
  isLoading.value = true;
  try {
    data.value = await getPaidVideoDetail(id);
    if (data.value) {
      selectedCurrency.value = data.value.currencies.find(
        (currency) => currency.currency_id === (CurrencyCode === "INR" ? 1 : 2)
      );
      setVideo(data.value);
    }
    isLoading.value = false;
  } catch (error) {
    isLoading.value = false;
    // TODO handle error
    console.error(error);
  }
};


onBeforeMount(() => {
  if (route.query.token) {
    setUserToken(route.query.token);
  } else if (!getUserToken()) {
    sendDataToParent({ message: "Login Needed" });
    router.push({
      path: `/login`,
      query: {
        toSend: `/paidVideo/${videoId}`,
        query: {},
      },
    });
    return;
  }
  getDetails(videoId);
});
</script>

<style scoped>
.dark-input {
  background-color: transparent;
  border: 0.2px solid white;
  border-radius: 25px;
  color: rgba(255, 255, 255, 0.9);
  padding: 12px 20px;
  font-size: 16px;
  width: 100%;
  max-width: 500px;
  outline: none;
}

.dark-input::placeholder {
  color: white;
}

.dark-input:focus {
  border-color: white;
  box-shadow: 0 0 0 1px white;
}

.video-container {
  position: relative;
  width: 100%;
}

.video-player {
  width: 100%;
  border-radius: 8px;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.play-button:hover {
  background-color: rgba(0, 0, 0, 0.7);
}
</style>
