<template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular :size="70" :width="7" color="white" indeterminate></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <template v-else>
    <div class="ma-12">
      <v-sheet v-if="orderDetail.payment_status == 'PAYMENT_SUCCESS'">
        <p class="text-caption text-left pa-2 ma-2">
          THANK YOU FOR SHOPPING WITH US. YOUR ACCOUNT HAS BEEN CHARGED AND YOUR
          TRANSACTION IS SUCCESSFUL. WE WILL BE PROCESSING YOUR ORDER SOON.
        </p>
      </v-sheet>
      <v-sheet v-if="
        orderDetail.payment_status == 'PAYMENT_PENDING' ||
        orderDetail.payment_status == 'PAYMENT_INIT'
      ">
        <p class="text-caption text-left pa-2 ma-2">
          THANK YOU FOR SHOPPING WITH US. PAYMENT IS PENDING.
        </p>
      </v-sheet>
      <v-sheet color="red" v-if="
        orderDetail.payment_status == 'PAYMENT_DECLINED' ||
        orderDetail.payment_status == 'PAYMENT_FAILED'
      ">
        <p class="text-caption text-left pa-2 ma-2">
          Payment Failed for this Order
        </p>
      </v-sheet>
      <v-row>
        <v-col cols="12" lg="6" md="6" sm="6">
          <h4>Order Details</h4>
          <p><strong>Order number:</strong> {{ orderDetail.orderNumber }}</p>
          <p><strong>Date:</strong> {{ orderDetail.date }}</p>
          <p><strong>Order Status:</strong> {{ orderDetail.order_status }}</p>
          <p>
            <strong>Payment Status:</strong> {{ orderDetail.payment_status }}
          </p>
        </v-col>
        <v-col cols="12" lg="6" md="6" sm="6">
          <h4 v-if="orderDetail?.address?.address_line_one">Shipping Address:</h4>
          <p v-if="orderDetail?.address?.first_name">
            {{
              orderDetail?.address?.first_name +
              " " +
              orderDetail?.address?.last_name
            }}
          </p>
          <p v-if="orderDetail?.address?.address_line_one">
            {{ orderDetail?.address?.address_line_one }}
          </p>
          <p v-if="orderDetail?.address?.address_line_two">
            {{ orderDetail?.address?.address_line_two }}
          </p>
          <p v-if="orderDetail?.address?.address_line_three">
            {{ orderDetail?.address?.address_line_three }}
          </p>
          <p v-if="orderDetail?.address?.city">
            {{ orderDetail?.address?.city + " , " + orderDetail?.address?.pincode }}
          </p>
          <p v-if="orderDetail?.address?.state">
            {{
              orderDetail?.address?.state + " , " + orderDetail?.address?.country
            }}
          </p>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12" lg="6" md="6" sm="12">
          <v-table class="elevation-0" style="border: none; background: transparent">
            <thead>
              <tr>
                <th class="text-left text-white">Product</th>
                <th class="text-left text-white hide-on-mobile">Price</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="product in products" :key="product.id" style="border: none">
                <td style="border: none">
                  <v-row no-gutters class="mb-5">
                    <v-col cols="auto" class="mr-4">
                      <div class="image-container">
                        <img :src="product.image" height="100" alt="Image 1" class="image-front" />
                      </div>
                    </v-col>
                    <v-col class="d-flex flex-column justify-space-between">
                      <div>
                        {{ product.title }}
                        <br />
                        <v-row>
                          <v-col v-for="(el, index) in filteredVariants(
                            product.variants || []
                          )" :key="index">
                            <div class="text-subtitle-1 text-capitalize" v-if="el">
                              {{ Object.keys(el)[0] }} :
                              {{ Object.values(el)[0] }}
                            </div>
                          </v-col>
                        </v-row>
                      </div>
                      <div>
                        <v-row class="show-on-mobile">
                          <v-col>
                            {{ `x${product.quantity} Items` }}
                          </v-col>
                          <v-col>
                            {{ orderDetail.currency_symbol }}
                            {{ product.total }}
                          </v-col>
                        </v-row>
                        <div class="hide-on-mobile">
                          {{ `x${product.quantity} Items` }}
                        </div>
                      </div>
                    </v-col>
                  </v-row>
                </td>
                <td class="hide-on-mobile" style="border: none">
                  {{ orderDetail.currency_symbol }}
                  <span v-if="
                    orderDetail?.discount?.code &&
                    Number(product?.original_price)?.toFixed(2) !==
                    Number(product?.total)?.toFixed(2)
                  ">
                    <del>{{ product.original_price * product.quantity }}</del>
                  </span>
                  {{ product.total }}
                </td>
              </tr>
            </tbody>
          </v-table>
        </v-col>
        <v-col cols="12" lg="6" md="6" sm="12"> </v-col>
      </v-row>
      <v-row>
        <v-col cols="12" lg="6" md="6" sm="12">
          <v-divider class="border-opacity-100" color="grey"></v-divider>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="6" lg="3" md="3" sm="3">
          <p v-if="
            (orderDetail.discount && orderDetail.discount.code) ||
            Number(taxValue).toFixed(2) !==
            Number(totalOriginalPrice).toFixed(2)
          ">
            <strong>Cart Total: </strong>
          </p>
          <p v-if="
            Number(totalOriginalPrice.toFixed(2)) -
            Number(taxValue.toFixed(2)) !=
            0
          ">
            <strong>Coupon Discount:
              <span v-if="orderDetail.discount && orderDetail.discount.code">
                {{ orderDetail.discount.code }}
              </span></strong>
          </p>
          <p>
            <strong>Subtotal: </strong>
          </p>
          <p v-if="taxValue > 0 && orderDetail.currency_name !== 'USD'">
            <strong>Tax:</strong>
          </p>
          <p><strong>Shipping:</strong></p>
          <p><strong>Total:</strong></p>
        </v-col>
        <v-col cols="6" lg="3" md="3" sm="3" class="text-right">
          <!-- cart total -->
          <p v-if="
            (orderDetail.discount && orderDetail.discount.code) ||
            Number(taxValue).toFixed(2) !==
            Number(totalOriginalPrice).toFixed(2)
          ">
            {{ totalOriginalPrice.toFixed(2) }}
          </p>
          <!-- discount -->
          <p v-if="
            (Number(totalOriginalPrice) - Number(taxValue)).toFixed(2) != 0
          ">
            -{{ (Number(totalOriginalPrice) - Number(taxValue)).toFixed(2) }}
          </p>
          <!-- subtotal -->
          <p>
            {{ Number(taxValue).toFixed(2) }}
          </p>
          <!-- tax -->
          <p v-if="
            taxValue > 0 && orderDetail.currency_name !== 'USD'
            // (
            //   Number(orderDetail?.total).toFixed(2) -
            //   Number(taxValue).toFixed(2)
            // ).toFixed(2) > 0
          ">
            {{
              (
                Number(orderDetail?.total).toFixed(2) -
                Number(taxValue).toFixed(2)
              ).toFixed(2)
            }}
          </p>
          <!-- shipping -->
          <p>{{ orderDetail.shipping || "-" }}</p>
          <!-- total -->
          <p>
            <strong>
              {{ orderDetail.currency_symbol }} {{ orderDetail.total }}</strong>
          </p>
        </v-col>
      </v-row>
      <br />
      <v-row>
        <v-btn v-if="orderDetail.payment_status == 'PAYMENT_SUCCESS'" @click="downloadInvoice()" color="white"
          class="view-order mb-2 mr-2" height="50">
          <span>Download Invoice</span>
        </v-btn>
        <v-btn v-if="
          orderDetail.payment_status == 'PAYMENT_SUCCESS' &&
          orderDetail.order_status == 'PLACED' &&
          orderDetail.is_cancellable
        " @click="triggeringCancelOrder()" color="red" height="50">
          <span>CANCEL</span>
        </v-btn>

        <v-btn v-if="orderType == 'COURSE' || orderType == 'PAID_VIDEO'" @click="goToProduct()" color="white"
          class="view-order mb-2" height="50">
          <span class="button-text">Access Now</span>
        </v-btn>
      </v-row>
      <v-dialog transition="dialog-top-transition" v-model="dialogState" width="380" height="200">
        <v-card v-if="dialogState" class="dialogBoxCard">
          Are you sure you want to cancel this order?
          <div class="dialogContent">
            <v-btn @click="cancelOrder()" rounded class="dialogcancelBTNYes">
              Yes
            </v-btn>
            <v-btn @click="triggeringCancelOrderClose()" rounded class="dialogcancelBTNNo">
              No
            </v-btn>
          </div>
        </v-card>
      </v-dialog>
    </div>

  </template>
  <Snackbar ref="snackbarRef" />

</template>

<script setup>
import { sendDataToParent } from "@/helper/common";
import { downloadFile } from "@/services/fileService";
import { getOrderDetail } from "@/services/merchService";
import { orderStatusCancel } from "@/services/productService";
import { getUserToken, setUserToken } from "@/services/userService";
import { onBeforeMount, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import Snackbar from "@/components/Snackbar.vue";


const router = useRouter();
const route = useRoute();

const orderId = route.params.id;
const orderType = route.query.order_type;

const orderDetail = ref(null);
const products = ref(null);
const isLoading = ref(true);
const dialogState = ref(false);

const taxValue = ref(0);
let totalOriginalPrice = ref(0);
const snackbarRef = ref(null);


const goToProduct = () => {
  if (orderType == "COURSE") {
    router.push({
      path: `/course/${orderDetail.value.product_id}`,
    });
  }

  if (orderType == "PAID_VIDEO") {
    router.push({
      path: `/paidVideo/${orderDetail.value.product_id}`,
    });
  }
};

const triggeringCancelOrder = () => {
  dialogState.value = true;
};
const triggeringCancelOrderClose = () => {
  dialogState.value = false;
};

const cancelOrder = async () => {
  isLoading.value = true;
  try {
    const response = await orderStatusCancel({
      order_id: orderId,
      order_type: orderType,
    });
    if (response.status === "success") {
      dialogState.value = false;
      isLoading.value = false;
      window.location.reload();
    }
  } catch (error) {
    console.error("Error in cancel order API", error);
     setTimeout(() => {
    snackbarRef?.value?.showSnackbar(
        `error in canceling order "${error?.message}"`,
        "red"
      );}, 0);
  }
};

const downloadInvoice = async () => {
  try {
    const response = await downloadFile(orderDetail.value.invoice_url);
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute(
      "download",
      `invoice_${orderDetail.value.orderNumber}.pdf`
    ); // or the filename you want
    document.body.appendChild(link);
    link.click();
  } catch (error) {
    console.error("Error downloading the file: ", error);
     setTimeout(() => {
    snackbarRef?.value?.showSnackbar(
        `error in downloading the file "${error?.message}"`,
        "red"
      );}, 0);
  }
};

const filteredVariants = (variants) => {
  return variants.filter((variant) => {
    const key = Object.keys(variant)[0];
    const value = variant[key];
    return key && value; // Keep only variants with valid key-value pairs
  });
};

const variantKey = (variant) => Object.keys(variant)[0];
const variantValue = (variant) => variant[variantKey(variant)];

onBeforeMount(async () => {
  try {
    if (!orderId || !orderType) {
      return null;
    }
    if (route.query.token) {
      setUserToken(route.query.token);
    } else if (!getUserToken()) {
      sendDataToParent({ message: "Login Needed" });
      router.push({
        path: `/orderDetail/${orderId}`,
        query: {
          toSend: `/orderHistory`,
          query: {
            order_type: orderType,
          },
        },
      });
      return;
    }

    const resp = await getOrderDetail(orderId, orderType);

    isLoading.value = false;
    orderDetail.value = resp.detail;
    products.value = resp.products;

    taxValue.value = products?.value?.reduce((totalTax, product) => {
      const final = totalTax + Number(product.subtotal);
      return Number(final);
    }, 0);

    totalOriginalPrice.value = products.value.reduce((total, product) => {
      return (
        total +
        parseFloat(
          Number(product.original_price) / (1 + Number(product.tax) / 100)
        )
      );
    }, 0);
  } catch (error) {
    isLoading.value = false;
    console.error("Error in order detail API", error);
     setTimeout(() => {
    snackbarRef?.value?.showSnackbar(
        `error in order Details "${error?.message}"`,
        "red"
      );}, 0);
  }
});
</script>

<style scoped>
.image-front {
  border-radius: 10px;
  max-width: 100px;
  max-height: 100px;
}

.v-table .v-table__wrapper>table>thead>tr>th {
  border: 0px;
}

.view-order {
  border-radius: 30px !important;
  font-weight: bold;
  color: black !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 0 7px 0 20px !important;
  position: relative !important;
  overflow: hidden !important;
}

.dialogBoxCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: white;
  color: #000;
  border-radius: 12px;
  font-family: Helvetica, Arial, sans-serif;
}

.dialogContent {
  display: flex;
  justify-content: center;
  gap: 10px;
  width: 100%;
}

.dialogcancelBTNYes {
  background-color: black;
  color: white;
  padding: 10px 20px;
  font-size: 14px;
}

.dialogcancelBTNNo {
  background-color: red;
  color: white;
  padding: 10px 20px;
  font-size: 14px;
}

@media (max-width: 600px) {
  .show-on-mobile {
    display: block;
  }
}

@media (min-width: 601px) {
  .show-on-mobile {
    display: none;
  }
}

@media (max-width: 600px) {
  .hide-on-mobile {
    display: none;
  }
}

@media (min-width: 601px) {
  .hide-on-mobile {
    display: block;
  }
}
</style>
