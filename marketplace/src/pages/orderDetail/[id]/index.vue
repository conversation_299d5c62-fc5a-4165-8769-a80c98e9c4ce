<template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular :size="70" :width="7" color="white" indeterminate></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <template v-else-if="products.length > 0">
    <div class="ma-12">
      <v-sheet v-if="orderDetail.payment_status == 'PAYMENT_SUCCESS'">
        <p class="text-caption text-left pa-2 ma-2">
          THANK YOU FOR SHOPPING WITH US. YOUR ACCOUNT HAS BEEN CHARGED AND YOUR
          TRANSACTION IS SUCCESSFUL. WE WILL BE PROCESSING YOUR ORDER SOON.
        </p>
      </v-sheet>
      <v-sheet v-if="
        orderDetail.payment_status == 'PAYMENT_PENDING' ||
        orderDetail.payment_status == 'PAYMENT_INIT'
      ">
        <p class="text-caption text-left pa-2 ma-2">
          THA<PERSON><PERSON> YOU FOR SHOPPING WITH US. PAYMENT IS PENDING.
        </p>
      </v-sheet>
      <v-sheet color="red" v-if="
        orderDetail.payment_status == 'PAYMENT_DECLINED' ||
        orderDetail.payment_status == 'PAYMENT_FAILED'
      ">
        <p class="text-caption text-left pa-2 ma-2">
          Payment Failed for this Order
        </p>
      </v-sheet>
      <v-row>
        <v-col cols="12" lg="6" md="6" sm="6">
          <h4>Order Details</h4>
          <p><strong>Order number:</strong> {{ orderDetail.orderNumber }}</p>
          <p><strong>Date:</strong> {{ orderDetail.date }}</p>
          <p><strong>Order Status:</strong> {{ orderDetail.order_status }}</p>
          <p>
            <strong>Payment Status:</strong> {{ orderDetail.payment_status }}
          </p>
        </v-col>
        <v-col cols="12" lg="6" md="6" sm="6">
          <h4 v-if="orderDetail?.address?.address_line_one">Shipping Address:</h4>
          <p v-if="orderDetail?.address?.first_name">
            {{
              orderDetail?.address?.first_name +
              " " +
              orderDetail?.address?.last_name
            }}
          </p>
          <p v-if="orderDetail?.address?.address_line_one">
            {{ orderDetail?.address?.address_line_one }}
          </p>
          <p v-if="orderDetail?.address?.address_line_two">
            {{ orderDetail?.address?.address_line_two }}
          </p>
          <p v-if="orderDetail?.address?.address_line_three">
            {{ orderDetail?.address?.address_line_three }}
          </p>
          <p v-if="orderDetail?.address?.city">
            {{ orderDetail?.address?.city + " , " + orderDetail?.address?.pincode }}
          </p>
          <p v-if="orderDetail?.address?.state">
            {{
              orderDetail?.address?.state + " , " + orderDetail?.address?.country
            }}
          </p>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12" lg="6" md="6" sm="12">
          <!-- Modern, minimal product list: Card-based, no table -->
          <div>
            <div v-for="(product, idx) in products" :key="product.id" class="mb-0">
              <v-sheet class="pa-4 d-flex flex-column flex-md-row align-center" color="transparent" elevation="0" rounded style="border: 1px solid #333; background: transparent;">
                <v-img :src="product.image" height="80" width="80" class="mr-md-6 mb-3 mb-md-0" style="border-radius: 10px; object-fit: cover;" alt="Product image" />
                <div class="flex-grow-1 d-flex flex-column justify-space-between">
                  <div class="mb-2">
                    <div class="text-subtitle-1 font-weight-bold mb-1">{{ product.title }}</div>
                    <div class="d-flex flex-wrap align-center mb-1" v-if="product.variants && product.variants.length">
                      <div v-for="(el, index) in filteredVariants(product.variants)" :key="index" class="mr-3 text-caption text-grey-darken-1">
                        <span v-if="el">{{ Object.keys(el)[0] }}: {{ Object.values(el)[0] }}</span>
                      </div>
                    </div>
                    <div class="text-caption text-grey-darken-1">Quantity: <span class="font-weight-bold">x{{ product.quantity }}</span></div>
                  </div>
                  <div class="d-flex align-center mt-2 flex-wrap">
                    <div class="text-h6 mr-2">
                      <span v-if="orderDetail?.discount?.code && Number(product?.original_price)?.toFixed(2) !== Number(product?.total)?.toFixed(2)">
                        <del class="text-grey-darken-1">{{ orderDetail.currency_symbol }}{{ (product.original_price * product.quantity).toFixed(2) }}</del>
                      </span>
                      <span class="ml-1">
                        {{ orderDetail.currency_symbol }}
                        <del v-if="Number(product.original_price) >Number(product.price)">{{ product.original_price }}</del>
                        {{ Number(product.total).toFixed(2) }}</span>
                    </div>
                    <v-btn v-if="product.product_type === 2" @click="callRedirect(product.id)" color="primary" class="ml-auto mt-2 mt-md-0" height="36" rounded>
                      <span class="button-text">Access Now</span>
                    </v-btn>
                  </div>
                </div>
              </v-sheet>
              <div v-if="idx < products.length - 1" class="my-4" />
            </div>
          </div>
        </v-col>
        <v-col cols="12" lg="6" md="6" sm="12"> </v-col>
      </v-row>
      <v-row>
        <v-col cols="12" lg="6" md="6" sm="12">
          <v-divider class="border-opacity-100" color="grey"></v-divider>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="6" lg="3" md="3" sm="3">
          <p v-if="
            (orderDetail.discount && orderDetail.discount.code) &&
            Number(taxValue).toFixed(2) !==
            Number(totalOriginalPrice).toFixed(2)
          ">
            <strong>Cart Total: </strong>
          </p>
          <p v-if="orderDetail.discount && orderDetail.discount.code &&
            Number(totalOriginalPrice.toFixed(2)) -
            Number(taxValue.toFixed(2)) !=
            0
          ">
            <strong>Coupon Discount:
              <span v-if="orderDetail.discount && orderDetail.discount.code">
                {{ orderDetail.discount.code }}
              </span></strong>
          </p>
          <p>
            <strong>Subtotal: </strong>
          </p>
          <p v-if="taxValue > 0 && orderDetail.currency_name !== 'USD'">
            <strong>Tax:</strong>
          </p>
          <p><strong>Shipping:</strong></p>
          <p><strong>Total:</strong></p>
        </v-col>
        <v-col cols="6" lg="3" md="3" sm="3" class="text-right">
          <!-- cart total -->
          <p v-if="
            (orderDetail.discount && orderDetail.discount.code) &&
            Number(taxValue).toFixed(2) !==
            Number(totalOriginalPrice).toFixed(2)
          ">
            {{ totalOriginalPrice.toFixed(2) }}
          </p>
          <!-- discount -->
          <p v-if="orderDetail?.discount &&
            (Number(totalOriginalPrice) - Number(taxValue)).toFixed(2) != 0
          ">
            -{{ (Number(totalOriginalPrice) - Number(taxValue)).toFixed(2) }}
          </p>
          <!-- subtotal -->
          <p>
            {{ Number(taxValue).toFixed(2) }}
          </p>
          <!-- tax -->
          <p v-if="
            taxValue > 0 && orderDetail.currency_name !== 'USD'
            // (
            //   Number(orderDetail?.total).toFixed(2) -
            //   Number(taxValue).toFixed(2)
            // ).toFixed(2) > 0
          ">
            {{
              (
                Number(orderDetail?.total).toFixed(2) -
                Number(taxValue).toFixed(2)
              ).toFixed(2)
            }}
          </p>
          <!-- shipping -->
          <p>{{ orderDetail.shipping || "-" }}</p>
          <!-- total -->
          <p>
            <strong>
              {{ orderDetail.currency_symbol }} {{ orderDetail.total }}</strong>
          </p>
        </v-col>
      </v-row>
      <br />
      <v-row>
        <v-btn v-if="orderDetail.payment_status == 'PAYMENT_SUCCESS'" @click="downloadInvoice()" color="white"
          class="view-order mb-2 mr-2" height="50">
          <span>Download Invoice</span>
        </v-btn>
        <v-btn v-if="
          orderDetail.payment_status == 'PAYMENT_SUCCESS' &&
          orderDetail.order_status == 'PLACED' &&
          orderDetail.is_cancellable
        " @click="triggeringCancelOrder()" color="red" height="50">
          <span>CANCEL</span>
        </v-btn>

        <v-btn v-if="orderType == 'COURSE' || orderType == 'PAID_VIDEO'" @click="goToProduct()" color="white"
          class="view-order mb-2" height="50">
          <span class="button-text">Access Now</span>
        </v-btn>
      </v-row>
      <v-dialog transition="dialog-top-transition" v-model="dialogState" width="380" height="200">
        <v-card v-if="dialogState" class="dialogBoxCard">
          Are you sure you want to cancel this order?
          <div class="dialogContent">
            <v-btn @click="cancelOrder()" rounded class="dialogcancelBTNYes">
              Yes
            </v-btn>
            <v-btn @click="triggeringCancelOrderClose()" rounded class="dialogcancelBTNNo">
              No
            </v-btn>
          </div>
        </v-card>
      </v-dialog>
    </div>

  </template>
   <div v-else class="flex flex-col justify-center items-center text-center" style="display: flex; flex-direction: column; align-items: center; height: calc(100dvh - 50px);">
    <v-icon size="48" color="gray">mdi-cart-outline</v-icon>
    <p class="text-lg mt-2">There is no Order</p>
    <v-btn color="primary" class="mt-4" @click="$router.push('/')">
      Browse Products
    </v-btn>
  </div>
  <Snackbar ref="snackbarRef" />

</template>

<script setup>
import { sendDataToParent } from "@/helper/common";
import { downloadFile } from "@/services/fileService";
import { getOrderDetail } from "@/services/merchService";
import { orderStatusCancel } from "@/services/productService";
import { getUserToken, setUserToken } from "@/services/userService";
import { onBeforeMount, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import Snackbar from "@/components/Snackbar.vue";


const router = useRouter();
const route = useRoute();

const orderId = route.params.id;
const orderType = route.query.order_type;

const callRedirect = (id)=>{
  router.push({
      path: `/course/${id}`,
    });
  
}

const orderDetail = ref(null);
const products = ref([]);
const isLoading = ref(true);
const dialogState = ref(false);

const taxValue = ref(0);
let totalOriginalPrice = ref(0);
const snackbarRef = ref(null);


const goToProduct = () => {
  if (orderType == "COURSE") {
    router.push({
      path: `/course/${orderDetail.value.product_id}`,
    });
  }

  if (orderType == "PAID_VIDEO") {
    router.push({
      path: `/paidVideo/${orderDetail.value.product_id}`,
    });
  }
};

const triggeringCancelOrder = () => {
  dialogState.value = true;
};
const triggeringCancelOrderClose = () => {
  dialogState.value = false;
};

const cancelOrder = async () => {
  isLoading.value = true;
  try {
    const response = await orderStatusCancel({
      order_id: orderId,
      order_type: orderType,
    });
    if (response.status === "success") {
      dialogState.value = false;
      isLoading.value = false;
      window.location.reload();
    }
  } catch (error) {
    console.error("Error in cancel order API", error);
     setTimeout(() => {
    snackbarRef?.value?.showSnackbar(
        `error in canceling order "${error?.message}"`,
        "red"
      );}, 0);
  }
};

const downloadInvoice = async () => {
  try {
    const response = await downloadFile(orderDetail.value.invoice_url);
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const invoiceNumber = products.value[0].title;
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute(
      "download",
      `${invoiceNumber}-Invoice.pdf`
    );
    document.body.appendChild(link);
    link.click();
  } catch (error) {
    console.error("Error downloading the file: ", error);
     setTimeout(() => {
    snackbarRef?.value?.showSnackbar(
        `error in downloading the file "${error?.message}"`,
        "red"
      );}, 0);
  }
};

const filteredVariants = (variants) => {
  return variants.filter((variant) => {
    const key = Object.keys(variant)[0];
    const value = variant[key];
    return key && value; // Keep only variants with valid key-value pairs
  });
};

const variantKey = (variant) => Object.keys(variant)[0];
const variantValue = (variant) => variant[variantKey(variant)];

onBeforeMount(async () => {
  try {
    if (!orderId || !orderType) {
      return null;
    }
    if (route.query.token) {
      setUserToken(route.query.token);
    } else if (!getUserToken()) {
      sendDataToParent({ message: "Login Needed" });
      router.push({
        path: `/orderDetail/${orderId}`,
        query: {
          toSend: `/orderHistory`,
          query: {
            order_type: orderType,
          },
        },
      });
      return;
    }

    const resp = await getOrderDetail(orderId, orderType);
    
if(resp.detail && resp.products){
  isLoading.value = false;
  orderDetail.value = resp.detail;
  products.value = resp.products;

  taxValue.value = products?.value?.reduce((totalTax, product) => {
    const final = totalTax + Number(product.subtotal);
    return Number(final);
  }, 0);

  totalOriginalPrice.value = products.value.reduce((total, product) => {
    return (
      total +
      parseFloat(
        Number(product.original_price) / (1 + Number(product.tax) / 100)
      )
    );
  }, 0);
}else{
  isLoading.value = false;
console.log("error");

}
  } catch (error) {
    isLoading.value = false;
    console.error("Error in order detail API", error);
     setTimeout(() => {
    snackbarRef?.value?.showSnackbar(
        `error in order Details "${error?.message}"`,
        "red"
      );}, 0);
  }
});
</script>

<style scoped>
.image-front {
  border-radius: 10px;
  max-width: 100px;
  max-height: 100px;
}

.v-table .v-table__wrapper>table>thead>tr>th {
  border: 0px;
}

.image-container {
  position: relative;
  width: 100px;
  height: 100px;
  overflow: hidden;
  border-radius: 8px; 
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
}

.view-order {
  border-radius: 30px !important;
  font-weight: bold;
  color: black !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 0 7px 0 20px !important;
  position: relative !important;
  overflow: hidden !important;
}

.dialogBoxCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: white;
  color: #000;
  border-radius: 12px;
  font-family: Helvetica, Arial, sans-serif;
}

.dialogContent {
  display: flex;
  justify-content: center;
  gap: 10px;
  width: 100%;
}

.dialogcancelBTNYes {
  background-color: black;
  color: white;
  padding: 10px 20px;
  font-size: 14px;
}

.dialogcancelBTNNo {
  background-color: red;
  color: white;
  padding: 10px 20px;
  font-size: 14px;
}

@media (max-width: 600px) {
  .show-on-mobile {
    display: block;
  }
}

@media (min-width: 601px) {
  .show-on-mobile {
    display: none;
  }
}

@media (max-width: 600px) {
  .hide-on-mobile {
    display: none;
  }
}

@media (min-width: 601px) {
  .hide-on-mobile {
    display: block;
  }
}
</style>
