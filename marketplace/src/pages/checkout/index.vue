<template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular :size="70" :width="7" color="white" indeterminate></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <v-container v-else class="py-8">
    <v-row class="ma-0">
      <v-col cols="12" md="8" class="pr-md-6">
        <v-card class="mb-6 transparent pa-4" flat>
          <h2 class="text-h5 white--text mb-2">Your Order</h2>
          <div class="pa-2">
            <v-card v-for="(item, index) in items" :key="index" class="mb-3 rounded-lg"
              style="background-color: rgba(255,255,255,0.04); border: 1px solid rgba(255,255,255,0.08); padding: 10px; margin-bottom: 24px !important;"
              flat>
              <v-row no-gutters>
                <v-col cols="4" class="d-flex align-center justify-center">
                  <v-img :src="item.images[0]" height="150" contain></v-img>
                </v-col>

                <v-col cols="8" class="px-3 py-2 d-flex flex-column justify-space-between">
                  <div>
                    <div class="text-body-2 font-weight-medium grey--text text--lighten-1">
                      {{ getArtistName(Number(item.artist_id)) }}
                    </div>
                    <div class="text-body-1 font-weight-bold white--text">
                      {{ item.name }}
                    </div>

                    <v-row v-if="Object.values(item.variants)[0]" dense class="mt-1">
                      <v-col v-for="(el, index) in filteredVariants(item.variants)" :key="index" cols="auto">
                        <div class="text-caption grey--text text--lighten-1" v-if="el">
                          {{ Object.keys(el)[0] }}: {{ Object.values(el)[0] }}
                        </div>
                      </v-col>
                    </v-row>
                  </div>

                  <div class="d-flex justify-space-between align-center mt-3">
                    <div class="d-flex align-center" style="gap: 6px;">
                      <v-btn icon variant="text" density="compact" @click="updateQuantity('-1', item.id)">
                        <v-icon size="16" class="grey--text text--lighten-1 quantity-action">mdi-minus</v-icon>
                      </v-btn>
                      <span class="text-body-8 white--text">{{ item.cart_quantity }}</span>
                      <v-btn icon variant="text" density="compact" @click="updateQuantity('1', item.id)">
                        <v-icon size="16" class="grey--text text--lighten-1 quantity-action">mdi-plus</v-icon>
                      </v-btn>
                    </div>

                    <span class="text-body-2 white--text">
                      {{ currencySymbol }}
                      {{ formatIndianCurrency(item.price_per_qty * item.cart_quantity) }}
                    </span>
                  </div>
                </v-col>
              </v-row>
            </v-card>
          </div>
        </v-card>

        <!-- Shipping Address Section -->
        <v-card class="mb-6 transparent" flat>
          <h3 class="text-h6 white--text mb-2 pa-4">Shipping Address</h3>
          <v-card class="pa-4 rounded-lg ma-4 mt-0" style="background: rgba(255, 255, 255, 0.05);">
            <div class="shipping-address-container"
              style="display: flex; justify-content: space-between; flex-wrap: wrap; gap: 16px;">
              <!-- Address Details -->
              <div class="address-details" style="flex: 1 1 65%; color: white;">
                <div class="text-h6">{{ address.first_name }} {{ address.last_name }}</div>
                <div>{{ address.address_line_one }}</div>
                <div v-if="address.address_line_two">{{ address.address_line_two }}</div>
                <div v-if="address.address_line_three">{{ address.address_line_three }}</div>
                <div>
                  {{ address.city }}, {{ address.state }}, {{ address.country }} - {{ address.pincode }}
                </div>
                <div>{{ address.phone }}</div>
                <div>{{ address.email }}</div>
              </div>

              <!-- Action Buttons -->
              <div class="address-actions"
                style="display: flex; flex-direction: column; align-items: flex-end; justify-content: space-between; padding: 10px; gap: 8px;">
                <button @click="editAdress = true" class="action-button">
                  Edit
                </button>
                <button @click="showAddressPopup = true" class="action-button">
                  <v-icon size="15" class="mr-2">mdi-home-map-marker</v-icon>
                  Choose Another
                </button>
              </div>
            </div>
          </v-card>
        </v-card>
      </v-col>

      <!-- Order Summary Section -->
      <v-col cols="12" md="4">
        <v-card class="pa-6 rounded-lg" style="background: rgba(255, 255, 255, 0.05);">
          <h3 class="text-h6 white--text mb-4">Order Summary</h3>

          <!-- Price Breakdown -->
          <div class="mb-6">
            <div v-if="costBfrCoupon" class="d-flex justify-space-between mb-2">
              <span class="white--text">Cart Total:</span>
              <span class="white--text">{{ currencySymbol }} {{ formatIndianCurrency(costBfrCoupon) }}</span>
            </div>
            <div v-if="discountValue" class="d-flex justify-space-between mb-2">
              <span class="white--text">Coupon Discount:</span>
              <span class="white--text">-{{ currencySymbol }} {{ formatIndianCurrency(discountValue) }}</span>
            </div>
            <div class="d-flex justify-space-between mb-2">
              <span class="white--text">Subtotal:</span>
              <span class="white--text">{{ currencySymbol }} {{ formatIndianCurrency(getSubtotal()) }}</span>
            </div>
            <div v-if="tax > 0" class="d-flex justify-space-between mb-2">
              <span class="white--text">Tax:</span>
              <span class="white--text">{{ currencySymbol }} {{ formatIndianCurrency(tax) }}</span>
            </div>
            <div class="d-flex justify-space-between mb-2">
              <span class="white--text">Shipping:</span>
              <span class="white--text">Free</span>
            </div>
            <div v-if="selectedPaymentMethod === 'paypal'">
              <div class="d-flex justify-space-between mb-2">
                <span class="white--text">PayPal Fee (10%):</span>
                <span class="white--text">
                  {{ currencySymbol }} {{ formatIndianCurrency(getPaypalFee()) }}
                </span>
              </div>
            </div>

            <div class="mb-6 d-flex align-center justify-space-between flex-wrap" style="width: 100%;">
              <div class="d-flex align-center mb-2" style="width: 40%;" v-if="appliedCoupon">
                <span class="text-subtitle-1 font-weight-medium white--text">Coupon Code:</span>
              </div>
              <div v-if="couponDetail" class="ml-3 text-error text-body-2">
                {{ couponDetail }}
              </div>

              <div class="d-flex align-center justify-end" :style="{ width: appliedCoupon ? '50%' : '100%' }">
                <template v-if="appliedCoupon">
                  <v-chip class="ma-1" color="green lighten-2" text-color="white" close label style="font-weight: 500"
                    @click:close="removeCoupon" closable>
                    {{ appliedCoupon }}
                  </v-chip>
                </template>

                <template v-else>
                  <div style="display: flex; align-items: center; width: 100%;">
                    <input v-model="coupon" type="text" placeholder="Enter coupon" style="
                        border: 1px solid rgba(255, 255, 255, 0.3);
                        outline: none;
                        font-size: 14px;
                        padding: 6px 10px;
                        width: 100%;
                        background: transparent;
                        color: white;
                        border-right: none;
                        border-radius: 6px 0 0 6px;
                        height: 38px;
                      " />

                    <v-btn :disabled="coupon.length === 0" color="primary" class="text-capitalize" elevation="1"
                      @click="applyCoupon" style="
                        border-radius: 0 6px 6px 0;
                        min-width: 100px;
                        height: 38px;
                      ">
                      Apply
                    </v-btn>
                  </div>
                </template>
              </div>
            </div>

          </div>
          <v-divider class="my-4" dark></v-divider>
          <div class="d-flex justify-space-between">
            <span class="text-h6 white--text">Total:</span>
            <span class="text-h6 white--text">{{ currencySymbol }} {{ formatIndianCurrency(getTotal()) }}</span>
          </div>

          <!-- Payment Methods -->
          <v-container fluid>
            <div v-if="errors.pincode !== ''">{{ errors.pincode }}</div>

            <div style="display: flex; flex-direction: column; gap: 10px">
              <div v-if="selectedCurrency === 'USD'" style="display: flex; flex-direction: column; gap: 10px">
                <p>Select Payment Method:</p>
                <label style="display: flex; gap: 8px">
                  <input type="radio" v-model="selectedPaymentMethod" value="paypal"
                    @change="validateAndSubmit('paypal')" />
                  <span style="display: flex; align-items: center; gap: 8px">
                    <PaypalSvg />

                    Paypal<small style="color: white; font-size:0.8rem;">(10% extra fees)</small>
                  </span>
                </label>
                <label style="display: flex; gap: 8px">
                  <input type="radio" v-model="selectedPaymentMethod" value="payglocal" />
                  <span style="display: flex; align-items: center; gap: 7px">
                    <PglocalSvg />
                    International Credit/Debit Cards
                  </span>
                </label>
              </div>

              <div v-if="loadingForPayment && selectedPaymentMethod === 'paypal' && !PayPalButtonRendered">
                <div v-if="loadingForPayment && !PayPalButtonRendered"
                  style="text-align: center; padding: 6px; background-color: white; color: black;">
                  Loading...
                </div>
              </div>

              <v-btn v-if="selectedPaymentMethod === 'payglocal'" @click="validateAndSubmit('payglocal')" color="white"
                class="border" style="border-radius: 0; padding: 0; min-width: 0; height: auto;">
                <img src="@/assets/payglocal-logo.png" alt="PayGlocal" style="height: 32.5px; object-fit: contain;" />
              </v-btn>
            </div>

            <v-btn v-if="selectedCurrency === 'INR' && !PhonepeValue" color="white" class="m-2"
              @click="validateAndSubmit('phonepe')" style="display: flex;
                align-items: center;
                justify-content: center;
                padding: 10px 20px;
                background-color: white;
                color: black;
                font-weight: bold;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
                width: 100%;">
              <span style="display: flex; align-items: center; gap: 8px">
                <PhonepeSvg />
                PHONEPE
              </span>
            </v-btn>
            <div id="paypal-button-container" v-show="selectedPaymentMethod === 'paypal'" style="margin-top: 10px;">
            </div>
          </v-container>

        </v-card>
      </v-col>
    </v-row>

    <!-- Address Edit Dialog -->
    <v-dialog v-model="editAdress" max-width="600px">
      <v-card class="elevation-12 black pa-6">
        <v-card-title class="d-flex justify-space-between align-center mb-4">
          <span class="text-h5">Edit Address</span>
          <v-btn icon @click="editAdress = false" class="white--text">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <form @submit.prevent="saveAddress">
          <div class="d-flex mb-4">
            <div class="mr-2 flex-grow-1">
              <input v-model="tempAdd.first_name" type="text"
                :class="['dark-input', { 'error-input': errors.first_name }]" placeholder="First Name" required />
              <div v-if="errors.first_name" class="error-message">
                {{ errors.first_name }}
              </div>
            </div>
            <div class="ml-2 flex-grow-1">
              <input v-model="tempAdd.last_name" type="text"
                :class="['dark-input', { 'error-input': errors.last_name }]" placeholder="Last Name" required />
              <div v-if="errors.last_name" class="error-message">
                {{ errors.last_name }}
              </div>
            </div>
          </div>
          <div class="mb-4">
            <input v-model="tempAdd.phone" type="tel" :class="['dark-input', { 'error-input': errors.phone }]"
              placeholder="Phone Number" required />
            <div v-if="errors.phone" class="error-message">
              {{ errors.phone }}
            </div>
          </div>
          <div class="mb-4">
            <input v-model="tempAdd.address_line_one" type="text" :class="[
              'dark-input',
              { 'error-input': errors.address_line_one },
            ]" placeholder="Address line 1" required />
            <div v-if="errors.address_line_one" class="error-message">
              {{ errors.address_line_one }}
            </div>
          </div>
          <input v-model="tempAdd.address_line_two" type="text" class="dark-input mb-4" placeholder="Address line 2" />
          <input v-model="tempAdd.address_line_three" type="text" class="dark-input mb-4"
            placeholder="Address line 3" />
          <div class="d-flex mb-4">
            <div class="mr-2 flex-grow-1">
              <input v-model="tempAdd.city" type="text" :class="['dark-input', { 'error-input': errors.city }]"
                placeholder="City" required />
              <div v-if="errors.city" class="error-message">
                {{ errors.city }}
              </div>
            </div>
            <div class="ml-2 flex-grow-1">
              <input v-model="tempAdd.pincode" type="text" :class="['dark-input', { 'error-input': errors.pincode }]"
                placeholder="Pincode" required />
              <div v-if="errors.pincode" class="error-message">
                {{ errors.pincode }}
              </div>
            </div>
          </div>
          <div class="d-flex mb-4">
            <div class="mr-2 flex-grow-1">
              <input v-model="tempAdd.state" type="text" :class="['dark-input', { 'error-input': errors.state }]"
                placeholder="State" required />
              <div v-if="errors.state" class="error-message">
                {{ errors.state }}
              </div>
            </div>
            <div class="ml-2 flex-grow-1">
              <input v-model="tempAdd.country" type="text" :class="['dark-input', { 'error-input': errors.country }]"
                placeholder="Country" required />
              <div v-if="errors.country" class="error-message">
                {{ errors.country }}
              </div>
            </div>
          </div>
          <div class="mb-4">
            <input v-model="tempAdd.email" type="email" :class="['dark-input', { 'error-input': errors.email }]"
              placeholder="Email" required />
            <div v-if="errors.email" class="error-message">
              {{ errors.email }}
            </div>
          </div>
          <v-btn type="submit" class="white--text" color="primary" block>
            Save Address
          </v-btn>
        </form>
      </v-card>
    </v-dialog>

    <!-- Address Selection Dialog -->
    <v-dialog v-model="showAddressPopup" max-width="600px">
      <v-card class="elevation-12 black pa-6">
        <ManageAddress />
        <v-btn @click="handleSaveAddress" class="white--text" color="primary" block>
          Save
        </v-btn>
      </v-card>
    </v-dialog>

    <!-- Calendly Confirmation Dialog -->
    <v-dialog v-model="popupShowConfirm" v-if="scheduling_URL" max-width="500">
      <v-card class="pa-6">
        <v-toolbar dark color="transparent">
          <v-btn icon dark @click="popupShowConfirm = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="text-p text-center">
          You will be redirected to Calendly, please fill in the required details and submit. After submitting your
          details, you will be redirected to Artisteverse to complete your payment.
          <br /><br />
          Your slot will be confirmed only after the payment is successfully processed on Artisteverse.
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn variant="flat" text="OK" @click="closeDialog" color="surface-variant"></v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Calendly Popup -->
    <v-dialog v-model="popupShow" transition="dialog-bottom-transition" fullscreen v-if="scheduling_URL">
      <v-toolbar dark color="transparent">
        <v-btn icon dark @click="toggle">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card title="Schedule The Meeting">
        <CalendlyPopup :url="scheduling_URL" />
      </v-card>
    </v-dialog>

    <Snackbar ref="snackbarRef" />
  </v-container>
</template>

<script setup>
import PglocalSvg from "@/assets/PayglocalSvg.vue";
import PaypalSvg from "@/assets/PaypalSvg.vue";
import PhonepeSvg from "@/assets/PhonePeSvg.vue";
import { formatIndianCurrency, getArtistName } from "@/helper/common";
import {
  cancelOrder,
  getCart,
  placeOrder,
  placeOrderPaypal,
  updateBulkCartAPI,
} from "@/services/productService";
import { getUserAddress } from "@/services/userService";
import { ref, watch, onBeforeMount, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useAppStore } from "@/stores/app";
import ManageAddress from "@/pages/profile/manageAddress/manageAddress.vue";
import Snackbar from "@/components/Snackbar.vue";

const appStore = useAppStore();
const router = useRouter();

let isLoading = ref(true);
let address = ref({
  first_name: "",
  last_name: "",
  country: "",
  address_line_one: "",
  address_line_two: "",
  address_line_three: "",
  city: "",
  state: "",
  pincode: "",
  phone: "",
  email: "",
});
const snackbarRef = ref(null);

let tempAdd = ref(JSON.parse(JSON.stringify(address.value)));

const respaddress = ref(null);
const selectedPaymentMethod = ref("");

let items = ref([]);
let currency = ref("INR");
let currencySymbol = ref("₹");
let tax = ref(0);
let costBfrCoupon = ref(null);
let discountValue = ref(null);
let paymentDeclined = ref(false);
const couponDetail = ref(null);
let orderId = null;
let mTxnId = null;
const orderCache = {
  response: null,
  paymentMethod: null,
};

let errors = ref({});
const showAddressPopup = ref(false)
const editAdress = ref(false);


function handleSaveAddress() {
  showAddressPopup.value = false
  setTimeout(() => {
    window.location.reload()
  }, 300)
}

const coupon = ref("");
const appliedCoupon = ref(null);

const selectedCurrency = appStore.currencyCode;

const PhonepeValue = ref(false);

const loadingForPayment = ref(false);
const PaypalValue = ref(false);
const PayPalButtonRendered = ref(false);
const isAddressFormReady = ref(false);

// onMounted(async () => {
//   const unwatch = watch(isAddressFormReady, async (ready) => {
//     if (ready && selectedCurrency === "USD") {
//       await validateAndSubmit('paypal');
//       unwatch();
//     }
//   });
// });

const payPalButtonRenderSet = () => {
  PayPalButtonRendered.value = true;
}

const processOrderResponse = (response, paymentMethod) => {
  const { selectedPayment } = response.data;

  orderId = selectedPayment.id;
  mTxnId = selectedPayment.order_id;

  switch (paymentMethod) {
    case "phonepe":
      isLoading.value = false;
      openPayment(selectedPayment.url);
      break;
    case "paypal":
      if (!PayPalButtonRendered.value) {
        setupPayPalButton(selectedPayment.order_id);
        payPalButtonRenderSet();
      }
      break;
    case "payglocal":
      openPaymentForPayGlocal(selectedPayment.url);
      break;
    default:
      console.error(`Unknown payment method: ${paymentMethod}`);
  }
};

const isAddressChanged = computed(() => {
  return JSON.stringify(respaddress.value) !== JSON.stringify(address.value);
});

const applyCoupon = async () => {
  isLoading.value = true;
  if (coupon.value !== "") {
    appliedCoupon.value = coupon.value.toUpperCase();
    coupon.value = "";
    try {
      const resp = await getCart(
        undefined,
        selectedCurrency === "USD" ? "currency=2" : "currency=1",
        `coupon=${appliedCoupon.value}`
      );

      if (resp.length > 0) {
        if (resp.some((product) => product.offer_applied)) {

          items.value = resp;
          currency.value = resp[0].currency;
          currencySymbol.value = resp[0].currency_symbol;
          couponDetail.value = "";
          isLoading.value = false;
          orderCache.response = null
          orderCache.paymentMethod = null
          PayPalButtonRendered.value = false;
          selectedPaymentMethod.value = "";
        } else {
          couponDetail.value = "Invalid Coupon";
          appliedCoupon.value = null;
          isLoading.value = false;
          setTimeout(() => {
        snackbarRef?.value?.showSnackbar(
          `Invalid Coupon`,
          "red"
        );
      }, 0);
        }
      } else {
        couponDetail.value = "Invalid Coupon";
        appliedCoupon.value = null;
        isLoading.value = false;
        setTimeout(() => {
        snackbarRef?.value?.showSnackbar(
          `Invalid Coupon`,
          "red"
        );
      }, 0);
      }
    } catch (error) {
      console.error("Error in applying coupon", error);
      couponDetail.value = "Invalid Coupon";
      appliedCoupon.value = null;
      isLoading.value = false;

       setTimeout(() => {
        snackbarRef?.value?.showSnackbar(
          `error in applying coupon "${error?.message} || error"`,
          "red"
        );
      }, 0);
    }
  }
};

const removeCoupon = async () => {
  isLoading.value = true;

  appliedCoupon.value = null;
  const resp = await getCart(
    undefined,
    selectedCurrency === "USD" ? "currency=2" : "currency=1"
  );
  if (resp) {
    isLoading.value = false;
    costBfrCoupon.value = null;
    discountValue.value = null;
    items.value = resp;
    currency.value = resp[0].currency;
    currencySymbol.value = resp[0].currency_symbol;
    selectedPaymentMethod.value = "";
    orderCache.response = null
    orderCache.paymentMethod = null
    PayPalButtonRendered.value = false
    isLoading.value = false;
  } else {
    isLoading.value = false;
  }
};

function callback(response) {
  if (response === "USER_CANCEL") {
    paymentDeclined.value = true;
    cancelOrder({
      mTxnId,
    });
    return;
  } else if (response === "CONCLUDED") {
    router.push({
      path: `/orderDetail/${orderId}`,
      query: {
        order_type: "PRODUCT",
      },
    });
    return;
  }
}

const openPayment = (tokenUrl) => {
  try {
    window.PhonePeCheckout.transact({
      tokenUrl,
      callback,
      type: "IFRAME",
    });
  } catch (error) {
    console.error("Error in opening payment", error);
  }
};

const updateArray = (arr, id, updatedData) => {
  return arr.map((item) =>
    item.id === id ? { ...item, ...updatedData } : item
  );
};
const updateQuantity = async (num, id) => {
  isLoading.value = true;

  const data = items.value.filter((el) => {
    if (el.id == id) {
      return true;
    }
  });
  let quantity = data[0].cart_quantity;
  quantity += +num;
  !isNaN(quantity) && quantity > 0 ? quantity : (quantity = 0);
  const newArray = updateArray(items.value, id, {
    cart_quantity: quantity,
  });
  items.value = newArray;
  const toUpdate = items.value.map((el) => {
    // console.log(el);

    return {
      productItemId: el.id,
      quantity: el.cart_quantity,
      currency: selectedCurrency === "INR" ? 1 : 2,
    };
  });
  console.log(toUpdate)
  const resp = await updateBulkCartAPI({
    products: toUpdate,
  });

  if (resp.status) {
    isLoading.value = false;
    getSubtotal();
  } else {
    isLoading.value = false;
    console.error("Error in updating cart", resp);
  }


};

const getSubtotal = () => {
  const beforeTaxTotals = items.value.map(el => {
    const full = el.price_per_qty * el.cart_quantity;
    return full / (1 + Number(el.tax) / 100);
  });

  const subtotal = beforeTaxTotals.reduce((a, b) => a + b, 0);

  const originalPriceTotals = items.value.map(el => {
    const full = el.originalPrice;
    return full / (1 + Number(el.tax) / 100);
  });

  const totalOriginal = originalPriceTotals.reduce((a, b) => a + b, 0);

  if (totalOriginal && items.value.some(p => p.offer_applied)) {
    costBfrCoupon.value = totalOriginal;
    discountValue.value = totalOriginal - subtotal;
  } else {
    costBfrCoupon.value = null;
    discountValue.value = null;
  }

  const originalTaxValues = items.value.map(el => {
    const full = el.price_per_qty * el.cart_quantity;
    const net = full / (1 + Number(el.tax) / 100);
    return net * (Number(el.tax) / 100);
  });

  const totalOriginalTax = originalTaxValues.reduce((a, b) => a + b, 0);

  tax.value =
    appliedCoupon.value && items.value.some(p => p.offer_applied)
      ? totalOriginalTax
      : (getBaseTotal() - subtotal).toFixed(2);

  return Number(subtotal);
};

const getBaseTotal = () => {
  return items.value
    .map(el => el.price_per_qty * el.cart_quantity)
    .reduce((sum, v) => sum + v, 0)
}

const getPaypalFee = () => {
  return selectedPaymentMethod.value === 'paypal'
    ? getBaseTotal() * 0.10
    : 0
}

const subtotal = computed(() => getSubtotal());

const getTotal = () => {
  return subtotal.value + (selectedCurrency === "INR" ? Number(tax.value) : 0) + getPaypalFee()
}

const validateForm = (address) => {
  errors.value = {};
  let isValid = true;

  const requiredFields = [
    "first_name",
    "phone",
    "address_line_one",
    "city",
    "pincode",
    "state",
    "country",
    "email",
  ];

  for (const field of requiredFields) {
    if (!address.value[field]) {
      errors.value[field] = `${field.replace("_", " ")} is required`;
      isValid = false;
    }
  }

  // Additional validation for email
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (address.value.email && !emailRegex.test(address.value.email)) {
    errors.value.email = "Invalid email format";
    isValid = false;
  }

  // Additional validation for phone (example: 10 digits)
  const phoneRegex = /^(\+91[\-\s]?)?[0]?(91)?\d{10}$/;

  if (
    selectedCurrency === "INR"
      ? address.value.phone && !phoneRegex.test(address.value.phone)
      : false
  ) {
    errors.value.phone = "Invalid phone number";
    isValid = false;
  }

  // Additional validation for pincode (example: 6 digits)
  const pincodeRegex = /^\d{6}$/;
  if (
    selectedCurrency === "INR"
      ? address.value.pincode && !pincodeRegex.test(address.value.pincode)
      : false
  ) {
    errors.value.pincode = "Invalid pincode (should be 6 digits)";
    isValid = false;
  }

  return isValid;
};

function callbackPaypal(response) {
  if (response === "USER_CANCEL") {
    paymentDeclined.value = true;
    cancelOrder({
      mTxnId,
    });
    return;
  } else if (response === "successfully") {
    router.push({
      path: `/orderDetail/${orderId}`,
      query: {
        order_type: "PRODUCT",
      },
    });
    return;
  }
}

function callbackForPayglocal(response) {
  if (response.status === "CUSTOMER_CANCELLED") {
    paymentDeclined.value = true;
    cancelOrder({
      mTxnId,
    });
    return;
  } else if (response.status === "SENT_FOR_CAPTURE") {
    router.push({
      path: `/orderDetail/${orderId}`,
      query: {
        order_type: "PRODUCT",
      },
    });
    return;
  }
}

const openPaymentForPayGlocal = (tokenUrl) => {
  try {
    window.PGPay.launchPayment(
      {
        redirectUrl: tokenUrl,
      },
      callbackForPayglocal
    );
  } catch (error) {
    console.error("Error in opening payment", error);
  }
};

const setupPayPalButton = (orderID) => {
  if (!window.paypal) {
    console.error("PayPal SDK not loaded");
    return;
  }
  window.paypal
    .Buttons({
      style: {
        shape: "rect",
        borderRadius: 0,
        color: "white",
        layout: "vertical",
        label: "paypal"
      },

      createOrder: () => {
        return orderID;
      },
      onApprove: async (data, actions) => {
        const order = await actions.order.capture();
        console.log("Order successfully processed:", order);
        callbackPaypal("successfully");
      },
      onCancel: (data) => {
        console.log("Order cancelled:", data);
        callbackPaypal("USER_CANCEL");
      },
      onError: (err) => {
        console.error("PayPal error:", err);
      },
    })
    .render("#paypal-button-container");
  loadingForPayment.value = false;
  PaypalValue.value = true;
};

const paymentMethodNum = {
  phonepe: 1,
  paypal: 2,
  payglocal: 3,
};

const saveAddress = () => {
  if (validateForm(tempAdd)) {
    address.value = { ...tempAdd.value };
    editAdress.value = false;
  }
};

const validateAndSubmit = async (paymentMethod) => {
  if (validateForm(address)) {
    loadingForPayment.value = true;
    try {
      // using cached response to avoid making a new order everytime this function is called
      let response;
      if (orderCache.response && orderCache.paymentMethod === paymentMethod) {
        return
      }

      // loadingForPayment.value = false;
      if (isAddressChanged.value) {
        response = await placeOrderPaypal({
          address: address.value,
          currency: selectedCurrency === "INR" ? 1 : 2,
          coupon: appliedCoupon.value,
          payment_method: paymentMethodNum[paymentMethod],
        });
      } else {
        response = await placeOrderPaypal({
          address_id: address.value.address_id,
          currency: selectedCurrency === "INR" ? 1 : 2,
          coupon: appliedCoupon.value,
          payment_method: paymentMethodNum[paymentMethod],
        });
      }

      orderCache.response = response;
      orderCache.paymentMethod = paymentMethod;
      processOrderResponse(response, paymentMethod);

      if (response && response.data) {
        if (paymentMethod === "phonepe") {
          let url = response.data.selectedPayment.url;
          orderId = response.data.selectedPayment.id;
          mTxnId = response.data.selectedPayment.order_id;
          isLoading.value = false;
          loadingForPayment.value = false;
          openPayment(url);
        }

        if (paymentMethod === "paypal" && !PayPalButtonRendered.value) {
          orderId = response.data.selectedPayment.id;
          mTxnId = response.data.selectedPayment.order_id;
          setupPayPalButton(response.data.selectedPayment.order_id);
          payPalButtonRenderSet();
          loadingForPayment.value = false;
        }

        if (paymentMethod === "payglocal") {
          orderId = response.data.selectedPayment.id;
          mTxnId = response.data.selectedPayment.order_id;
          openPaymentForPayGlocal(response.data.selectedPayment.url);
          loadingForPayment.value = false;
        }
      } else {
        console.error("Invalid API response from placeOrder");
      }
    } catch (error) {
      console.error("Error in placeOrder API", error);
    }
  } else {
    console.error("Form validation failed");
  }
};

const filteredVariants = (variants) => {
  return variants.filter((variant) => {
    const key = Object.keys(variant)[0];
    const value = variant[key];
    return key && value;
  });
};

const fetchCart = async () => {
  try {
    const resp = await getCart(
      undefined,
      selectedCurrency === "USD" ? "currency=2" : "currency=1"
    );

    items.value = resp;
    currency.value = resp[0].currency;
    currencySymbol.value = resp[0].currency_symbol;
    isLoading.value = false;
  } catch (error) {
    console.error("Error in product detail API", error);
  }
};

const fetchAddress = async () => {
  try {
    const resp = await getUserAddress();

    if (resp.address) {
      address.value = resp.address;
      respaddress.value = structuredClone(resp.address);
      address.value.country = "India";
      respaddress.value.country = "India";
    } else {
      respaddress.value = null;
      address.value = {
        first_name: "",
        last_name: "",
        country: "",
        address_line_one: "",
        address_line_two: "",
        address_line_three: "",
        city: "",
        state: "",
        pincode: "",
        phone: "",
        email: "",
      };
    }
    isAddressFormReady.value = true;
  } catch (error) {
    console.error("Error in product detail API", error);
  }
};

onBeforeMount(async () => {
  await fetchCart();
  await fetchAddress();
});
</script>

<style scoped>
.action-button {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: right;
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: white;
}

.quantity-action {
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 10px;
  border-radius: 50%;
}

.dark-input {
  width: 100%;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: white;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.dark-input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.08);
}

input[type="radio"] {
  cursor: pointer;
}

.error-input {
  border-color: red;
  box-shadow: 0 0 0 1px red;
}

.error-message {
  color: red;
  font-size: 12px;
  margin-top: 4px;
  padding-left: 20px;
}

.full-width-table {
  width: 100%;
  border-collapse: collapse;
  color: white;
}

.full-width-table td {
  padding: 8px 0;
}

.full-width-table tr:last-child td {
  padding-top: 16px;
  font-weight: bold;
}
</style>
