<template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular :size="70" :width="7" color="white" indeterminate></v-progress-circular>
      </v-col>
    </v-row>
  </template>

  <div class="ml-8" v-else>
    <v-row>
      <v-col>
        <div class="text-h6 font-weight-bold">Schedule Meeting</div>
        <div class="text-subtitle-1">When would you like to do this call?</div>
      </v-col>
    </v-row>

    <v-row>
      <v-col lg="6" md="6" sm="12" xs="12">
        <v-date-picker v-model="selectedDate" :allowed-dates="allowedDates" :min="minDate" :max="maxDate"
          :current="currentMonth" color="black" @update:model-value="onDateChange" scrollable hide-header
          show-adjacent-months></v-date-picker>
      </v-col>

      <v-col lg="6" md="6" sm="12" xs="12">
        <v-card max-width="400" class="pa-4" elevation="3"
          style="border-radius: 20px; background-color: white; color: black" v-if="selectedDate && timeSelected">
          <v-row>
            <v-col cols="2" class="d-flex align-center">
              <v-avatar size="40">
                <v-img :src="data.image" alt="avatar" />
              </v-avatar>
            </v-col>
            <v-col cols="10">
              <h4 class="mb-1">{{ data.name }}</h4>
              <p class="text-subtitle-1">{{ formattedDate }}</p>
            </v-col>
          </v-row>

          <v-row class="mt-4">
            <v-col cols="12">
              <strong>Day</strong>
              <p>{{ formattedDate }}</p>
              <strong>Time</strong>
              <p>{{ timeSelected }}</p>
              <strong>Duration</strong>
              <p>{{ data.duration.minutes }} Minutes</p>
            </v-col>
          </v-row>

          <v-row class="d-flex justify-center mt-5 mb-3">
            <v-btn @click="buyNow()" color="black" class="white--text rounded-pill" width="150">
              BOOK Now
            </v-btn>
          </v-row>
        </v-card>
      </v-col>
    </v-row>

    <v-row v-if="selectedDate">
      <v-col cols="12" lg="6" md="6" sm="6" xs="6">
        <div class="text-h6 font-weight-bold mb-2">Select Time</div>
        <div class="scroll-wrapper">
          <v-btn icon="mdi-chevron-left" @click="scrollLeft" class="scroll-arrow left-arrow"
            :disabled="isScrolledToStart">
            <v-icon>mdi-chevron-left</v-icon>
          </v-btn>
          <div class="chips-container" ref="chipsContainer">
            <div class="chips-inner">
              <v-chip v-for="time in slotTimes" :key="time" class="ma-2"
                :class="{ 'active-time-chip': time === timeSelected }" outlined @click="selectTime(time)">
                {{ time }}
              </v-chip>
            </div>
          </div>
          <v-btn icon="mdi-chevron-right" @click="scrollRight" class="scroll-arrow right-arrow"
            :disabled="isScrolledToEnd">
            <v-icon>mdi-chevron-right</v-icon>
          </v-btn>
        </div>
      </v-col>
    </v-row>
  </div>

  <Snackbar ref="snackbarRef" />
</template>

<script setup>
import { onBeforeMount, onMounted, onUnmounted, ref, computed } from "vue";
import dayjs from "dayjs";
import { useRoute, useRouter } from "vue-router";
import { getAvailableSlot } from "@/services/calendly";
import Snackbar from "@/components/Snackbar.vue";
import { useAppStore } from "@/stores/app";

const appStore = useAppStore();
const selectedDate = ref(null);
const timeSelected = ref(null);
const slotTimes = ref([]);
const currentMonth = ref(dayjs().format("YYYY-MM-DD"));
const minDate = ref(dayjs().format("YYYY-MM-DD"));
const maxDate = ref(dayjs().endOf("year").format("YYYY-MM-DD"));
const chipsContainer = ref(null);
const isScrolledToStart = ref(true);
const isScrolledToEnd = ref(false);
const data = ref(null);
const availableDates = ref({});
const snackbarRef = ref(null);
const router = useRouter();
const route = useRoute();
const artistId = route.params.id;
const CurrencyCode = appStore.currencyCode;
let formId = null;

// Memoized allowed dates set
const allowedDateSet = computed(() =>
  new Set(Object.keys(availableDates.value || {}))
);

const allowedDates = (date) => {
  const formattedDate = new Date(date).toLocaleDateString("en-US");
  return allowedDateSet.value.has(formattedDate);
};

const formatDate = (date) => new Date(date).toLocaleDateString("en-US");

const formattedDate = computed(() => {
  return selectedDate.value
    ? dayjs(selectedDate.value).format("DD, MMMM, YYYY / dddd")
    : "";
});

const onDateChange = (date) => {
  const formatted = formatDate(date);
  slotTimes.value = availableDates.value[formatted] || [];
  timeSelected.value = null;
};

const selectTime = (time) => {
  timeSelected.value = time;
};

const scrollLeft = () => {
  if (chipsContainer.value) {
    chipsContainer.value.scrollLeft -= 100;
    updateScrollState();
  }
};

const scrollRight = () => {
  if (chipsContainer.value) {
    chipsContainer.value.scrollLeft += 100;
    updateScrollState();
  }
};

const updateScrollState = () => {
  if (chipsContainer.value) {
    isScrolledToStart.value = chipsContainer.value.scrollLeft <= 0;
    isScrolledToEnd.value =
      chipsContainer.value.scrollLeft + chipsContainer.value.clientWidth >=
      chipsContainer.value.scrollWidth;
  }
};

function convertToIST(slots) {
  const result = {};
  slots.forEach((slot) => {
    const utc = new Date(slot.start_time);
    const ist = new Date(utc.getTime() + 5.5 * 60 * 60 * 1000);
    const dateKey = `${ist.getMonth() + 1}/${ist.getDate()}/${ist.getFullYear()}`;
    const timeString = utc.toLocaleTimeString("en-IN", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
    if (!result[dateKey]) result[dateKey] = [];
    result[dateKey].push(timeString);
  });
  return result;
}

function getSchedulingURL(date, time, slots) {
  const selectedDate = new Date(date + " " + time);
  const selectedUTCString = selectedDate.toISOString().slice(0, -5) + "Z";
  const slot = slots.find((slot) => slot.start_time === selectedUTCString);
  return slot ? slot.scheduling_url : "No slot available at this time.";
}

const buyNow = () => {
  const scheduling_URL = getSchedulingURL(
    dayjs(selectedDate.value).format("MM/DD/YYYY"),
    timeSelected.value,
    data.value.slots.collection
  );

  const selectedCurrency = data.value.currencies.find(
    (currency) => currency.currency_id === (CurrencyCode === "INR" ? 1 : 2)
  );

  router.push({
    path: `/singleCheckout/${artistId}`,
    query: {
      productId: formId,
      slotTime: timeSelected.value,
      slotDate: dayjs(selectedDate.value).format("MM/DD/YYYY"),
      image: data.value.image,
      name: data.value.name,
      tax: selectedCurrency.gst,
      price: selectedCurrency.price,
      currency: CurrencyCode === "INR" ? "₹" : "$",
      quantity: 1,
      type: 2,
      scheduledURL: scheduling_URL,
    },
  });
};

const isLoading = ref(true);
const fetchSlots = async () => {
  isLoading.value = true;
  try {
    const response = await getAvailableSlot({ artistMeetId: artistId });
    data.value = response;
    availableDates.value = convertToIST(response.slots.collection);
    formId = response.source_id;
  } catch (error) {
     setTimeout(() => {
    snackbarRef?.value?.showSnackbar(
      `Error getting available slots: ${error?.message}`,
      "red"
    );}, 0);
  } finally {
    isLoading.value = false;
  }
};

onBeforeMount(fetchSlots);

onMounted(() => {
  updateScrollState();
  window.addEventListener("resize", updateScrollState);
});

onUnmounted(() => {
  window.removeEventListener("resize", updateScrollState);
});
</script>

<style scoped>
.scroll-wrapper {
  position: relative;
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
}

.chips-container {
  flex-grow: 1;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  scroll-behavior: smooth;
}

.chips-container::-webkit-scrollbar {
  display: none;
}

.chips-inner {
  display: inline-flex;
  gap: 8px;
  padding: 0 8px;
}

.scroll-arrow {
  flex-shrink: 0;
  z-index: 1;
  background-color: rgba(255, 255, 255, 0.8);
}

.active-time-chip {
  background-color: #3f51b5 !important;
  color: white !important;
}

.white--text {
  color: white !important;
}

.v-btn {
  font-weight: bold;
}

.v-card {
  border-radius: 15px;
  max-width: 350px;
}

.v-avatar img {
  object-fit: cover;
}
</style>
