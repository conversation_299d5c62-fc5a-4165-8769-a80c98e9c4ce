<template>
  <template v-if="isSucess">
    <div style="
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
      ">
      <p class="text-color text-center text-h5 ma-2">
        We have received your request and we will get back to you shortly
      </p>
    </div>
  </template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular :size="70" :width="7" color="white" indeterminate></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <div v-else-if="meetId != '11'" class="container">
    <div class="error-card">
      <v-alert type="error" prominent border="left">
        <div class="text-h5 mb-2">Invalid Data</div>
        <p>Form not available</p>
      </v-alert>
    </div>
  </div>
  <div class="container" v-else-if="!isLoading && !isSucess">
    <div class="text-h4 mb-6 text-center">Practitioner Application Form</div>
    <v-row>
      <!-- Left Section - Personal Details -->
      <v-col cols="12" lg="6" md="6" sm="12" xs="12">
        <v-card color="transparent" flat class="form-card">
          <v-card-title class="white--text text-h6">Personal Details</v-card-title>
          <v-card-text>
            <CustomInput v-model="personalForm.name.value" :placeholder="personalForm.name.title"
              :name="personalForm.name.title" type="text" :required="true" />
            <CustomInput v-model="personalForm.age.value" :placeholder="personalForm.age.title"
              :name="personalForm.age.title" type="text" custom-class="mb-6 mt-3" :required="true" />
            <CustomInput v-model="personalForm.email.value" :placeholder="personalForm.email.title"
              :name="personalForm.email.title" type="text" custom-class="mb-6 mt-3" :required="true" />
              <div v-if="CurrencyCode === 'INR'">
                <div class="text-subtitle-1 mb-2">Select the batch:</div>
                <v-radio-group v-model="personalForm.batch.value" row class="align-top">
                  <v-radio value="Tuesdays and Thursdays : 6:30 - 8:00 am IST" color="white" class="align-top">
                    <template v-slot:label>
                      <div>
                        <div class="text-subtitle-2 font-weight-bold">Batch 1</div>
                        <div class="text-body-2">Timings - Tuesdays and Thursdays : 6:30 - 8:00 am IST</div>
                        <div class="text-caption text-grey-lighten-1">Starting from July 8</div>
                      </div>
                    </template>
                  </v-radio>
                  <v-radio value="Wednesdays & Fridays : 3:30 - 5:00 pm IST" color="white" class="align-top">
                    <template v-slot:label>
                      <div>
                        <div class="text-subtitle-2 font-weight-bold">Batch 2</div>
                        <div class="text-body-2">Timings - Wednesdays & Fridays : 3:30 - 5:00 pm IST</div>
                        <div class="text-caption text-grey-lighten-1">Starting from July 9</div>
                      </div>
                    </template>
                  </v-radio>
                </v-radio-group>
              </div>
              <div v-if="CurrencyCode === 'USD'">
                <div class="text-subtitle-1 mb-2">Select the batch:</div>
                <v-radio-group v-model="personalForm.batch.value" row class="align-top">
                  <v-radio value="Tuesdays and Thursdays : 1:00 - 2:30 am GMT" color="white" class="align-top">
                    <template v-slot:label>
                      <div>
                        <div class="text-subtitle-2 font-weight-bold">Batch 1</div>
                        <div class="text-body-2">Timings - Tuesdays and Thursdays : 1:00 - 2:30 am GMT</div>
                        <div class="text-caption text-grey-lighten-1">Starting from July 8</div>
                      </div>
                    </template>
                  </v-radio>
                  <v-radio value="Wednesdays & Fridays : 12:00 - 1:30 pm CEST" color="white" class="align-top">
                    <template v-slot:label>
                      <div>
                        <div class="text-subtitle-2 font-weight-bold">Batch 2</div>
                        <div class="text-body-2">Timings - Wednesdays & Fridays : 12:00 - 1:30 pm CEST</div>
                        <div class="text-caption text-grey-lighten-1">Starting from July 9</div>
                      </div>
                    </template>
                  </v-radio>
                </v-radio-group>
              </div>
              <div>
                <div class="text-subtitle-1 mb-2">Kind of practitioner:</div>
                <v-radio-group v-model="personalForm.practitioner_type.value" row>
                  <v-radio label="Actor" value="Actor" color="white"></v-radio>
                  <v-radio label="Dancer" value="Dancer" color="white"></v-radio>
                </v-radio-group>
              </div>
            
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Right Section - Additional Information -->
      <v-col cols="12" lg="6" md="6" sm="12" xs="12">
        <v-card color="transparent" flat class="form-card">
          <v-card-title class="white--text text-h6">Additional Information</v-card-title>
          <v-card-text>
            <CustomInputArea v-model="personalForm.address.value" :placeholder="personalForm.address.title"
              :name="personalForm.address.title" type="textarea" custom-class="mb-6" :required="true" />
            <CustomInputArea v-model="personalForm.prior_experience.value"
              :placeholder="personalForm.prior_experience.title" :name="personalForm.prior_experience.title"
              type="textarea" custom-class="mb-6" :required="true" />
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12" class="text-center">
        <v-btn height="50" color="white" class="white--text rounded-xl px-8" @click="submitForm">
          Submit Application
        </v-btn>
        <div v-if="errorMessage" class="error-message mt-4">
          {{ errorMessage }}
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from "vue-router";
import { onBeforeMount, ref } from "vue";
import { getUserToken } from "@/services/userService";
import { meetRequest } from "@/services/bookClass";
import { useSlotStore } from "@/stores/slot";
import { useAppStore } from "@/stores/app";

const store = useSlotStore();
const appStore = useAppStore();
const CurrencyCode = appStore.currencyCode;
const router = useRouter();
const route = useRoute();
const meetId = route.params.id;

const errorMessage = ref("");
const isLoading = ref(false);
const isSucess = ref(false);
let isValid = true;

const personalForm = ref({
  name: {
    value: "",
    key: "name",
    title: "Full Name",
    order: 1,
  },
  address: {
    value: "",
    key: "address",
    title: "Address",
    order: 2,
  },
  age: {
    value: null,
    key: "age",
    title: "Age",
    order: 3,
  },
  practitioner_type: {
    value: "",
    key: "practitioner_type",
    title: "Kind of practitioner",
    order: 4,
  },
  prior_experience: {
    value: "",
    key: "prior_experience",
    title: "Prior experience in",
    order: 5,
  },
  email: {
    value: null,
    key: "email",
    title: "Email",
    order: 6,
  },
  batch: {
    value: null,
    key: "batch",
    title: "Batch",
    order: 6,
  },
});

const submitForm = async () => {
  if (meetId !== "11") {
    errorMessage.value =
      "Invalid Form ID. This form is only available for Form ID 11.";
    return;
  }

  if (!validateForm()) {
    return;
  }

  const responseData = {
    ...personalForm.value,
  };

  const toSend = {
    data: responseData,
    id: meetId,
  };

  try {
    isLoading.value = true;
    const res = await meetRequest(toSend);
    isLoading.value = false;

    checkout(meetId);

  } catch (error) {
    isLoading.value = false;
    errorMessage.value = "Error submitting the form. Please try again.";
    console.error("Error in practitioner request", error);
  }
};

const validateForm = () => {
  errorMessage.value = "";
  isValid = true;

  if (!personalForm.value.name.value) {
    isValid = false;
    errorMessage.value += "Name is required.\n";
  }

  if (!personalForm.value.address.value) {
    isValid = false;
    errorMessage.value += "Address is required.\n";
  }

  if (
    personalForm.value.age.value === null ||
    personalForm.value.age.value === "" ||
    isNaN(Number(personalForm.value.age.value)) ||
    Number(personalForm.value.age.value) <= 0
  ) {
    isValid = false;
    errorMessage.value += "Age must be a positive number.\n";
  }

  if (!personalForm.value.practitioner_type.value) {
    isValid = false;
    errorMessage.value += "Please select if you're an Actor or Dancer.\n";
  }

  if (!personalForm.value.prior_experience.value) {
    isValid = false;
    errorMessage.value += "Prior experience information is required.\n";
  }
  if (!personalForm.value.email.value) {
    isValid = false;
    errorMessage.value += "Email information is required.\n";
  }
  if (!personalForm.value.batch.value) {
    isValid = false;
    errorMessage.value += "Batch information is required.\n";
  }

  return isValid;
};

const checkout = async (id) => {
  let toPath = `/singleCheckout/${meetId}`;
  // let toPath = `/meetCheckout/${meetId}`;

  router.push({
    path: toPath,
    query: {
      productId: meetId,
      image: store.artistDetail.metadata.image,
      name: store.artistDetail.name,
      price: store.artistDetail.metadata.product[CurrencyCode === "INR" ? 0 : 1].price,
      tax: store.artistDetail.metadata.product[CurrencyCode === "INR" ? 0 : 1].tax,
      currency: CurrencyCode === "INR" ? "₹" : "$",
      image: store.artistDetail.image,
      quantity: 1,
      type: 2,
      slotTime: null,
      slotDate: null
    },
  });
};


onBeforeMount(async () => {
  if (getUserToken()) {
    await store.getPopupArtist(meetId);
  } else {
    await store.getPopupArtistOutLogin(meetId);
  }
});
</script>

<style scoped>
.container {
  margin: 32px;
}

.error-message {
  color: #ff5252;
  font-size: 14px;
  white-space: pre-line;
}

.error-card {
  margin-top: 30px;
}

.form-card {
  min-height: 340px;
  border: 0.5px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 10px;
  height: 100%;
}
</style>