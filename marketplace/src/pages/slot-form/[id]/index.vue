<template>
  <template v-if="isSucess">
    <div
      style="
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
      "
    >
      <p class="text-color text-center text-h5 ma-2">
        We have received your request and we will get back to you shortly
      </p>
    </div>
  </template>
  <template v-if="isLoading">
    <v-row class="fill-height ma-0 align-center justify-center">
      <v-col cols="12" class="text-center">
        <v-progress-circular
          :size="70"
          :width="7"
          color="white"
          indeterminate
        ></v-progress-circular>
      </v-col>
    </v-row>
  </template>
  <div class="container" v-if="!isLoading && !isSucess">
    <v-row>
      <v-col cols="12" lg="6" md="6" sm="6" xs="6">
        <div class="text-subtitle" v-if="formId == 10">Note: If you are a band, each member must submit their individual details.</div>
        <div class="text-h6">Personal Details</div>
        <CustomInput
          v-model="personalForm.name.value"
          :placeholder="personalForm.name.title"
          :name="personalForm.name.title"
          type="text"
          :required="true"
        />
        <v-row>
          <v-col>
            <CustomInput
              v-model="personalForm.age.value"
              :placeholder="personalForm.age.title"
              :name="personalForm.age.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
          <v-col>
            <CustomInput
              v-model="personalForm.city.value"
              :placeholder="personalForm.city.title"
              :name="personalForm.city.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
        </v-row>
        <CustomInput
          v-model="personalForm.email.value"
          :placeholder="personalForm.email.title"
          :name="personalForm.email.title"
          type="text"
          custom-class="mb-6 mt-3"
        />
        <CustomInput
          v-model="personalForm.phn_no.value"
          :placeholder="personalForm.phn_no.title"
          :name="personalForm.phn_no.title"
          type="text"
          custom-class="mb-6 mt-3"
        />
        <CustomInput
          v-if="formId == 9 || formId == 10"
          v-model="musicForm.social_links.value"
          :placeholder="musicForm.social_links.title"
          :name="musicForm.social_links.title"
          type="text"
          custom-class="mb-6 mt-3"
        />
      </v-col>
      <v-col
        cols="12"
        lg="6"
        md="6"
        sm="6"
        xs="6"
        v-if="formId != 9 && formId != 10"
      >
        <div class="text-h6">About your dance training</div>
        <CustomInput
          v-model="trainingForm.dance_style.value"
          :placeholder="trainingForm.dance_style.title"
          :name="trainingForm.dance_style.title"
          type="text"
          custom-class="mb-6 mt-3"
        />
        <v-row>
          <v-col>
            <CustomInput
              v-model="trainingForm.no_training_years.value"
              :placeholder="trainingForm.no_training_years.title"
              :name="trainingForm.no_training_years.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
          <v-col>
            <CustomInput
              v-model="trainingForm.teacher_name.value"
              :placeholder="trainingForm.teacher_name.title"
              :name="trainingForm.teacher_name.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
        </v-row>
        <CustomInputArea
          v-model="trainingForm.perform_exp.value"
          :placeholder="trainingForm.perform_exp.title"
          :name="trainingForm.perform_exp.title"
          type="textarea"
          custom-class="mb-6 mt-3"
        />
        <CustomInputArea
          v-model="trainingForm.teach_exp.value"
          :placeholder="trainingForm.teach_exp.title"
          :name="trainingForm.teach_exp.title"
          type="textarea"
          custom-class="mb-6 mt-3"
        />
      </v-col>
      <v-col
        cols="12"
        lg="6"
        md="6"
        sm="6"
        xs="6"
        v-if="formId == 9 || formId == 10"
      >
        <div class="text-h6">About your music</div>
        <CustomInput
          v-model="musicForm.music_role.value"
          :placeholder="musicForm.music_role.title"
          :name="musicForm.music_role.title"
          type="text"
          custom-class="mb-6 mt-3"
        />
        <CustomInputArea
          v-model="musicForm.music_journey.value"
          :placeholder="musicForm.music_journey.title"
          :name="musicForm.music_journey.title"
          type="textarea"
          custom-class="mb-6 mt-3"
        />
        <CustomInput
          v-model="musicForm.music_geners.value"
          :placeholder="musicForm.music_geners.title"
          :name="musicForm.music_geners.title"
          type="text"
          custom-class="mb-6 mt-3"
        />
        <CustomInput
          v-model="musicForm.music_released.value"
          :placeholder="musicForm.music_released.title"
          :name="musicForm.music_released.title"
          type="text"
          custom-class="mb-6 mt-3"
          subtext = "( If yes, please share link )"
        />
        <CustomInput
          v-model="musicForm.music_demo.value"
          :placeholder="musicForm.music_demo.title"
          :name="musicForm.music_demo.title"
          type="text"
          custom-class="mb-6 mt-3"
          subtext="(If yes, please upload your demo below (MP3/WAV) or share a link.)"
        />
        <CustomInput
          v-if="formId == 10"
          v-model="musicForm.group_name.value"
          :placeholder="musicForm.group_name.title"
          :name="musicForm.group_name.title"
          type="text"
          custom-class="mb-6 mt-3"
        />
      </v-col>
    </v-row>
    <v-row v-if="formId == 6 || formId == 7  || formId == 8">
      <v-col cols="12">
        <div class="text-h6">Additional Questions</div>
      </v-col>
      <template v-if="formId == 6">
        <CustomInputArea
          v-model="additionalData.composition_name.value"
          :placeholder="additionalData.composition_name.title"
          :name="additionalData.composition_name.title"
          type="textarea"
          custom-class="mb-6 mt-3"
        />
        <CustomInputArea
          v-model="additionalData.composition_desc.value"
          :placeholder="additionalData.composition_desc.title"
          :name="additionalData.composition_desc.title"
          type="textarea"
          custom-class="mb-6 mt-3"
        />
        <v-row>
          <v-col cols="12" md="6" sm="12" lg="4">
            <CustomInput
              v-model="additionalData.is_pre_record_music.value"
              :placeholder="additionalData.is_pre_record_music.title"
              :name="additionalData.is_pre_record_music.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
          <v-col cols="12" md="6" sm="12" lg="4">
            <CustomInput
              v-model="additionalData.is_wip.value"
              :placeholder="additionalData.is_wip.title"
              :name="additionalData.is_wip.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
          <v-col cols="12" md="6" sm="12" lg="4">
            <CustomInput
              v-model="additionalData.work_time.value"
              :placeholder="additionalData.work_time.title"
              :name="additionalData.work_time.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
        </v-row>
        <CustomInputArea
          v-model="additionalData.class_reason.value"
          :placeholder="additionalData.class_reason.title"
          :name="additionalData.class_reason.title"
          type="textarea"
          custom-class="mb-6 mt-3"
        />
      </template>

      <template v-if="formId == 7">
        <v-row>
          <v-col cols="12" md="6" sm="6" lg="6">
            <CustomInput
              v-model="additionalData2.production_name.value"
              :placeholder="additionalData2.production_name.title"
              :name="additionalData2.production_name.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
          <v-col cols="12" md="6" sm="6" lg="6">
            <CustomInput
              v-model="additionalData2.is_pre_record_music.value"
              :placeholder="additionalData2.is_pre_record_music.title"
              :name="additionalData2.is_pre_record_music.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
          <v-col cols="12" md="6" sm="7" lg="6">
            <CustomInput
              v-model="additionalData2.is_wip.value"
              :placeholder="additionalData2.is_wip.title"
              :name="additionalData2.is_wip.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
          <v-col cols="12" md="6" sm="5" lg="6">
            <CustomInput
              v-model="additionalData2.is_group.value"
              :placeholder="additionalData2.is_group.title"
              :name="additionalData2.is_group.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
          <v-col cols="12" md="6" sm="12" lg="6">
            <CustomInput
              v-model="additionalData2.stage_date.value"
              :placeholder="additionalData2.stage_date.title"
              :name="additionalData2.stage_date.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
          <v-col cols="12" md="6" sm="12" lg="6">
            <CustomInput
              v-model="additionalData2.work_time.value"
              :placeholder="additionalData2.work_time.title"
              :name="additionalData2.work_time.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
          <v-col cols="12" md="12" sm="12" lg="12">
            <CustomInputArea
              v-model="additionalData2.class_reason.value"
              :placeholder="additionalData2.class_reason.title"
              :name="additionalData2.class_reason.title"
              type="textarea"
              custom-class="mb-6 mt-3"
            />
          </v-col>
        </v-row>
      </template>

      <template v-if="formId == 8">
        <v-row>
          <v-col cols="12" md="6" sm="8" lg="6">
            <CustomInput
              v-model="additionalData3.composition_name.value"
              :placeholder="additionalData3.composition_name.title"
              :name="additionalData3.composition_name.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
          <v-col cols="12" md="6" sm="4" lg="6">
            <CustomInput
              v-model="additionalData3.whose_choreography.value"
              :placeholder="additionalData3.whose_choreography.title"
              :name="additionalData3.whose_choreography.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
          <v-col cols="12" md="6" sm="6" lg="6">
            <CustomInput
              v-model="additionalData3.is_pre_record_music.value"
              :placeholder="additionalData3.is_pre_record_music.title"
              :name="additionalData3.is_pre_record_music.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
          <v-col cols="12" md="6" sm="6" lg="6">
            <CustomInput
              v-model="additionalData3.work_time.value"
              :placeholder="additionalData3.work_time.title"
              :name="additionalData3.work_time.title"
              type="text"
              custom-class="mb-6 mt-3"
            />
          </v-col>
          <v-col cols="12" md="12" sm="12" lg="12">
            <CustomInputArea
              v-model="additionalData3.class_reason.value"
              :placeholder="additionalData3.class_reason.title"
              :name="additionalData3.class_reason.title"
              type="textarea"
              custom-class="mb-6 mt-3"
            />
          </v-col>
        </v-row>
      </template>
    </v-row>
    <br />
    <div class="upload-button-container">
      <input
        type="file"
        ref="fileInput"
        :accept="formId == 9 || formId == 10 ? '.mp3,.wav' : '.mp4'"
        style="display: none"
        @change="handleFileSelect"
      />
      <v-btn
        v-if="!selectedFile"
        height="50"
        color="white"
        class="white--text rounded-xl"
        @click="openFilePicker"
      >
        {{
          formId == 9 || formId == 10 ? "Pick Audio File" : "Pick Video File"
        }}
      </v-btn>
      <div v-else>
        <v-row
          align="center"
          no-gutters
          v-if="uploadProgress == null && video_link == ''"
        >
          <v-col v-if="selectedFile" cols="12" sm="8">
            <div class="selected-file d-flex align-center">
              <span class="file-name">{{ selectedFile.name }}</span>
              <v-btn icon small color="grey" @click="removeFile" class="ml-2">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </v-col>
          <v-col cols="12" sm="4" class="d-flex justify-sm-end mt-3 mt-sm-0">
            <v-btn
              height="50"
              color="white"
              class="white--text rounded-xl"
              @click="uploadFile"
            >
              Upload
            </v-btn>
          </v-col>
        </v-row>
      </div>
      <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
    </div>
    <v-row v-if="uploadProgress !== null">
      <v-col cols="12">
        <v-progress-linear
          color="light-blue"
          height="10"
          :model-value="uploadProgress"
          striped
          class="mt-5"
        ></v-progress-linear>
      </v-col>
    </v-row>
    <div v-if="video_link">
      {{
        formId == 9 || formId == 10
          ? "Audio has been uploaded"
          : "Video has been uploaded"
      }}
    </div>
    <template id="additional-text">
      <div class="text-subtitle-2 mt-2" v-if="formId == 6">
        {{ additionalData.video.title }}
      </div>
      <div class="text-subtitle-2 mt-2" v-if="formId == 7">
        {{ additionalData2.video.title }}
      </div>
      <div class="text-subtitle-2 mt-2" v-if="formId == 8">
        {{ additionalData3.video.title }}
      </div>
    </template>
    <br />
    <br />
    <v-btn
      height="50"
      color="white"
      class="white--text rounded-xl"
      @click="submitForm"
    >
      Submit
    </v-btn>
  </div>
</template>

<script setup>
import { useRoute } from "vue-router";
import axios from "axios";

import { getUserToken } from "@/services/userService";
import { meetRequest } from "@/services/bookClass";
import { isValidIndianMobileNumber } from "@/helper/common";

const route = useRoute();
const formId = route.params.id;

const errorMessage = ref("");
const fileInput = ref(null);
const selectedFile = ref(null);
let isValid = true;
const uploadProgress = ref(null);
const isLoading = ref(false);
const isSucess = ref(false);

const emit = defineEmits(["file-selected", "file-removed"]);

const openFilePicker = () => {
  fileInput.value.click();
};

const handleFileSelect = (event) => {
  const file = event.target.files[0];
  if (file) {
    if (formId == 9 || formId == 10) {
      // Audio file validation
      console.log(file.type);
      if (!["audio/mp3", "audio/wav", "audio/mpeg"].includes(file.type)) {
        errorMessage.value = "Please select an MP3 or WAV audio file.";
      } else if (file.size > 50 * 1024 * 1024) {
        // 50MB limit for audio
        errorMessage.value = "Audio file size exceeds 50MB limit.";
      } else {
        errorMessage.value = "";
        selectedFile.value = file;
        emit("file-selected", file);
      }
    } else {
      // Original video validation
      if (file.type !== "video/mp4") {
        errorMessage.value = "Please select an MP4 file.";
      } else if (file.size > 200 * 1024 * 1024) {
        errorMessage.value = "File size exceeds 200MB limit.";
      } else {
        errorMessage.value = "";
        selectedFile.value = file;
        emit("file-selected", file);
      }
    }
  }
  event.target.value = "";
};

const removeFile = () => {
  selectedFile.value = null;
  emit("file-removed");
};

const uploadFile = async () => {
  const formData = new FormData();
  formData.append("file", selectedFile.value);
  formData.append("form_id", formId);

  try {
    const token = getUserToken();
    const res = await axios({
      method: "POST",
      url: import.meta.env.VITE_BASE_URL + `/api/asset-upload?id=${2}`,
      data: formData,
      headers: {
        authorization: token,
      },
      onUploadProgress: (progressEvent) => {
        uploadProgress.value = Math.round(
          (progressEvent.loaded / progressEvent.total) * 100
        );
      },
    });
    const data = res.data;
    video_link = data.url;
    uploadProgress.value = null;
  } catch (error) {
    uploadProgress.value = null;
    console.error("Error in sending file", error);
  }
};

const personalForm = ref({
  name: {
    value: "",
    key: "name",
    title: "Full Name",
    order: 1,
  },
  age: {
    value: null,
    key: "age",
    title: "Age",
    order: 2,
  },
  city: {
    value: "",
    key: "city",
    title: "City",
    order: 3,
  },
  email: {
    value: "",
    key: "email",
    title: "Email ID",
    order: 4,
  },
  phn_no: {
    value: "",
    key: "phn_no",
    title: "Contact Number",
    order: 5,
  },
});

const musicForm = ref({
  social_links: {
    value: "",
    key: "social_links",
    title: "Social Media Links: (Instagram, YouTube, Facebook, etc.)",
    order: 6,
  },
  music_role: {
    value: "",
    key: "music_role",
    title: "What is your primary role in music?",
    order: 7,
  },
  music_journey: {
    value: "",
    key: "music_journey",
    title: "Briefly describe your journey in music",
    order: 8,
  },
  music_geners: {
    value: null,
    key: "music_geners",
    title: "Which genre(s) best describe your music?",
    order: 9,
  },
  music_released: {
    value: "",
    key: "music_released",
    title: "Have you released music before?",
    order: 10,
  },
  music_demo: {
    value: "",
    key: "music_demo",
    title: "Do you have a demo or rough idea for the song?",
    order: 11,
  },
  group_name: {
    value: "",
    key: "group_name",
    title: "Group Name (if applicable)",
    order: 12,
  },
  audio: {
    order: 13,
    value: "",
    key: "audio",
    isHidden: true,
    title: "Audio Link",
  },
});

const trainingForm = ref({
  dance_style: {
    value: "",
    key: "dance_style",
    title: "Your Dance Style",
    order: 6,
  },
  no_training_years: {
    value: "",
    key: "no_training_years",
    title: "Number of years of training",
    order: 7,
  },
  teacher_name: {
    value: null,
    key: "teacher_name",
    title: "Name of Teacher",
    order: 8,
  },
  perform_exp: {
    value: "",
    key: "perform_exp",
    title: "Briefly describe your Performance experience",
    order: 9,
  },
  teach_exp: {
    value: "",
    key: "teach_exp",
    title: "Briefly describe your Teaching expereience",
    order: 10,
  },
});

let video_link = "";

const additionalData = ref({
  composition_name: {
    value: "",
    key: "composition_name",
    title: "Name of the composition you would like to be mentored on?",
    order: 11,
  },
  composition_desc: {
    value: "",
    key: "composition_desc",
    title:
      "Briefly describe this composition and what you have envisioned (theme, approach to music/rhythm/visuals etc)",
    order: 12,
  },
  is_pre_record_music: {
    value: "",
    key: "is_pre_record_music",
    title: "Does it have pre-recorded music?",
    order: 13,
  },
  is_wip: {
    value: "",
    key: "is_wip",
    title: "Is this composition a work in progress or complete?",
    order: 14,
  },
  work_time: {
    value: "",
    key: "work_time",
    title: "For how long have you been working on creating this piece?",
    order: 15,
  },
  class_reason: {
    value: "",
    key: "class_reason",
    title:
      "Why do you wish to attend this masterclass? In what way do you think it will help you?",
    order: 16,
  },
  video: {
    order: 17,
    value: "",
    key: "video",
    isHidden: true,
    title: "Submit a video of your chosen composition, in part or in full.",
  },
});

const additionalData2 = ref({
  production_name: {
    value: "",
    key: "production_name",
    title: "Name of your production",
    order: 11,
  },
  is_pre_record_music: {
    value: "",
    key: "is_pre_record_music",
    title: "Does it have pre-recorded music?",
    order: 12,
  },
  is_wip: {
    value: "",
    key: "is_wip",
    title: "Is this composition a work in progress or complete?",
    order: 13,
  },
  work_time: {
    value: "",
    key: "work_time",
    title: "For how long have you been working on creating this production?",
    order: 14,
  },
  is_group: {
    value: "",
    key: "is_group",
    title: "Is this a group or solo production?",
    order: 15,
  },
  stage_date: {
    value: "",
    key: "stage_date",
    title: "Do you have a date when this production is to be staged?",
    order: 16,
  },
  class_reason: {
    value: "",
    key: "class_reason",
    title:
      "Why do you wish to attend this masterclass? In what way do you think it will help you?",
    order: 17,
  },
  video: {
    order: 18,
    value: "",
    key: "video",
    isHidden: true,
    title:
      "Submit a video where you speak about your production in as much detail as you would like to share, which will give enough information to be considered for the next step.",
  },
});

const additionalData3 = ref({
  composition_name: {
    value: "",
    key: "composition_name",
    title: "Name of the composition you would like to be mentored on?",
    order: 11,
  },
  whose_choreography: {
    value: "",
    key: "whose_choreography",
    title: "Whose choreography is it?",
    order: 12,
  },
  is_pre_record_music: {
    value: "",
    key: "is_pre_record_music",
    title: "Does it have pre-recorded music?",
    order: 13,
  },
  work_time: {
    value: "",
    key: "work_time",
    title: "For how long have you practiced/performed the piece?",
    order: 14,
  },
  class_reason: {
    value: "",
    key: "class_reason",
    title:
      "Why do you wish to attend this masterclass? In what way do you think it will help you?",
    order: 15,
  },
  video: {
    order: 16,
    value: "",
    key: "video",
    isHidden: true,
    title: "Submit a video of your chosen composition, in part or in full.",
  },
});

const submitForm = async () => {
  if (!validateForm()) {
    return;
  }
  if ((video_link == null || video_link == "") && (formId != 9 && formId != 10)) {
    errorMessage.value = "File Upload is needed";
    return;
  }
  let toSend = null;
  if (formId == "6") {
    additionalData.value.video.value = video_link;
    const responseData = {
      ...trainingForm.value,
      ...additionalData.value,
      ...personalForm.value,
      video_link,
    };
    toSend = {
      data: responseData,
      id: formId,
    };
  }
  if (formId == "7") {
    additionalData2.value.video.value = video_link;
    const responseData = {
      ...trainingForm.value,
      ...additionalData2.value,
      ...personalForm.value,
      video_link,
    };
    toSend = {
      data: responseData,
      id: formId,
    };
  }

  if (formId == "8") {
    additionalData3.value.video.value = video_link;
    const responseData = {
      ...trainingForm.value,
      ...additionalData3.value,
      ...personalForm.value,
      video_link,
    };
    toSend = {
      data: responseData,
      id: formId,
    };
  }

  if (formId == "9") {
    musicForm.value.audio.value = video_link;
    const responseData = {
      ...musicForm.value,
      ...personalForm.value,
      video_link,
    };
    toSend = {
      data: responseData,
      id: formId,
    };
  }

  if (formId == "10") {
    musicForm.value.audio.value = video_link;
    const responseData = {
      ...musicForm.value,
      ...personalForm.value,
      video_link,
    };
    toSend = {
      data: responseData,
      id: formId,
    };
  }

  if (typeof toSend == null) {
    return;
  }

  try {
    isLoading.value = true;
    const res = await meetRequest(toSend);
    isLoading.value = false;
    isSucess.value = true;
  } catch (error) {
    console.error("Error in meet request", error);
  }
};
const validateForm = () => {
  errorMessage.value = "";
  isValid = true;
  validatePersonal();
  validateTraining();
  return isValid;
};

const validatePersonal = () => {
  if (!personalForm.value.name.value) {
    isValid = false;
    errorMessage.value += "Name is required.\n";
  }
  if (
    !personalForm.value.email.value ||
    !/\S+@\S+\.\S+/.test(personalForm.value.email.value)
  ) {
    isValid = false;
    errorMessage.value += "A valid email is required.\n";
  }
  if (
    personalForm.value.age.value < 0 ||
    !Number.isInteger(Number(personalForm.value.age.value)) ||
    !personalForm.value.age.value
  ) {
    isValid = false;
    errorMessage.value += "Age must be a positive integer.\n";
  }

  if (!personalForm.value.city.value) {
    isValid = false;
    errorMessage.value += "City is required.\n";
  }

  if (
    !personalForm.value.phn_no.value ||
    !isValidIndianMobileNumber(personalForm.value.phn_no.value)
  ) {
    isValid = false;
    errorMessage.value += "Valid Contact Number is required.\n";
  }
};

const validateTraining = () => {
  if (formId == 9 || formId == 10) {
    return null;
  }
  if (!trainingForm.value.dance_style.value) {
    isValid = false;
    errorMessage.value += "Dance Style is required.\n";
  }

  if (!trainingForm.value.no_training_years.value) {
    isValid = false;
    errorMessage.value += "Training Year is required.\n";
  }

  if (!trainingForm.value.teacher_name.value) {
    isValid = false;
    errorMessage.value += "Teacher Name is required.\n";
  }
};
</script>

<style scoped>
.container {
  margin: 32px;
}

.upload-button-container {
  display: inline-block;
}

.selected-file {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  margin-top: 8px;
}

.file-name {
  margin-right: 12px;
  color: white;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.error-message {
  color: #ff5252;
  font-size: 14px;
  margin-top: 8px;
}

.progress-bar {
  width: 100%;
  background-color: #e0e0e0;
  border-radius: 5px;
  margin-top: 10px;
}

.progress {
  height: 20px;
  background-color: #76c7c0; /* Change this color as needed */
  border-radius: 5px;
}
</style>
