<template>
  <v-container>

    <h1>Marketplace</h1>

    <h3 class="filter-text">Filter by artiste</h3>
    <ArtistCard></ArtistCard>

    <h3 class="filter-text">Filter by products</h3>
    <ProductTypeCard />

    <h3 class="filter-text">Products</h3>
    <SortBy />

    <v-row>
      <v-col cols="12" md="2" v-if="mdAndUp">
        <h3 class="filter-text">Filters</h3>
        <Search class="search" />
        <FilterSection />
      </v-col>

      <hr v-if="mdAndUp" class="vertical-line" />

      <v-col class="productFilter">
        <v-btn color="#0066cc" block v-if="smAndDown" @click="showFilters = true">
          Show Filters
        </v-btn>

        <ProductGrid :products="appStore.products" />
      </v-col>
    </v-row>

    <v-dialog v-model="showFilters" max-width="90%" transition="dialog-bottom-transition" fullscreen>
      <v-card>
        <v-card-title>
          <h3 class="filter-text">Filters</h3>
        </v-card-title>

        <v-card-text>
          <Search class="search" />
          <FilterSection />
        </v-card-text>

        <v-card-actions>
          <v-btn color="#0066cc" text @click="showFilters = false">
            Apply Filters
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script setup>
import { onBeforeMount } from "vue";
import { useRoute } from "vue-router";
import { useAppStore } from "@/stores/app";
const route = useRoute();
const appStore = useAppStore();

import { useDisplay } from "vuetify";
import FilterSection from "@/components/FilterSection.vue";

const { mdAndUp, smAndDown } = useDisplay();

const showFilters = ref(false);


onBeforeMount(() => {
  const artistId = route.query.artist_id;
  const cateogryId = route.query.category_id;
  appStore.artistId = artistId;
  appStore.categoryId = cateogryId;
  appStore.getProductsStore();
});
</script>

<style scoped>
.filter-text {
  margin-top: 1rem;
}

.search {
  margin-top: 10px;
}

.vertical-line {
  width: 1px;
  height: auto;
  background-color: #ddd;
  border: none;
}

.mobile-filter-btn {
  position: fixed;
  bottom: 16px;
  right: 16px;
  background-color: #0066cc;
  color: white;
  z-index: 1000;
}

.mobile-filter-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  z-index: 1100;
  padding: 16px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
  overflow-y: auto;
}

.apply-btn {
  margin-top: 16px;
  background-color: #0066cc;
  color: white;
  width: 100%;
}

@media (max-width: 960px) {
  .verticalLine {
    display: none;
  }

  .productFilter {
    margin-top: 10px;
  }
}
</style>
