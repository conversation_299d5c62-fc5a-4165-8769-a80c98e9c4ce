order_details - "CREATE TRIGGER after_order_details_update AFTER UPDATE OF payment_status ON public.order_details FOR EACH ROW WHEN (new.payment_status = 3 AND (old.payment_status <> 3 OR old.payment_status IS NULL)) EXECUTE FUNCTION populate_sales_for_products(); CREATE TRIGGER after_order_details_insert AFTER INSERT ON public.order_details FOR EACH ROW EXECUTE FUNCTION populate_sales_for_products();"
artist_meeting_logs - "CREATE TRIGGER after_meet_purchase_update AFTER UPDATE OF invoice ON public.artist_meeting_logs FOR EACH ROW EXECUTE FUNCTION populate_sales_for_meets();",
user_paid_video_entries - "CREATE TRIGGER after_video_purchase_update AFTER UPDATE OF invoice ON public.user_paid_video_entries FOR EACH ROW EXECUTE FUNCTION populate_sales_for_paid_videos();",
course_purchase_logs - "CREATE TRIGGER after_course_purchase_update AFTER UPDATE ON public.course_purchase_logs FOR EACH ROW EXECUTE FUNCTION populate_sales_for_courses();",
