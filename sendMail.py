import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import ssl

# Email configuration
SMTP_HOST = "email-smtp.ap-south-1.amazonaws.com"
SMTP_PORT = 465
SMTP_USER = "AKIASKU7O5NR6XVIWGTJ"
SMTP_PASSWORD = "BA0CoQ2LhA/Go2484UTeku3V5kKLYCsDVv3i1socuO/9"
FROM_EMAIL = "<EMAIL>"
FROM_NAME = "Artisteverse"

def send_personalized_email(recipients_data, subject, body_template, payment_link, body_type="plain"):
    """
    Send personalized email to multiple recipients
    
    Args:
        recipients_data: List of dictionaries with 'email' and 'name' keys
        subject: Email subject line
        body_template: Email body template with [User's Name] placeholder
        payment_link: Payment link to replace [Link] placeholder
        body_type: "plain" for plain text, "html" for HTML content
    """
    try:
        # Create secure connection
        context = ssl.create_default_context()
        
        with smtplib.SMTP_SSL(SMTP_HOST, SMTP_PORT, context=context) as server:
            server.login(SMTP_USER, SMTP_PASSWORD)
            
            # Send to each recipient
            for recipient in recipients_data:
                # Personalize the email body
                personalized_body = body_template.replace("[User's Name]", recipient['name'])
                personalized_body = personalized_body.replace("Link", payment_link)
                
                # Create message for this recipient
                msg = MIMEMultipart()
                msg['From'] = f'"{FROM_NAME}" <{FROM_EMAIL}>'
                msg['To'] = recipient['email']
                msg['Subject'] = subject
                
                # Add personalized body to email
                msg.attach(MIMEText(personalized_body, body_type))
                
                # Send email
                text = msg.as_string()
                server.sendmail(FROM_EMAIL, recipient['email'], text)
                print(f"Email sent successfully to {recipient['name']} ({recipient['email']})")
                
    except Exception as e:
        print(f"Error sending email: {str(e)}")

# Example usage
if __name__ == "__main__":
    # Define recipients with their names
    recipients_data = [
        {"email": "<EMAIL>", "name": "omkar"},
        {"email": "<EMAIL>", "name": "Sashmit Chakraborty"},
        {"email": "<EMAIL>", "name": "Ritam Ganguly"},
        {"email": "<EMAIL>", "name": "Maulik"}
    ]
    
    # Define email content
    email_subject = "Complete Your Payment - Mentorship Program with Bickram Ghosh"
    
    # Email body template with placeholders
    email_body_template = """Dear [User's Name],
    
We're thrilled that you've been selected for the Mentorship Program with Bickram Ghosh — a unique opportunity to learn, collaborate, and create with the maestro himself.

However, we've noticed that your payment hasn't been completed yet.

To secure your spot, please complete the payment within the next 48 hours using the link below:
🔗 Link

Please note that due to limited slots, we will only be able to hold your selection for the next 2 days. After that, your slot may be offered to another applicant.

We'd love to have you on board for this special musical journey!

Best regards,
Team Bickram Ghosh"""
    
    # Payment link
    payment_link = "https://artisteverse.com/store/slotBook/9"
    
    # Send personalized emails
    send_personalized_email(recipients_data, email_subject, email_body_template, payment_link)