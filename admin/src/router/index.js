import { createRouter, createWebHistory } from 'vue-router'

import { getUserToken } from '../services/userService';


const routes = [
    {
        path: '/',
        name: "home",
        component: () => import('@/views/HomeView.vue')
    },
    {
        path: '/login',
        name: "login",
        component: () => import("@/views/LoginView.vue")
    },
    {
        path: '/karaoke',
        name: "Karaoke",
        component: () => import("@/views/KaraokeView.vue")
    },
    {
        path: '/order',
        name: "Order",
        component: () => import("@/views/OrderView.vue")
    },
    {
        path: '/cart',
        name: "Cart",
        component: () => import("@/views/CartView.vue")
    },
    {
        path: '/orderDetail',
        name: "Order Detail",
        component: () => import("@/views/OrderDetailView.vue")
    },
]

const router = createRouter({
    history: createWebHistory('/'),
    routes,
});

router.beforeEach(async (to) => {
    // redirect to login page if not logged in and trying to access a restricted page
    const publicPages = ['/login'];
    const authRequired = !publicPages.includes(to.path);
    const token = getUserToken();


    if (authRequired && !token) {
        // auth.returnUrl = to.fullPath;
        return '/login';
    }

});

export default router