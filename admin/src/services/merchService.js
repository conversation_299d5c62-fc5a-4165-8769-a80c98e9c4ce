import { apiWrapper } from "../helper/apiWrapper";

export const getProductList = async (artistId) => {
  try {
    const response = await apiWrapper.get(`/api/products/${artistId}`);
    const data = response.data;
    if (response.status == 200) {
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In getting Product List API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

const processProductDetailResp = (data) => {
  let imageList = data.imageList;
  let productDetail = data.details;
  let selectedImage = "";
  if (data.imageList.length) {
    selectedImage = data.imageList[0].imageURL;
  }
  let dropDownValues = {};
  data.details.varaiant_option.forEach((key) => {
    const attr = productDetail.variants.map((el) => el[key]);
    const uniqAttr = [...new Set(attr)];
    dropDownValues[key] = uniqAttr;
  });
  let isVariation = Object.keys(dropDownValues).length;

  let sendData = {
    imageList,
    productDetail,
    selectedImage,
    dropDownValues,
    isVariation,
  };
  return sendData;
};

export const getProductDetail = async (body, header, productId = 1) => {
  try {
    const response = await apiWrapper.get(
      `/api/product-details/${productId}`,
      body,
      header
    );
    const data = response.data;
    if (response.status == 200) {
      if (data.data.details) {
        return processProductDetailResp(data.data);
      }
      return null;
    }
    if (response?.response) {
      console.error(
        "Error In getting Product Detail API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const getCart = async (artistId) => {
  try {
    const response = await apiWrapper.get(`/api/cart/${artistId}`);
    if (response.status == 200) {
      const data = response.data.data.cartItems.map((el, index) => {
        return {
          ...el,
          id: el.product_item_id,
        };
      });
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In getting cart API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const getOrderDetail = async (order_id, order_type) => {
  try {
    const response = await apiWrapper.get(
      `/admin/orderDetail?order_item_id=${order_id}&order_type=${order_type}`
    );
    if (response.status == 200) {
      const data = response.data.data;
      return data;
    }
    if (response?.response) {
      // TODO Temp Remove Once Backend is done
      console.error(
        "Error In Getting order detail API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

// Admin

export const getOrderHistory = async (params) => {
  console.log(params);

  try {
    let query = `?page=${params.page}&pageSize=${params.itemsPerPage}`;
    if (params.key) {
      query = query + `&key=${params.key}`;
    }
    if (params.order) {
      query = query + `&order=${params.order}`;
    }
    if (params.filterBy) {
      query = query + `&filterBy=${params.filterBy}`;
    }

    const response = await apiWrapper.get(`/admin/orderHistory${query}`);
    if (response.status == 200) {
      const data = response.data.result;
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In Getting Admin API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};

export const getCartHistory = async (params) => {
  try {
    let query = `?page=${params.page}&pageSize=${params.itemsPerPage}`;
    if (params.key) {
      query = query + `&key=${params.key}`;
    }
    if (params.order) {
      query = query + `&order=${params.order}`;
    }
    const response = await apiWrapper.get(`/admin/cartHistory${query}`);
    if (response.status == 200) {
      const data = response.data.result;
      return data;
    }
    if (response?.response) {
      console.error(
        "Error In Getting Cart API Internal",
        response.response.data
      );
      return Promise.reject({
        status: false,
        message: response.response.data.message,
        rawError: response.response,
      });
    }
    return Promise.reject({
      status: false,
      message: "Broken Connection",
      rawError: response,
    });
  } catch (error) {
    console.error(error);
    return Promise.reject({
      status: false,
      message: "Something Went Wrong on Connection",
      rawError: error,
    });
  }
};
