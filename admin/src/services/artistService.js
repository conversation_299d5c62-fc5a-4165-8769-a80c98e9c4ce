import { apiWrapper } from "../helper/apiWrapper";

export const getPopupContent = async (popupId) => {
    try {
        const response = await apiWrapper.get(`/api/popup?id=${popupId}`)
        const data = response.data;
        if (response.status == 200) {
            return data.data;
        }
        if (response?.response) {
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });

    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}

export const getVideoContent = async (popupId) => {
    try {
        const response = await apiWrapper.get(`/api/video?id=${popupId}`)
        const data = response.data;
        if (response.status == 200) {
            return data.data;
        }
        if (response?.response) {
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });

    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}