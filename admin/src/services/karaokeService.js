import { apiWrapper } from "../helper/apiWrapper";

export const getTracks = async (artist_id = 1) => {
    try {
        const response = await apiWrapper.get(`/api/getKaraokeTracks/${artist_id}`);
        if (response.status == 200) {
            const data = response.data;
            return data;
        }
        if (response?.response) {
            console.error("Error In Get Tracks API Internal", response.response.data)
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });
    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}

// Admin

export const getAllKaraoke = async (params) => {
    try {
        let query = `?page=${params.page}&pageSize=${params.itemsPerPage}`;
        if(params.key) {
            query = query + `&key=${params.key}`
        }
        if(params.order) {
            query =  query + `&order=${params.order}`
        }
        const response = await apiWrapper.get(`/admin/karaoke${query}`);
        if (response.status == 200) {
            const data = response.data.result;
            return data;
        }
        if (response?.response) {
            console.error("Error In Getting karaoke API Internal", response.response.data)
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });
    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}