import { apiWrapper } from '@/helper/apiWrapper';
import axios from 'axios';

export const getUserToken = () => {
    const token = localStorage.getItem('token');
    return token;
}

export const removeUserToken = () => {
    localStorage.removeItem('token');
}

export const setUserToken = (token) => {
    localStorage.setItem('token', token);
}

export const login = async (body, header) => {
    try {
        const response = await apiWrapper.post("/admin/login", body, header)
        if (response.status == 200) {
            const data = response.data;
            setUserToken(data.data.token);
            return data;
        }

        if (response?.response) {
            console.error("Error In Login API Internal", response.response.data)
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });

    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}

export const googleLoginAPI = async (body, header) => {
    try {
        const response = await apiWrapper.post("/admin/google-login", body, header)
        if (response.status == 200) {
            const data = response.data;
            setUserToken(data.data.token);
            return data;
        }

        if (response?.response) {
            console.error("Error In Google Login API Internal", response.response.data)
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });

    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}

export const getAllUser = async (params) => {
    try {
        let query = `?page=${params.page}&pageSize=${params.itemsPerPage}`;
        if (params.key) {
            query = query + `&key=${params.key}`
        }
        if (params.order) {
            query = query + `&order=${params.order}`
        }
        const response = await apiWrapper.get(`/admin/user${query}`);
        if (response.status == 200) {
            const data = response.data.result;
            return data;
        }
        if (response?.response) {
            console.error("Error In Getting Users API Internal", response.response.data)
            return Promise.reject({
                status: false,
                message: response.response.data.message,
                rawError: response.response
            });
        }
        return Promise.reject({
            status: false,
            message: "Broken Connection",
            rawError: response
        });
    } catch (error) {
        console.error(error);
        return Promise.reject({
            status: false,
            message: "Something Went Wrong on Connection",
            rawError: error
        });
    }
}



const authHeader = () => {
    // return auth header with jwt if user is logged in and request is to the api url

    const token = getUserToken();
    if (token) {
        return { authorization: `${token}` };
    } else {
        return {};
    }
}
export const downloadFile = (url) => {
    return axios({
        url,
        method: 'GET',
        responseType: 'blob',
        headers: {
            ...authHeader()
        }
    })
};