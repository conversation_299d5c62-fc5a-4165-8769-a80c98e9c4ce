<script setup>
import { ref, defineAsyncComponent } from "vue";
import { downloadFile } from "../services/fileService";
import { getAllUser } from "../services/userService";

const Header = defineAsyncComponent(() => import("@/components/Header.vue"));

const itemsPerPage = ref(10);

const headers = [
  {
    title: "User Id",
    align: "start",
    key: "user_id",
  },
  { title: "Email", key: "user_name", align: "end" },
  { title: "Created", key: "user_created", align: "end" },
  { title: "Client", key: "client_name", align: "end", sortable: false },
  { title: "Display Name", key: "display_name", align: "end", sortable: false },
  {
    title: "Login Method",
    key: "login_method_name",
    align: "end",
    sortable: false,
  },
];
let search = ref("");
let serverItems = ref([]);
let loading = ref(true);
let totalItems = ref(0);

const loadItems = async ({ page, itemsPerPage, sortBy }) => {
  loading.value = true;
  let key = "user_id";
  let order = "desc";

  if (sortBy.length) {
    key = sortBy[0].key;
    order = sortBy[0].order;
  }
  try {
    let data = await getAllUser({
      page,
      itemsPerPage,
      key: key,
      order: order,
    });
    serverItems.value = data.items;
    totalItems.value = data.total;
    loading.value = false;
  } catch (error) {
    console.error("ERror in getting data", error);
  }
};

const downloadUser = async () => {
  try {
    const response = await downloadFile(`${import.meta.env.VITE_BASE_URL}/admin/user-report`);
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `user.csv`); // or the filename you want
    document.body.appendChild(link);
    link.click();
  } catch (error) {
    // TODO Handle Error
    console.error(error);
  }
};
</script>
<template>
  <Header />
  <v-container>
    <v-btn @click="downloadUser" type="elevated" color="#5865f2">
      Download User</v-btn
    >
  </v-container>
  <v-card class="pa-2 ma-4">
    <v-data-table-server
      v-model:items-per-page="itemsPerPage"
      :headers="headers"
      :items-length="totalItems"
      :items="serverItems"
      :loading="loading"
      :search="search"
      item-value="name"
      @update:options="loadItems"
    >
    </v-data-table-server>
  </v-card>
</template>

<style scoped>
</style>