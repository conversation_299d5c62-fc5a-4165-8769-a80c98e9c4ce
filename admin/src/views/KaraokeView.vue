<script setup>
import { ref, defineAsyncComponent } from "vue";
import { downloadFile } from "../services/fileService";
import { getAllKaraoke } from "../services/karaokeService";

const Header = defineAsyncComponent(() => import("@/components/Header.vue"));

const itemsPerPage = ref(10);

const headers = [
  {
    title: "Id",
    align: "start",
    key: "songid",
  },
  { title: "Email", key: "user_name", align: "end" },
  { title: "Song", key: "song_name", align: "end" },
  { title: "Artist", key: "artist_name", align: "end", sortable: false },
  { title: "Created", key: "song_created", align: "end", sortable: false },
  {
    title: "URL",
    key: "song_url",
    align: "end",
    sortable: false,
  },
];
let search = ref("");
let serverItems = ref([]);
let loading = ref(true);
let totalItems = ref(0);

const openWindow = (url) => {
  window.open(url);
}

const loadItems = async ({ page, itemsPerPage, sortBy }) => {
  loading.value = true;
  let key = "songid";
  let order = "desc";

  if (sortBy.length) {
    key = sortBy[0].key;
    order = sortBy[0].order;
  }
  try {
    let data = await getAllKaraoke({
      page,
      itemsPerPage,
      key: key,
      order: order,
    });
    serverItems.value = data.items;
    totalItems.value = data.total;
    loading.value = false;
  } catch (error) {
    console.error("ERror in getting data", error);
  }
};
const downloadKaraoke = async () => {
  try {
    const response = await downloadFile(`${import.meta.env.VITE_BASE_URL}/admin/karaoke-report`);
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `karaoke.csv`); // or the filename you want
    document.body.appendChild(link);
    link.click();
  } catch (error) {
    // TODO Handle Error
    console.error(error);
  }
};
</script>
<template>
  <Header />
  <v-container>
    <v-btn @click="downloadKaraoke" type="elevated" color="#5865f2">
      Download</v-btn
    >
  </v-container>
  <v-card class="pa-2 ma-4">
    <v-data-table-server
      v-model:items-per-page="itemsPerPage"
      :headers="headers"
      :items-length="totalItems"
      :items="serverItems"
      :loading="loading"
      :search="search"
      item-value="name"
      @update:options="loadItems"
    >
    <template v-slot:[`item.song_url`]="{ value }">
      <v-btn @click="openWindow(value)" color="primary" v-if="value">
        Open
      </v-btn>
    </template>
    </v-data-table-server>
  </v-card>
</template>

<style scoped>
</style>