<script setup>
import { onBeforeMount, ref,defineAsyncComponent } from "vue";
import { useRoute } from "vue-router";

import { downloadFile } from "../services/fileService";
import { getOrderDetail } from "../services/merchService";
const Loader = defineAsyncComponent(() => import("@/components/Loader.vue"));
const Header = defineAsyncComponent(() => import("@/components/Header.vue"));

let isLoading = ref(true);

let orderDetail = ref({});
let products = ref([]);

const downloadInvoice = async () => {
  try {
    const response = await downloadFile(orderDetail.value.invoice_url);
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute(
      "download",
      `invoice_${orderDetail.value.orderNumber}.pdf`
    ); // or the filename you want
    document.body.appendChild(link);
    link.click();
  } catch (error) {
    console.error("Error downloading the file: ", error);
  }
};

onBeforeMount(async () => {
  try {
    const route = useRoute();
    if (!route.query.id) {
      return null;
    }
    const resp = await getOrderDetail(route.query.id, route.query.order_type);
    orderDetail.value = resp.detail;
    products.value = resp.products;
    isLoading.value = false;
  } catch (error) {
    console.error("Error in product detail API", error);
  }
});
</script>

<template>
  <Header />
  <Loader v-if="isLoading" />

  <v-container v-else style="color: #212121; max-width: 1140px" class="pa-10">
    <v-sheet color="rgba(234,89,255,0.13)">
      <p class="text-caption text-left pa-2 ma-2">
        <br>
        Order Status : {{ orderDetail.order_status }}
        <br>
        <br>
        Payment Status : {{ orderDetail.payment_status }}
      </p>
    </v-sheet>
    <br />
    <v-sheet color="rgba(234,89,255,0.13)">
      <v-list bg-color="rgba(234,89,255,0.13)" density="compact">
        <v-list-item>
          <p class="text-subtitle-2">
            Order number:
            <strong>{{ orderDetail.orderNumber }}</strong>
          </p>
        </v-list-item>
        <v-list-item>
          <p class="text-subtitle-2">
            Date:
            <strong>{{ orderDetail.date }}</strong>
          </p>
        </v-list-item>
        <v-list-item>
          <p class="text-subtitle-2">
            Total:
            <strong> &#8377;{{ orderDetail.total }}</strong>
          </p>
        </v-list-item>
        <v-list-item>
          <p class="text-subtitle-2">
            Email:
            <strong> {{ orderDetail.email }}</strong>
          </p>
        </v-list-item>
        <v-list-item v-if="orderDetail.payment_status == 'PAYMENT_SUCCESS'">
          <p class="text-subtitle-2">
            Payment Method:
            <strong>{{ orderDetail.paymentMethod }}</strong>
          </p>
        </v-list-item>
      </v-list>
    </v-sheet>
    <br />
    <p class="text-h5 font-weight-bold">Order details</p>
    <br />
    <v-sheet color="rgba(234,89,255,0.13)">
      <v-table
        style="background-color: rgba(234, 89, 255, 0.13)"
        theme="rgba(234,89,255,0.13)"
      >
        <thead>
          <tr>
            <th class="text-left font-weight-bold">Product</th>
            <th class="text-left font-weight-bold">Total</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="product in products"
            :key="product.id"
            class="text-caption text-left font-weight-bold"
          >
            <td>
              {{ product.title + " x " + product.quantity }}
            </td>
            <td>
              {{ product.total }}
            </td>
          </tr>
          <tr>
            <td>
              <p class="text-caption text-left font-weight-bold">SUBTOTAL:</p>
            </td>
            <td>
              <p class="text-caption text-left font-weight-bold">
                &#8377;{{ orderDetail.subTotal }}
              </p>
            </td>
          </tr>

          <tr>
            <td>
              <p class="text-caption text-left font-weight-bold">SHIPPING:</p>
            </td>
            <td>
              <p class="text-caption text-left font-weight-bold">
                {{ orderDetail.shipping }}
              </p>
            </td>
          </tr>
          <tr v-if="orderDetail.payment_status == 'PAYMENT_SUCCESS'">
            <td>
              <p class="text-caption text-left font-weight-bold">
                PAYMENT METHOD:
              </p>
            </td>
            <td >
              <p class="text-caption text-left font-weight-bold">
                {{ orderDetail.paymentMethod }}
              </p>
            </td>
          </tr>
          <tr>
            <td>
              <p class="text-caption text-left font-weight-bold">Total</p>
            </td>
            <td>
              <p
                class="text-caption text-left font-weight-bold"
                style="display: inline"
              >
                &#8377;{{ orderDetail.total }}
              </p>
              <p
                class="text-left font-weight-bold"
                style="font-size: x-small !important; display: inline"
              >
                (INCLUEDS <strong> &#8377; {{ orderDetail.tax }}</strong> TAX)
              </p>
            </td>
          </tr>
        </tbody>
      </v-table>
    </v-sheet>
    <br />
    <v-btn
      @click="downloadInvoice()"
      size="large"
      variant="elevated"
      color="#0AA99D"
      v-if="orderDetail.payment_status == 'PAYMENT_SUCCESS'"
    >
      Download INVOICE</v-btn
    >
    <br />
    <br />
    <p class="text-h5 font-weight-bold" v-if="orderDetail.address">Shipping Addrees</p>

    <address class="text-subtitle-2 font-weight-bold" v-if="orderDetail.address">
      {{ orderDetail.address.first_name + " " + orderDetail.address.last_name }}
      <br />
      {{ orderDetail.address.address_line_one }}
      <br v-if="orderDetail.address.address_line_two" />
      {{ orderDetail.address.address_line_two }}
      <br v-if="orderDetail.address.address_line_three" />
      {{ orderDetail.address.address_line_three }}
      <br />
      {{ orderDetail.address.city + " , " + orderDetail.address.pincode }}
      <br />
      {{ orderDetail.address.state + " , " + orderDetail.address.country }}
    </address>
  </v-container>
</template>

<style scoped>
</style>