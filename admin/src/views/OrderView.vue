<script setup>
import { ref, defineAsyncComponent } from "vue";
import { downloadFile } from "../services/fileService";
import { getOrderHistory } from "../services/merchService";
import { useRouter } from "vue-router";

const Header = defineAsyncComponent(() => import("@/components/Header.vue"));

const router = useRouter();
const itemsPerPage = ref(10);

const headers = [
  {
    title: "Order Id",
    align: "start",
    key: "order_id",
  },
  { title: "Email", key: "user_name", align: "end" },
  { title: "Payment Status", key: "payment_status", align: "end" },
  { title: "Order Status", key: "order_status", align: "end", sortable: false },
  { title: "Created", key: "order_created", align: "end", sortable: false },
  {
    title: "Invoice",
    key: "invoice_url",
    sortable: false,
  },
  {
    title: "Detail",
    key: "detail",
    align: "end",
    sortable: false,
  },
];
let search = ref("");
let serverItems = ref([]);
let loading = ref(true);
let totalItems = ref(0);

const openWindow = (url) => {
  window.open(url);
};

const openDetail = (item) => {
  router.push({
    path: "orderDetail",
    query: {
      id: item.order_number,
      order_type: item.order_type,
    },
  });
};

const pagination = ref({
  page: 1,
  itemsPerPage: 10,
  sortBy: [],
  filterBy: "",
});

const selectedStatus = ref("ALL");

const handleListChange = () => {
  pagination.value.page = 1;
  pagination.value.filterBy = selectedStatus.value;
  loadItems();
};

const loadItems = async () => {
  loading.value = true;
  let key = "order_id";
  let order = "desc";

  if (pagination.value.sortBy.length) {
    key = pagination.value.sortBy[0].key;
    order = pagination.value.sortBy[0].order;
  }

  try {
    const data = await getOrderHistory({
      page: pagination.value.page,
      itemsPerPage: pagination.value.itemsPerPage,
      key: key,
      order: order,
      filterBy:
        pagination.value.filterBy !== "ALL" ? pagination.value.filterBy : "",
    });
    serverItems.value = data.items;
    totalItems.value = data.total;
  } catch (error) {
    console.error("Error in getting data", error);
  } finally {
    loading.value = false;
  }
};

const downloadOrder = async () => {
  try {
    const response = await downloadFile(
      `${import.meta.env.VITE_BASE_URL}/admin/order-report`
    );
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `order.csv`); // or the filename you want
    document.body.appendChild(link);
    link.click();
  } catch (error) {
    // TODO Handle Error
    console.error(error);
  }
};

const orderStatusOptions = [
  "ALL",
  "PAYMENT_SUCCESS",
  "CREATED",
  "PLACED",
  "PAYMENT_INIT",
  "PAYMENT_FAILED",
  "PAYMENT_DECLINED",
  "PAYMENT_ERROR",
];

const handleTableOptionsChange = (options) => {
  pagination.value.page = options.page;
  pagination.value.itemsPerPage = options.itemsPerPage;
  pagination.value.sortBy = options.sortBy;
  loadItems();
};
</script>

<template>
  <Header />
  <v-container>
    <v-btn @click="downloadOrder" type="elevated" color="#5865f2">
      Download</v-btn
    >
  </v-container>

  <v-card class="pa-2 ma-4">
    <v-select
      v-model="selectedStatus"
      :items="orderStatusOptions"
      label="Filter by Order Status"
      class="mt-4"
      @update:modelValue="handleListChange"
    />
    <v-data-table-server
      v-model:items-per-page="itemsPerPage"
      :headers="headers"
      :items-length="totalItems"
      :items="serverItems"
      :loading="loading"
      :search="search"
      item-value="name"
      @update:options="handleTableOptionsChange"
    >
      <template v-slot:[`item.invoice_url`]="{ value }">
        <v-btn @click="openWindow(value)" color="primary" v-if="value">
          Open
        </v-btn>
      </template>

      <template v-slot:[`item.detail`]="{ item }">
        <v-icon
          color="primary"
          class="mdi-information"
          @click="openDetail(item)"
        >
          Open
        </v-icon>
      </template>
    </v-data-table-server>
  </v-card>
</template>

<style scoped></style>
