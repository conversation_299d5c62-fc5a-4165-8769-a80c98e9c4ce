<script setup>
// Plugin Imports
import { ref, defineAsyncComponent } from "vue";
import { useRouter } from "vue-router";

// Local Components
const AlertBox = defineAsyncComponent(() =>
  import("@/components/ErrorAlert.vue")
);

const Logo = defineAsyncComponent(() => import("@/components/Logo.vue"));
// Local Imports
import { login, googleLoginAPI } from "../services/userService.js";
import {
  isValidUserNamePass,
  userNameRules,
  passwordRules,
} from "../helper/common";

// Variables
const router = useRouter();
let loginData = ref({
  userName: "",
  password: "",
});
let visible = ref(false);

let showAPIError = ref(false);
let errorMessage = ref("Something Went Wrong");

// Form Functions

const onSubmit = async () => {
  try {
    if (
      isValidUserNamePass(loginData.value.userName, loginData.value.password)
    ) {
      await login({
        userName: loginData.value.userName,
        password: loginData.value.password,
        client_id: 1,
      });
      router.push({
        path: "/",
      });
    }
  } catch (error) {
    showAPIError.value = true;
    console.error("On Submit", error);
    errorMessage = error?.message ? error.message : errorMessage;
    setTimeout(() => {
      showAPIError.value = false;
    }, "2000");
  }
};

const googleCallBack = async (response) => {
  try {
    await googleLoginAPI({
      token: response.credential,
      client_id: 1,
    });
    router.push({
      path: "/",
    });
  } catch (error) {
    showAPIError.value = true;
    console.error("On Google Login", error);
    errorMessage = error?.message ? error.message : errorMessage;
    setTimeout(() => {
      showAPIError.value = false;
    }, "2000");
  }
};
</script>

<template>
  <v-container>
    <v-row justify="center" align="center" style="height: 100vh">
      <v-col cols="12" xs="12" sm="8" md="5" lg="5">
        <v-card :elevation="24">
          <!-- Logo Starts-->
          <Logo />
          <!-- Logo Ends-->
          <v-container class="pa-5">
            <!-- Error Alert Box Starts-->
            <AlertBox v-if="showAPIError" :message="errorMessage" />
            <!-- Error Alert Box Ends-->

            <!-- Login Form Starts-->
            <v-card-text>
              <v-form
                validate-on="submit lazy"
                id="loginForm"
                ref="form"
                @submit.prevent="onSubmit"
              >
                <v-text-field
                  density="compact"
                  v-model="loginData.userName"
                  :rules="userNameRules"
                  autocomplete="email|tel-local"
                  placeholder="Email / Phone Number"
                  variant="outlined"
                ></v-text-field>
                <v-text-field
                  v-model="loginData.password"
                  :rules="[passwordRules.required, passwordRules.min]"
                  hint="At least 8 characters"
                  :append-inner-icon="visible ? 'mdi-eye-off' : 'mdi-eye'"
                  :type="visible ? 'text' : 'password'"
                  autocomplete="current-password"
                  density="compact"
                  placeholder="Password"
                  variant="outlined"
                  @click:append-inner="visible = !visible"
                ></v-text-field>

                <v-btn
                  block
                  density="comfortable"
                  rounded="lg"
                  class="mb-8 my-3"
                  color="blue"
                  size="large"
                  variant="elevated"
                  type="submit"
                >
                  Log In
                </v-btn>
              </v-form>
              <v-row>
                <v-col align-self="center">
                  <v-divider class="border-opacity-50" color="info"></v-divider>
                </v-col>
                <v-col>
                  <div class="text-subtitle-2 pa-2">Or Log In With</div>
                </v-col>
                <v-col align-self="center">
                  <v-divider class="border-opacity-50" color="info"></v-divider>
                </v-col>
              </v-row>
              <v-row justify="center">
                <GoogleLogin :callback="googleCallBack" prompt auto-login />
              </v-row>
            </v-card-text>
            <!-- Login Form Ends-->
          </v-container>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<style scoped>
</style>