<script setup>
import { ref, defineAsyncComponent } from "vue";
import { getCartHistory } from "../services/merchService";

const Header = defineAsyncComponent(() => import("@/components/Header.vue"));

const itemsPerPage = ref(10);

const headers = [
  {
    title: "Cart Id",
    align: "start",
    key: "cart_id",
  },
  { title: "Email", key: "user_name", align: "end" },
  { title: "Artist", key: "artist_name", align: "end" },
  { title: "Items", key: "total_items", align: "end", sortable: false },
  { title: "Last Updated", key: "cart_updated", align: "end", sortable: false }
];
let search = ref("");
let serverItems = ref([]);
let loading = ref(true);
let totalItems = ref(0);

const loadItems = async ({ page, itemsPerPage, sortBy }) => {
  loading.value = true;
  let key = "cart_id";
  let order = "desc";

  if (sortBy.length) {
    key = sortBy[0].key;
    order = sortBy[0].order;
  }
  try {
    let data = await getCartHistory({
      page,
      itemsPerPage,
      key: key,
      order: order,
    });
    serverItems.value = data.items;
    totalItems.value = data.total;
    loading.value = false;
  } catch (error) {
    console.error("ERror in getting data", error);
  }
};
</script>
<template>
  <Header />
  <v-card class="pa-2 ma-4">
    <v-data-table-server
      v-model:items-per-page="itemsPerPage"
      :headers="headers"
      :items-length="totalItems"
      :items="serverItems"
      :loading="loading"
      :search="search"
      item-value="name"
      @update:options="loadItems"
    >
    </v-data-table-server>
  </v-card>
</template>

<style scoped>
</style>