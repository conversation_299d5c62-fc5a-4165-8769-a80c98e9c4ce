<script setup>
import { ref } from "vue";

import { useRouter } from "vue-router";
import { getUserToken } from "../services/userService";
let drawer = ref(false);
let isMobile = ref(false);
const router = useRouter();


const redirect = (path) => {
  router.push({
    path: path,
  });
}

const onLogoClick = () => {
  router.push({
    path: "/",
  });
};

const onLoginlick = () => {
  router.push({
    path: "/login",
  });
};

const onLogoutClick = () => {
  localStorage.clear();
  router.push({
    path: "/login",
  });
};

const logo = "https://d2b4rv4q8lb0q0.cloudfront.net/assets/company/metastarlogo.png";
</script>
<template>
  <v-app-bar :elevation="2" color="transparent" height="100" style="border-bottom-color: rgba(197, 199, 200, 0.1)" class="bg-deep-purple">
    <template v-slot:prepend v-if="isMobile">
      <v-app-bar-nav-icon @click.stop="drawer = !drawer" color="white"></v-app-bar-nav-icon>
    </template>
    <v-container v-if="isMobile" style="display: flex; justify-content: center">
      <img :src="logo" alt="metastar logo" width="125" style="vertical-align: top" @click="onLogoClick" />
    </v-container>
    <v-container v-if="!isMobile" style="max-width: 1200px; display: flex; align-items: center">
      <img :src="logo" alt="metastar logo" width="165" style="vertical-align: top" @click="onLogoClick" />
      <v-spacer></v-spacer>
      <v-btn color="white" variant="flat" density="default" size="large" class="ma-2 pa-2" @click="onLogoutClick"
        v-if="getUserToken() == null || getUserToken() == ''">

        Log In
      </v-btn>
      <v-btn color="white" variant="flat" density="default" size="large" class="ma-2 pa-2" @click="onLoginlick" v-else>
        Log Out
      </v-btn>
    </v-container>
  </v-app-bar>
  <v-navigation-drawer
        class="bg-deep-purple"
        theme="dark"
        permanent
      >
        <v-list color="transparent">
          <v-list-item prepend-icon="mdi-account-group" title="Users" @click="redirect('/')"></v-list-item>
          <v-list-item prepend-icon="mdi-music" title="Karaoke" @click="redirect('/karaoke')"></v-list-item>
          <v-list-item prepend-icon="mdi-cart" title="Order" @click="redirect('/order')"></v-list-item>
          <v-list-item prepend-icon="mdi-cart" title="Cart" @click="redirect('/cart')"></v-list-item>
        </v-list>

        <template v-slot:append>
          <div class="pa-2">
            <v-btn block @click="onLogoutClick">
              Logout
            </v-btn>
          </div>
        </template>
      </v-navigation-drawer>
</template>

<style scoped>

</style>