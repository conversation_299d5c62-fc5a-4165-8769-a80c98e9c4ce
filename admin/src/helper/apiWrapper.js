import { getUserToken, removeUserToken } from "../services/userService";
import axios from "axios";

const BASE_URL = import.meta.env.VITE_BASE_URL;
const authHeader = () => {
    // return auth header with jwt if user is logged in and request is to the api url

    const token = getUserToken();
    if (token) {
        return { authorization: `${token}` };
    } else {
        return {};
    }
}

const handleResponse = (response) => {
    // TODO handle success
    if (response.status === 200) {
        console.log(response.data);
        return response;
    }
    // TODO Handle other exception
    else {
        return response;
    }
}

const handleError = (response) => {
    // TODO Handle Invalid token
    if ([401].includes(response?.response?.status)) {
        removeUserToken();
        return response;
    }
    // TODO Handle other exception
    else {
        return response;
    }
}

export const apiRequest = axios.create({
    baseURL: BASE_URL,
    // timeout: 1000,
    headers: {
    },
});
apiRequest.interceptors.response.use(handleResponse, handleError);

export const request = (method) => {
    return (url, body, header = {}) => {
        const requestOptions = {
            method,
            headers: {
                ...authHeader(),
                ...header
            },
            data: {}
        };
        if (body) {
            requestOptions.data = body;
        }
        return apiRequest(url, requestOptions);
    }
}
export const apiWrapper = {
    get: request('GET'),
    post: request('POST'),
    put: request('PUT'),
    delete: request('DELETE')
}