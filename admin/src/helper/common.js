export const isValidIndianMobileNumber = (number) => {
    const pattern = /^(?:(?:\+?91|0)\s?)?[6789]\d{9}$/;
    return pattern.test(number);
};

export const isValidEmail = (email) => {
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
}

export const isValidPassword = (password) => {
    const result = password.length >= 8;
    return result;
}

export const REGEX = {
    EMAIL: /^[\w\.-]+@[\w\.-]+\.\w+$/,
    MOBILE: new RegExp('^([0|+[0-9]{1,5})?([7-9][0-9]{9})$')
}

export const userNameType = (userName) => {
    return REGEX.MOBILE.test(userName);
}

// Form Rules
export const userNameRules = [
    (value) => {
        if (/^\d+$/.test(value)) {
            return isValidIndianMobileNumber(value) ? true : "Invalid Mobile Number";
        } else {
            return isValidEmail(value) ? true : "Invalid Email";
        }
    },
];

export const passwordRules = {
    required: (value) => !!value || "Required.",
    min: (v) => v.length >= 8 || "Min 8 characters",
    emailMatch: () => `The email and password you entered don't match`,
};

export const isValidUserNamePass = (userName, pass) => {
    if (
        (
            isValidEmail(userName) ||
            isValidIndianMobileNumber(userName)
        )
        &&
        isValidPassword(pass)
    ) {
        return true;
    } else {
        return false;
    }
}
